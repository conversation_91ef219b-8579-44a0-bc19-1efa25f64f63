# Анализ загрузки файлов в упрощенных договорах

## 📁 **Текущее состояние:**

Функционал загрузки файла "Сравнения цен" в `base_data.js` **уже реализован корректно** и работает по стандартной схеме проекта.

## 🔧 **Как работает загрузка файлов:**

### **1. Настройка поля в `base_data.js`:**
```javascript
reason: {
  label: "-Загрузить файл сравнения цен",
  type: "file",
  multiple: true,  // Поддержка множественных файлов
  required: true,
},
```

### **2. Автоматическая обработка в `simplified_body.js`:**

#### **Метод `onChangeProperty()` - обрабатывает загрузку файлов:**
```javascript
async onChangeProperty(field) {
  // Проверяет, что это поле типа 'file'
  if (!field || field.property.type != 'file') return;

  // Функция отправки файла на сервер
  var send = async (file) => {
    let formData = new FormData();
    formData.append('data', file, file.name);
    
    // Отправка на файловый сервер
    let { data, error } = await Http.upload.form('tender/attach', formData);
    
    if (error) return;

    // Возвращает данные файла с сервера
    return {
      id: data.uuid,           // ID файла на сервере
      name: data.meta.name,    // Имя файла
      meta: {
        "type": data.meta.type,
        "content_type": data.meta.content_type,
        "type_group": data.meta.group,
        "size": data.meta.size
      }
    }
  }

  // Обработка одиночного файла или массива файлов
  if (Array.isArray(value)) {
    // Множественные файлы
    for (let key in value) {
      let item = value[key];
      if (item && item.file && item.file.name) {
        let _value = await send(item.file);
        value[key] = _value;  // Заменяем файл на данные с сервера
      }
    }
  } else {
    // Одиночный файл
    if (item && item.file && item.file.name) {
      let _value = await send(item.file);
      value = field.property._value = _value;  // Сохраняем данные файла
    }
  }
}
```

### **3. Сохранение данных файла с договором:**

#### **Метод `build_action_params()` - подготавливает данные для отправки:**
```javascript
build_action_params(fields) {
  return fields.map((field) => {
    let value = field.value;  // Здесь уже данные файла с сервера
    
    // Для файлов value содержит: {id, name, meta}
    return { name: field.name, value: value }
  }).reduce((acc, field) => {
    acc[field.name] = field.value;  // reason: {id, name, meta}
    return acc;
  }, {});
}
```

#### **При сохранении договора:**
```javascript
// В методе save()
if (this.contract.rights?.set_contract_data) {
  actions.push({
    name: 'set_contract_data',
    params: this.build_action_params(this.properties.base_data.fields),
    // params будет содержать:
    // {
    //   reason: [
    //     {id: "uuid1", name: "file1.pdf", meta: {...}},
    //     {id: "uuid2", name: "file2.pdf", meta: {...}}
    //   ],
    //   // ... другие поля
    // }
  });
}
```

## 🔄 **Полный цикл работы с файлом:**

1. **Пользователь выбирает файл** → UI компонент `ui-file`
2. **Файл загружается** → `onChangeProperty()` → `Http.upload.form('tender/attach')`
3. **Файл сохраняется на файловом сервере** → возвращается `{id, name, meta}`
4. **Данные файла сохраняются в поле** → `field.property._value = {id, name, meta}`
5. **При сохранении договора** → `build_action_params()` → данные файла отправляются на сервер
6. **Сервер сохраняет ссылки на файлы** вместе с данными договора

## ✅ **Что работает корректно:**

- ✅ **Загрузка файлов на файловый сервер** через `Http.upload.form('tender/attach')`
- ✅ **Поддержка множественных файлов** (`multiple: true`)
- ✅ **Автоматическая обработка** через `onChangeProperty()`
- ✅ **Сохранение данных файлов** в поле формы
- ✅ **Передача данных файлов** при сохранении договора
- ✅ **Стандартная архитектура** как в других частях проекта

## 🔍 **Добавлено логирование для отладки:**

```javascript
// В onChangeProperty():
console.log('📁 Загрузка файла:', {fieldName, fieldType, value});
console.log('📤 Отправка файла на сервер:', {fileName, fileSize, fileType});
console.log('✅ Файл успешно загружен:', {id, name, meta});
console.log('💾 Файл сохранен:', field.property._value);

// В build_action_params():
console.log('📁 Поле файла "reason" в параметрах:', value);
console.log('📤 Параметры для отправки на сервер:', params);
```

## 🧪 **Для тестирования:**

1. **Откройте упрощенный договор**
2. **Перейдите в раздел "Основные данные"**
3. **Найдите поле "Загрузить файл сравнения цен"**
4. **Загрузите один или несколько файлов**
5. **Откройте консоль браузера** - должны появиться сообщения о загрузке
6. **Сохраните договор** - должны появиться сообщения о передаче данных файлов
7. **Проверьте, что файлы доступны** после перезагрузки страницы

## 🎉 **Заключение:**

Функционал загрузки файлов **уже работает корректно** по стандартной архитектуре проекта. Файлы:
- Загружаются на файловый сервер
- Данные файлов сохраняются с договором
- Поддерживается множественная загрузка
- Интегрировано с системой прав доступа

Никаких дополнительных изменений не требуется!
