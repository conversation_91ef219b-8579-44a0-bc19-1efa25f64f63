# Отладка проблемы с readonly полями

## 🐛 **Проблема:**
После последних изменений поля в упрощенных договорах стали недоступны для редактирования (readonly).

## 🔍 **Возможные причины:**

### 1. Права доступа не инициализированы
```javascript
// В index.js строка 14:
this.rights = undefined;

// В index.js строка 30:
await this.update_context({ ...this.context, ...data, rights: undefined })
```

### 2. Неправильная проверка прав в props
```javascript
// В base_data.js:
readonly: !contractBody.contract.rights?.set_contract_data,
readonly: !contractBody.contract.rights?.set_contragent_data,
```

### 3. Отсутствие вызова update_rights
Права доступа могут не загружаться с сервера.

## 🔧 **Исправления:**

### 1. Добавлены проверки на существование прав
```javascript
// Было:
if (this.contract.rights.set_contragent_data && ...)

// Стало:
if (this.contract.rights?.set_contragent_data && ...)
```

### 2. Добавлена отладочная информация
```javascript
// В simplified_body.js:
console.log('🔐 Права доступа в SimplifiedContractBody:', this.contract.rights);

// В base_data.js:
console.log('🔐 Проверка прав в base_data.js:', {
  'contractBody.contract.rights': contractBody.contract.rights,
  'set_contract_data': contractBody.contract.rights?.set_contract_data,
  'readonly_base_data': !contractBody.contract.rights?.set_contract_data
});
```

## 🧪 **Для тестирования:**

1. Откройте упрощенный договор в браузере
2. Откройте консоль разработчика (F12)
3. Найдите сообщения с 🔐
4. Проверьте значения прав доступа:
   - `contractBody.contract.rights` должно быть объектом, а не `undefined`
   - `set_contract_data` должно быть `true`
   - `set_contragent_data` должно быть `true`
   - `readonly_base_data` должно быть `false`

## 🎯 **Ожидаемые результаты:**

### ✅ Если права корректны:
```javascript
{
  contractBody.contract.rights: {
    set_contract_data: true,
    set_contragent_data: true,
    set_org_requisites: true,
    set_part_requisites: true,
    // ... другие права
  },
  set_contract_data: true,
  set_contragent_data: true,
  readonly_base_data: false,
  readonly_contragent_fields: false
}
```

### ❌ Если права не инициализированы:
```javascript
{
  contractBody.contract.rights: undefined,
  set_contract_data: undefined,
  set_contragent_data: undefined,
  readonly_base_data: true,  // ← ПРОБЛЕМА!
  readonly_contragent_fields: true  // ← ПРОБЛЕМА!
}
```

## 🚀 **Следующие шаги:**

1. **Если права `undefined`** - нужно исправить инициализацию прав в `index.js`
2. **Если права `false`** - проблема с бэкендом или правами пользователя
3. **Если права `true`** - проблема в логике проверки readonly

## 📝 **Временное решение:**
Если права не работают, можно временно отключить readonly:

```javascript
// В base_data.js:
readonly: false, // Временно отключаем readonly
// readonly: !contractBody.contract.rights?.set_contract_data,
```

**⚠️ ВНИМАНИЕ:** Это временное решение только для отладки!
