import { DataSource } from '@iac/data';
import { Language, Util } from '@iac/core';

export function getCompetitiveListProps(contractBody) {
  return {
    competitive_list: {
      group: "Конкурентный лист",
      type: "data-grid",
      readonly: !contractBody.contract.rights || !contractBody.contract.rights.set_graph_finance_data,
      label: "!grid",
      dataSource: new DataSource({
        actions: [
          {
            label: "add",
            btn_type: "success",
            handler: async () => {
              let item = await contractBody.edit_competitive_list();
              if (!item || item == 2)
                return;

              contractBody.properties.competitive_list.dataSource.push_item({
                status: 1,
                supplier_name: item.supplier_name,
                supplier_inn: item.supplier_inn,
                price: item.price,
                currency: item.currency,
                exchange_rate: item.exchange_rate,
                price_uzs: item.price_uzs
              })
            }
          },
          {
            label: "save_changes",
            hidden: () => {
              let items = contractBody.properties.competitive_list.dataSource.items;
              if (!items)
                return true;
              return items.filter((item) => {
                if ((item.status & 7) != 0)
                  return true
              }).length <= 0;
            },
            handler: async () => {
              await contractBody.save(["competitive_list"]);
            }
          }
        ],
        store: {
          data: contractBody._competitive_list.map((item) => {
            return { ...item, status: 0 }
          }),
          context: (context) => {

            Object.defineProperty(context, "bindClass", {
              configurable: true,
              enumerable: true,
              get: () => {

                if ((context.status & 4) != 0)
                  return "ui-alert ui-alert-danger";

                if ((context.status & 1) != 0)
                  return "ui-alert ui-alert-success";

                if ((context.status & 2) != 0)
                  return "ui-alert ui-alert-warning";

              },
            });

            context.actions = [
              {
                btn_type: "warning",
                icon: "edit",
                hidden: () => {
                  return ((context.status & 4) != 0)
                },
                handler: async () => {
                  let item = await contractBody.edit_competitive_list(context);
                  if (!item || item == 2)
                    return;

                  context.supplier_name = item.supplier_name;
                  context.supplier_inn = item.supplier_inn;
                  context.price = item.price;
                  context.currency = item.currency;
                  context.exchange_rate = item.exchange_rate;
                  context.price_uzs = item.price_uzs;

                  context.status |= 2;
                }
              },
              {
                btn_type: "danger",
                icon: "trash",
                hidden: () => {
                  return ((context.status & 4) != 0)
                },
                handler: () => {
                  context.status |= 4;
                }
              },
              {
                label: "Восстановить",
                btn_type: "danger",
                hidden: () => {
                  return !((context.status & 4) != 0)
                },
                handler: () => {
                  context.status &= ~4;
                }
              }
            ]

            return context
          }
        }
      }), attr: {
        action_name: Language.t("actions"),
        action_style: "min-width: 110px;text-align:left;",
        buttons: true,
        summary: false,
        columns: [
          {
            field: "supplier_name",
            label: "Наименование поставщика",
            style: "text-align: left; min-width: 200px;"
          },
          {
            field: "supplier_inn",
            label: "ИНН",
            style: "text-align: center; width: 120px;"
          },
          {
            field: "price",
            label: "Цена",
            style: "white-space: nowrap; text-align: right; width: 120px;",
            display: (value, item) => {
              return `${Util.Number(value, ' ', 2)} ${item.currency}`;
            }
          },
          {
            field: "exchange_rate",
            label: "Курс",
            style: "text-align: right; width: 100px;",
            hidden: (item) => item.currency === 'UZS',
            display: (value) => {
              return Util.Number(value, ' ', 2);
            }
          },
          {
            field: "price_uzs",
            label: "Цена в UZS",
            style: "white-space: nowrap; text-align: right; width: 140px;",
            display: (value) => {
              return `${Util.Number(value, ' ', 2)} UZS`;
            }
          }
        ]
      }
    }
  };
}
