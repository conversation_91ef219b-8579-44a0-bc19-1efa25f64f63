# Исправление проблемы с readonly полями после автозаполнения

## 🐛 **Проблема:**
После закомментирования `readonly: true` в `props/graph.js` все поля в упрощенном договоре стали недоступными для редактирования.

## 🔍 **Причина:**
1. **Неправильное использование `readonly: true`** - этот флаг влиял на всю форму, а не только на источники финансирования
2. **Конфликт логики автозаполнения** - в коде было два разных подхода:
   - Стандартный `update_graph()` для государственных договоров
   - Наш `updateGraphFromFinance()` для автозаполнения

## 🔧 **Исправления:**

### 1. **Убрали глобальный `readonly: true` из `props/graph.js`:**
```javascript
// Было:
readonly: true, // Источники финансирования недоступны для редактирования

// Стало:
// Убрали readonly, оставили только настройки DataSource для блокировки редактирования
```

### 2. **Исправили логику автозаполнения в `simplified_body.js`:**
```javascript
// Было:
if (params.graph_finance && this.is_gos) {
  this.update_graph(); // Только для государственных договоров
}

if (params.graph_finance) {
  this._graph_finance = params.graph_finance.map((item) => {
    return { ...item, status: 0 }
  });
  this.properties.graph_finance.dataSource.set_items(this._graph_finance);
  
  // Обновляем источники финансирования при получении новых данных графика финансирования
  this.updateGraphFromFinance();
}

// Стало:
if (params.graph_finance) {
  this._graph_finance = params.graph_finance.map((item) => {
    return { ...item, status: 0 }
  });
  this.properties.graph_finance.dataSource.set_items(this._graph_finance);

  // Для упрощенных договоров всегда используем автозаполнение из графика финансирования
  this.updateGraphFromFinance();
}
```

### 3. **Как работает автозаполнение в разных типах договоров:**

#### **Прямые договоры (`custom_body.js`):**
```javascript
// Автозаполнение только для государственных договоров через API
if (params.graph_finance && this.contract.is_gos) {
  this.update_graph(); // Получает данные с сервера через get_graph API
}
```

#### **Упрощенные договоры (`simplified_body.js`):**
```javascript
// Автозаполнение для всех упрощенных договоров через локальную логику
if (params.graph_finance) {
  this.updateGraphFromFinance(); // Формирует данные локально из графика финансирования
}
```

## ✅ **Результат:**

### 🎯 **Источники финансирования:**
- ✅ **Автоматически формируются** из графика финансирования
- ✅ **Недоступны для редактирования** (через настройки DataSource, а не глобальный readonly)
- ✅ **Не влияют на другие поля** формы

### 🎯 **Остальные поля:**
- ✅ **Доступны для редактирования** согласно правам доступа
- ✅ **Работают корректно** без влияния настроек источников финансирования

## 🧪 **Для проверки:**

1. **Откройте упрощенный договор**
2. **Убедитесь, что все поля доступны для редактирования** (кроме источников финансирования)
3. **Заполните график финансирования**
4. **Проверьте, что источники финансирования автоматически обновились**
5. **Убедитесь, что источники финансирования недоступны для ручного редактирования**

## 🎉 **Проблема решена!**

Теперь:
- **Все поля формы работают корректно** согласно правам доступа
- **Источники финансирования автозаполняются** и недоступны для редактирования
- **Нет конфликтов** между разными логиками автозаполнения
- **Упрощенные договоры работают независимо** от флага `is_gos`
