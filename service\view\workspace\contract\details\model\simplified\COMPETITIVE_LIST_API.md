# API для конкурентного листа упрощённых договоров

## Новое действие: `set_competitive_list_data`

### Описание
Действие для сохранения данных конкурентного листа в упрощённых договорах.

### Параметры запроса

```json
{
  "method": "contract_action",
  "params": {
    "number": "contract_number",
    "action": "set_competitive_list_data",
    "data": {
      "competitive_list": [
        {
          "supplier_name": "ООО Поставщик 1",
          "supplier_inn": "123456789",
          "price": 1000.00,
          "currency": "UZS",
          "exchange_rate": 1,
          "price_uzs": 1000.00
        },
        {
          "supplier_name": "ООО Поставщик 2", 
          "supplier_inn": "987654321",
          "price": 100.00,
          "currency": "USD",
          "exchange_rate": 12500,
          "price_uzs": 1250000.00
        },
        {
          "supplier_name": "ООО Поставщик 3",
          "supplier_inn": "555666777",
          "price": 90.00,
          "currency": "EUR", 
          "exchange_rate": 13500,
          "price_uzs": 1215000.00
        }
      ]
    }
  }
}
```

### Структура элемента конкурентного листа

| Поле | Тип | Обязательное | Описание |
|------|-----|--------------|----------|
| `supplier_name` | string | Да | Наименование поставщика |
| `supplier_inn` | string | Да | ИНН организации поставщика (9 цифр) |
| `price` | float | Да | Цена в указанной валюте |
| `currency` | string | Да | Код валюты (UZS, USD, EUR, RUB) |
| `exchange_rate` | float | Да | Курс пересчета к UZS |
| `price_uzs` | float | Да | Цена в сумах (автоматический расчет) |

### Валидация на бэкенде

#### Обязательные проверки:
1. **Минимальное количество**: не менее 3 поставщиков
2. **ИНН**: должен содержать ровно 9 цифр
3. **Цена**: должна быть больше 0
4. **Валюта**: должна быть из списка поддерживаемых
5. **Курс пересчета**: должен быть больше 0
6. **Цена в UZS**: должна соответствовать расчету `price * exchange_rate`

#### Пример валидации:
```javascript
// Проверка минимального количества
if (competitive_list.length < 3) {
  return error("Необходимо добавить не менее трех поставщиков в конкурентный лист");
}

// Проверка каждого поставщика
for (let supplier of competitive_list) {
  // ИНН
  if (!/^\d{9}$/.test(supplier.supplier_inn)) {
    return error("ИНН должен содержать 9 цифр");
  }
  
  // Цена
  if (supplier.price <= 0) {
    return error("Цена должна быть больше 0");
  }
  
  // Расчет цены в UZS
  let calculated_uzs = supplier.price * supplier.exchange_rate;
  if (Math.abs(calculated_uzs - supplier.price_uzs) > 0.01) {
    return error("Неверный расчет цены в UZS");
  }
}
```

### Ответ при успехе

```json
{
  "result": {
    "data": {
      "competitive_list": [
        // Массив сохраненных поставщиков с теми же полями
      ]
    }
  }
}
```

### Ответы при ошибках

#### Недостаточно поставщиков:
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Необходимо добавить не менее трех поставщиков в конкурентный лист"
  }
}
```

#### Неверный ИНН:
```json
{
  "error": {
    "code": "VALIDATION_ERROR", 
    "message": "ИНН должен содержать 9 цифр"
  }
}
```

#### Неверная цена:
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Цена должна быть больше 0"
  }
}
```

## Права доступа

### Новое право: `set_competitive_list_data`

Должно возвращаться в ответе метода `get_rights` для упрощённых договоров:

```json
{
  "result": {
    "data": {
      "set_contract_data": "grant",
      "set_contragent_data": "grant", 
      "set_competitive_list_data": "grant",
      "set_specification": "grant",
      // ... другие права
    }
  }
}
```

## Интеграция с публикацией договора

При публикации упрощённого договора (`publish` action) необходимо проверить:

1. **Наличие конкурентного листа** с минимум 3 поставщиками
2. **Валидность всех данных** конкурентного листа

Если конкурентный лист не заполнен или содержит менее 3 поставщиков, публикация должна быть заблокирована с соответствующим сообщением об ошибке.

## Получение данных

Конкурентный лист должен возвращаться в методе `contract_detailed`:

```json
{
  "result": {
    "data": {
      "number": "contract_number",
      "competitive_list": [
        // Массив поставщиков
      ],
      // ... другие данные договора
    }
  }
}
```

## Логирование

Все изменения конкурентного листа должны логироваться в историю договора с указанием:
- Пользователя, внесшего изменения
- Времени изменения  
- Типа операции (добавление, редактирование, удаление поставщика)
- Измененных данных
