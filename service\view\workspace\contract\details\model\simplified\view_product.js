import { Entity } from '@iac/data';
import { Language, Util } from '@iac/core';
import { Settings } from '@iac/kernel';

export default class SimplifiedContractViewProduct extends Entity {
  constructor(context) {
    super(context);

    this.is_gos = context.is_gos;
    this.proc_type = context.proc_type || 'simplified_contract';

    this.account = context.account;
    this.expense_item_code = context.expense;

    this.claim_id = context.claim_id;
    this.price = `${Util.Number(context.price, ' ', 2)} ${context.currency || Settings._default_currency}`;

    if (context.product?.unit) {
      this.amount = `${context.amount} ${context.product.unit}`;
    } else {
      this.amount = context.amount;
    }

    if (context.delivery_month) {
      this.delivery_month = Language.t(`month_${context.delivery_month}`);
    }
    this.delivery_year = context.delivery_year;

    this.conditions = context.conditions;
    this.delivery_address = context.delivery_address;
    this.country = context.country;
  }

  props() {
    return {
      account: {
        type: "static",
        label: "-account",
        hidden: !this.is_gos,
      },
      expense_item_code: {
        type: "static",
        label: "-expense_item_code",
        hidden: !this.is_gos,
      },
      amount: {
        type: "static",
        label: "-amount",
      },
      price: {
        type: "static",
        label: "-price",
      },
      delivery_month: {
        type: "static",
        label: "-contract.delivery_month",
        hidden: !this.is_gos,
      },
      delivery_year: {
        type: "static",
        label: "-contract.delivery_year",
        hidden: !this.is_gos,
      },
      country: {
        type: "static",
        label: "-country",
        dataSource: 'ref_country_',
        readonly: true,
      },
      conditions: {
        type: "static",
        label: "-conditions_characteristics",
      },
      delivery_address: {
        type: "static",
        label: "-delivery_address",
        hidden: this.is_gos,
      },
    }
  }
}
