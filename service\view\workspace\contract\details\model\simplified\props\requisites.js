import { Language } from '@iac/core';
import { Settings } from '@iac/kernel';

export function getRequisitesProps(contractBody) {
  return {
    requisites: {
      type: "model",
      group: "!company_bank",
      label: "!",
      fields: {
        org_banking_details: {
          group: "!company_bank/!requisites-/contract.requisites_org",
          type: 'entity',
          label: "requisites_title",
          description: "$requisites_title",
          dataSource: 'get_company_bank_accounts_struct',
          readonly: !contractBody.contract.rights?.set_org_requisites,
          has_del: true,
          required: contractBody.contract.rights?.set_org_requisites,
          attr: {
            details: [
              "bank_name",
              { label: Language.t([Settings._country + ".bank_mfo_code", "bank_mfo_code"]), field: "bank_mfo_code" },
              "bank_account"
            ]
          }
        },
        part_banking_details: {
          group: "!company_bank/!requisites/contract.requisites_contragent",
          type: 'entity',
          label: "requisites_title",
          description: "$requisites_title",
          dataSource: 'get_company_bank_accounts_struct',
          readonly: () => { return !contractBody.contract.rights?.set_part_requisites; },
          has_del: true,
          required: contractBody.contract.rights?.set_part_requisites,
          attr: {
            details: [
              "bank_name",
              { label: Language.t([Settings._country + ".bank_mfo_code", "bank_mfo_code"]), field: "bank_mfo_code" },
              "bank_account"
            ]
          }
        },
      }
    }
  };
}
