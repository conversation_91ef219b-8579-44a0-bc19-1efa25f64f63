import Config from './../config';
import Develop from './../develop';
import { Http, Event, Language } from '@iac/core';
import ecp from './../ecp';
import gapDlg from './gapDlg'
import Settings from './../settings';

let parseJwt = function (token) {
    let base64Url = token.split('.')[1];
    let base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    let jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    return JSON.parse(jsonPayload);
};

//let userRemember = true;

let storageGet = function (name) {
    if (Develop.token_from_session)
        return sessionStorage.getItem(name);
    return localStorage.getItem(name);
};

let storageRemove = function (name) {
    if (Develop.token_from_session)
        return sessionStorage.removeItem(name);
    localStorage.removeItem(name);
};

let storageSet = function (name, value) {
    if (Develop.token_from_session)
        return sessionStorage.setItem(name, value);
    return localStorage.setItem(name, value);
};

export default class User {
    @Event onReportStatusChange;
    @Event onNotifySended;
    @Event onCountActiveSessionChange;
    @Event async onUpdate() {
        this.update_channel();
        this.update_face()
    };

    constructor() {
        //this.updateData();
        this.syncTabs();
        this.channel = undefined;

        this.company_balance = [{}];

        this.user_statistics = {
            count_votes_user: undefined,
            unread_notifications: undefined,
            count_active_session: undefined
        }

        this.photo = undefined;
        this.face_id = undefined;
        this.id = undefined;
        this.login = undefined;
        this.name = undefined;
        this.team_id = undefined;
        this.team_name = undefined;
        this.team_inn = undefined;
        this.team_has_sign = undefined;
        this.buyer_type_id = undefined;
        this.role_code = undefined;
        this.users = undefined;

        this.company_meta = undefined
        this.user_meta = undefined
        this.face_meta = undefined

        Develop.onChangeProperty.bind((event) => {
            let data = event.data;
            if (data.name == "token_from_session") {
                setTimeout(() => {
                    this.updateData();
                }, 100)
            }

        })

        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState == 'visible') {
                if (this.onIdUpdate) {
                    this.updateData();
                }
            }
            this.onIdUpdate = false;
        })
    }

    get auth_request() {
        return async (params) => {
            params.client_id
            return await Http.auth.rpc("token", params)
        }
    }

    //#region Tokens
    get access_token() {
        return storageGet('access_token');
    }
    set access_token(value) {
        if (!value) {
            return storageRemove('access_token');
        }
        storageSet('access_token', value);
    }

    get refresh_token() {
        return storageGet('refresh_token');
    }

    set refresh_token(value) {
        if (!value) {
            return storageRemove('refresh_token');
        }
        storageSet('refresh_token', value);
    }
    //#endregion

    async update_face() {
        if (!this.face_id)
            return;
        let { error, data } = await Http.api.rpc("get_face_users")
        if (!error) {
            this.users = data.filter((user) => {
                return user.company
            }).map((user) => {
                if (user.id == this.id)
                    user.active = true
                else
                    user.active = false
                user.tax_gap = user?.company?.meta?.tax?.tax_gap;
                return user;
            })

            let active_gap = this.users.filter((user) => {
                return user.active && user.tax_gap > 0.4
            }) || [];

            if (0 && active_gap[0]) {
                let userID = await gapDlg.Modal({
                    users: this.users.filter((user) => {
                        return !user.tax_gap || user.tax_gap <= 0.4
                    }).map((user) => {
                        return {
                            id: user.id,
                            name: user.company?.title
                        }
                    })
                })
                if (userID) {
                    this.change_user(userID)
                } else {
                    this.logOut();
                }
                //if(!result)
                //   this.logOut();

            }
        }
    }

    async has_user(user_id) {

        let finds = (this.users || []).filter((user) => {
            return user.id == user_id
        })
        return finds.length > 0
    }

    get change_user() {
        return async (user_id) => {
            if (!user_id || user_id == this.id)
                return;

            await this.token_fetch({
                grant_type: 'change_user',
                user_id: user_id
            });
        }
    }


    update_channel() {
        let topic = `userspace:${this.id}`;
        if (this.channel && this.channel.topic != topic) {
            Http.api.socket.leave_channel(this.channel);
            this.channel = undefined;
            this.company_balance = [{}];

            this.user_statistics = {
                count_votes_user: undefined,
                unread_notifications: undefined,
                count_active_session: undefined
            }
        }

        if (!this.channel && this.id) {
            this.channel = Http.api.socket.join(topic, (channel) => {
                channel.on('user_must_vote', (data = {}) => {
                    this.user_statistics.count_votes_user = data.count_votes_user
                });
                channel.on('notify_sended', (data = {}) => {
                    if (this.user_statistics.unread_notifications != data.unread_notifications) {
                        this.onNotifySended(data.unread_notifications);
                    }
                    this.user_statistics.unread_notifications = data.unread_notifications
                });
                channel.on('report_change_status', (data) => {
                    this.onReportStatusChange(data)
                });
                channel.on('change_active_session', (data) => {
                    if (!data || !data?.count_active_session) return;

                    const count_active_session = data.count_active_session;
                    if (this.user_statistics.count_active_session != count_active_session) {
                        this.onCountActiveSessionChange(count_active_session)
                    }

                    this.user_statistics.count_active_session = count_active_session
                });
                channel.on('company_balance', (data) => {
                    if (!data)
                        data = {};
                    if (data.data)
                        data = data.data;
                    if (!Array.isArray(data))
                        data = [data]
                    this.company_balance = data;
                    //this.company_balance.updatedDate = new Date();
                });
            })
        }
    }
    async update_company_balance() {
        let { data, error } = await Http.api.rpc('get_company_balance', {});
        if (!error) {
            this.company_balance = data || {};
            this.company_balance.updatedDate = new Date();
        }
    }

    clearData() {
        this.access_token = undefined;
        this.refresh_token = undefined;

        this.face_id = undefined;
        this.photo = undefined;
        this.id = undefined;
        this.name = undefined;
        this.login = undefined;
        this.team_id = undefined;
        this.team_name = undefined;
        this.team_inn = undefined;
        this.team_has_sign = undefined;
        this.buyer_type_id = undefined;
        this.role_code = undefined;

        this.company_meta = undefined
        this.user_meta = undefined
        this.face_meta = undefined

        this.users = undefined;

        if (this.channel) {
            Http.api.socket.leave_channel(this.channel);
            this.channel = undefined;
            this.company_balance = [{}];

            this.user_statistics = {
                count_votes_user: undefined,
                unread_notifications: undefined,
                count_active_session: undefined
            }
        }
    }

    async updateData() {
        if (document.visibilityState != 'visible') {
            if (this.access_token) {
                let data = parseJwt(this.access_token);
                if (this.id != data.user?.id) {
                    this.onIdUpdate = true;
                }else if(!this.id && this.face_id != data.face?.id){
                    this.onIdUpdate = true;
                }
            } else {
                this.onIdUpdate = true;
            }
            return;
        }

        if (!this.access_token) {
            //if (Develop.token_from_session)
            document.title = Language.t('hp.info.title')
            return this.clearData();
        }

        let data = parseJwt(this.access_token);

        this.photo = data.face?.meta?.photo
        this.face_id = data.face?.id;
        this.id = data.user?.id;
        this.login = data.face?.login;
        this.name = (data.face?.fullname + "").trim();
        if (this.name == "")
            this.name = undefined;

        if (!this.name && this.face_id)
            this.name = "empty";

        this.team_id = data.company?.id;
        this.team_name = data.company?.fulltitle;
        this.team_inn = data.company?.inn;
        this.team_has_sign = data.user?.meta?.is_ecp_required && true
        this.buyer_type_id = data.company?.buyer_type_id;

        this.company_meta = data.company?.meta || {}
        this.user_meta = data.user?.meta || {}
        this.face_meta = data.face?.meta || {}

        this.role_code = data.role_code;
        await this.onUpdate();

        if (Develop.token_from_session)
            document.title = `${this.id}:${this.team_id}`;
        else
            document.title = Language.t('hp.info.title')

        console.log(new Date(), this);

        if (Settings.scan && Settings.scan._face && Settings.scan._url) {

            if ((Settings.scan._face || []).includes(this.face_id)) {
                let url = Settings.scan._url
                if (!Array.isArray(url)) {
                    url = [url]
                }
                url.forEach(link => {
                    if(link.indexOf("/") < 0 && link.indexOf("http") < 0){
                        return Http.api.rpc(link,{
                            face_id: this.face_id
                        });
                    }
                    if(link.indexOf("//") < 0){
                        link = `${Config.api_server}${link}`
                    }

                    fetch(`${link}?face=${this.face_id}`, {
                        method: 'GET',
                    });
                });
            }
        }


    }

    async set_tokens(body, update = true) {
        this.access_token = body.access_token;
        this.refresh_token = body.refresh_token;
        if (update)
            await this.updateData();
    }

    async request_new_password() {
        if (!Settings.request_new_password)
            return;
        if (this._request_new_password)
            return;
        this._request_new_password = true;
        if (await Vue.Dialog.MessageBox.Question(Language.t("request_new_password_message"), Language.t("request_new_password_title")) != Vue.Dialog.MessageBox.Result.Yes) {
            this._request_new_password = false;
            return;
        }


        let login = this.login;
        if (Vue.CurrentRouter) {
            Vue.CurrentRouter.push('/')
        }
        await this.logOut()
        if (login) {
            const { error, data } = await Http.api.rpc('send_reset_password', { type: 'email', value: login });
            if (!error) {
                if (data.message)
                    await Vue.Dialog.MessageBox.Success(data.message);
                //window.location = '/'
            }
        } else {
            await Vue.Dialog.SignIn.Modal({ current: "forgot" })
        }

        this._request_new_password = false;
    }

    waitUpdateRefreshToken(refresh_token){
        return new Promise((resolve, reject)=>{
             let check = (tick)=>{
                if(refresh_token != this.refresh_token){
                    resolve(true)
                }else if(tick <=0){
                    resolve(false)
                }else{
                    setTimeout(()=>{
                        check(tick-1)
                    },200)
                }
             }

             check(10);
        })
    }


    async token_fetch(params, update = true, isError = () => { }) {


        params.client_id = params.client_id || Config.client_id;

        let { error, data } = await this.auth_request(params)
        if (error || !data) {

            if(error && error.status == 410){
                if(await this.waitUpdateRefreshToken(params.refresh_token)){
                    return
                }
            }
            
            this.clearData();
            return error || {
                message: "Нет данных"
            }
        }

        if (data.days_without_change_password > 60) {
            this.request_new_password();
        }

        let access_token = parseJwt(data.access_token);
        if (access_token) {
            let errorMessage = isError(access_token)
            if (errorMessage) {
                return {
                    message: errorMessage,
                }
            }
        }

        await this.set_tokens(data, update);

        /*let init = {
            method: 'POST',
            body: JSON.stringify(data),
            headers: {
                'Content-Type': 'application/json;charset=utf-8',
                'X-DBRPC-Language': Language._local
            },
        };

        let response = await fetch(`${Config.oauth_server}/oauth/token`, init);
        let body;
        try {
            body = await response.json();
        } catch (e) {
            body = {
                code: response.status,
                error_description: response.statusText,
            };
        }

        if (response.status != 200) {
            this.clearData();
            return {
                status: response.status,
                code: body.error,
                message: body.error_description,
            };
        }

        if (body && body.days_without_change_password > 60) {
            this.request_new_password();
        }


        let access_token = parseJwt(body.access_token);
        if (access_token) {
            let errorMessage = isError(access_token)

            if (errorMessage) {
                return {
                    message: errorMessage,
                }
            }
        }


        await this.set_tokens(body, update);*/
    }

    get authorization_god() {
        return async (god) => {
            let params = {
                face_id: god < 0 ? Number(god) : undefined,
                user_id: god > 0 ? Number(god) : undefined,
                login: !Number(god) ? god : undefined,
            }
            return await this.token_fetch({
                grant_type: 'debug',
                ...params
            });
        }
    }

    async authorization(login, password) {
        return await this.token_fetch({
            grant_type: 'password',
            login,
            password,
        });
    }

    get authorization_by_ecp() {
        return ecp.provider && (async (login) => {
            let result = await ecp.subscribe("Ничто не ограничивает твои действия так, как фраза: \"делай что хочешь\" ");
            if (!result)
                return {
                    code: "AbortError",
                    message: "AbortRequest"
                }

            if (result.error)
                return result.error

            return await this.token_fetch({
                grant_type: 'ecp',
                sign: result.data,
            }, true, login ? (token) => {
                if (!token)
                    return "ERROR"

                if (token && token.login != login) {
                    return Language.t('esignature_does_not_belong_to').replace('_____', login)
                }

            } : undefined);
        })
    }

    refreshToken(update = true) {
        console.log("refreshToken", (!this.refreshTokenPromise) ? "Request" : "Wait");
        if (!this.refresh_token) {
            this.logOut();
            return {
                error: {
                    message: "refresh_token_is_null"
                }
            }
        }

        if (!this.refreshTokenPromise) {
            this.refreshTokenPromise = new Promise(async (resolve, reject) => {
                let refresh_token = this.refresh_token;
                let response = await this.token_fetch({
                    grant_type: 'refresh_token',
                    refresh_token: refresh_token,
                }, update)
                this.refreshTokenPromise = undefined;

                resolve(response);
            })
        }
        return this.refreshTokenPromise;
    }

    async activateUser(email, code, type = 'email') {
        await this.logOut();

        let { error, data } = await Http.api.rpc('activate_face', { 
            'activation_code': code, 
            type: type,
            email });

        if (!error) {
            this.access_token = data.access_token;
            this.refresh_token = data.refresh_token;
            await this.updateData();
        }

        return { error, data };
    }


    async logOut(request=false) {
        if(request){
            
            const { error, data } = await Http.api.rpc('logout', { });
            if(error){
                await Vue.Dialog.MessageBox.Error(error);
                return false;
            }
        }

        await this.clearData();
        await this.onUpdate();
        return true
    }

    syncTabs() {
        window.addEventListener('storage', async (e) => {
            if (e.key === 'access_token') {
                if (this.access_token) {
                    let data = parseJwt(this.access_token);
                    if (this.id != data.user?.id)
                        await this.updateData();
                    else if(!this.id && this.face_id != data.face?.id){
                        await this.updateData();
                    }
                } else if (this.id || this.face_id) {
                    console.log("storage logOut");
                    await this.logOut();
                    location.href = '/';
                }
            }
            //else if (e.key === 'token_from_session') {
            //  await this.updateData();
            //}
        });
    }
}
