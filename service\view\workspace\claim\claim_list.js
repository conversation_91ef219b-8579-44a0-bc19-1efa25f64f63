import { DataSource, RefStore, Query, ArrayStore, Entity } from '@iac/data';
import Router from '../../../router';
import { Action, Http, Language } from '@iac/core'
import { Context, Develop, Settings } from '@iac/kernel'
import DetailsDlg from './_dlg_details'

class EditClaimModel extends Entity {
    constructor(context) {
        super(context)

        this.id = context.id;
        this.address = context.address || context.adress;
        this.conditions = context.conditions;
        this.expense = context.expense;
        this.finyear = context.finyear;
        this.kls = context.kls ? {
            bank_account: context.kls
        } : undefined;
        this.month = context.month;
        this.product_properties = context.properties;
        this.srok = context.srok;
        this.tovar = context.tovar;
        this.tovaramount = context.tovaramount;
        this.tovarprice = context.tovarprice;
        this.tovarname = context.tovarname;

        this.unit = context.unit;
        this.product_type = context.type;

    }

    props() {
        let $this = this;
        return {
            id: {
                type: "hidden",
            },
            tovar: {
                type: "hidden",
            },
            product_properties: {
                type: "hidden"
            },
            conditions: {
                type: "text",
                label: 'conditions_characteristics',
                required: true,
                //readonly: this.id != undefined
            },
            tovaramount: {
                label: "-contract.amount",
                type: "float",
                suffix: this.unit,
                required: true,
            },
            tovarprice: {
                label: "-price_unit",
                type: "float",
                suffix: Settings._default_currency,
                required: true,
            },

            expense: {
                label: '-expense_item_code',
                type: "entity",
                dataSource: "ref_expense_item",
                required: true,
                //readonly: this.id != undefined
            },
            kls: {
                type: 'entity',
                label: "-kls",
                required: true,
                dataSource: {
                    search: "search",
                    valueExp: 'bank_account',
                    displayExp: 'bank_account',
                    query: { search: {},state_accounts:{
                        value: true
                    } },
                    store: {
                        key: 'bank_account',
                        method: 'get_company_bank_accounts',
                    },
                   //template: DataSource.get("get_company_bank_accounts").template
                },
                readonly: this.id != undefined
            },
            finyear: {
                group: '<fin>',
                label: '!year',
                max: 2100,
                type: "number",
                required: true,
                attr: {
                    style: "max-width: 210px;"
                },
                //readonly: this.id != undefined
            },
            month: {
                group: '<fin>',
                type: "entity",
                label: "!select_month",
                dataSource: "ref_months",
                required: true,
                has_del: true,
                //readonly: this.id != undefined
            },
            srok: {
                label: "-delivery_time",
                type: "number",
                suffix: "дн.",
                required: true,
                //readonly: this.id != undefined
            },
            address: {
                type: "text",
                label: 'delivery_address',
                required: true,
                //readonly: this.id != undefined
            },
        }
    }

    async save() {
        let fields = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).reduce((prev, curr) => {
            //if (curr.readonly)
            //    return prev
            prev[curr.name] = curr.value && curr.value.exp ? curr.value.exp.value : curr.value
            return prev
        }, {})

        fields.properties = fields.product_properties.map((prop) => {
            return {
                prop_name: prop.prop_name,
                prop_numb: prop.prop_numb,
                val_name: prop.val_name,
                val_numb: prop.val_numb != undefined ? prop.val_numb : prop.val_number
            }
        });
        fields.product_properties = undefined;
        delete fields.product_properties;

        let { error, data } = await Http.api.rpc("upsert_isugf_goods", fields)

        if (error) {
            if (!this.setError(error)) {
                Vue.Dialog.MessageBox.Error(error);
            }
            //return;
        } else {
            if (data && data.message) {
                Vue.Dialog.MessageBox.Success(data.message);
            }
            return data
            //return false;
        }

    }
}

var EditClaimDlg = Vue.Dialog({
    props: ['context'],
    data: function () {
        return {
            model: new EditClaimModel(this.context)
        }
    },
    methods: {
        create() {
            this.$wait(async () => {
                let data = await this.model.save();
                if (data)
                    this.Close(data)
            })
        }
    },
    template: `
<div>
    <header>{{model.tovarname}}</header>
    <main>
        <iac-layout-static :value='model.product_properties' />
        <ui-layout :fields='model.fields'/>
    </main>
    <footer>
        <ui-btn type='secondary' v-on:click.native='Close()'>{{$t("close")}}</ui-btn>
        <ui-btn type='primary' v-on:click.native='create'>{{$t(model.id ? "save" : "create")}}</ui-btn>
    </footer>
</div>
    `
})

var mountDlg = Vue.Dialog({
    props: ["items"],
    data: function () {
        return {
            source: new DataSource({
                displayExp: 'tovarname',
                limit: 100,
                store: new ArrayStore({
                    data: this.items,
                    inject: async (ids) => {
                        let items = [];
                        const chunkSize = 100;
                        for (let i = 0; i < ids.length; i += chunkSize) {
                            const chunk = ids.slice(i, i + chunkSize);

                            let { error, data } = await Http.api.rpc("ref_schedule", {
                                ref: 'schedule_isugf_goods',
                                op: 'read',
                                limit: chunk.length,
                                filters: {
                                    id: chunk
                                }
                            })
                            if (!error && data) {

                                items = items.concat(
                                    data.map((context) => {
                                        context.pos_type = undefined
                                        context.amount = context.tovaramount;
                                        context.status = undefined
                                        return context
                                    }));
                            }
                        }
                        return items;
                    }
                }),
                template: {
                    props: ['model'],
                    data: function () {
                        this.model.pos_type = this.model.position_type;
                        return {
                            source: new DataSource({
                                store: new ArrayStore({
                                    data: [{ id: "good", name: "doc_type_1" }, { id: "work", name: "doc_type_4" }, { id: "serv", name: "doc_type_2" }]
                                })
                            })
                        }
                    },
                    methods: {
                        onAmount(amount) {
                            if (amount > this.model.tovaramount) {
                                this.model.status = {
                                    type: 'error',
                                    message: Language.t('limit_exceeded')
                                }
                            } else {
                                this.model.status = undefined
                            }
                        }
                    },
                    computed: {
                        tovaramount() {
                            return `${this.model.tovaramount} ${this.model.unit}`
                        }
                    },
                    template: `
                        <div style='margin-top: 24px; border-bottom: 1px solid #ddd'>
                            <div style='color: #222;  margin-bottom: 8px;'>{{model.index}}. {{model.tovarname}}</div>

                            <div>

                                <div class='ui-field'>
                                        <div class='field-label'>
                                            <div  class='label-wrapper'>
                                                <label>
                                                    <span>{{$t('doc_type')}}:</span>
                                                    <span class="color-red"> (*)</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class='field-container'>
                                            <div class='field-control'>
                                            <ui-entity :readonly='model.position_type' label="doc_type" :dataSource='source' v-model='model.pos_type' />
                                            </div>
                                        </div>
                                    </div>

                                <ui-control-group>
                                    <div class='ui-field'>
                                        <div class='field-label'>
                                            <div  class='label-wrapper'>
                                                <label>
                                                    <span>{{$t('user.order_limit')}}:</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class='field-container'>
                                            <div class='field-control'>
                                                <ui-input readonly :value='tovaramount' />
                                            </div>
                                        </div>
                                    </div>
                                    <div class='ui-field'>
                                        <div class='field-label'>
                                            <div  class='label-wrapper'>
                                                <label>
                                                    <span>{{$t('quantity')}}:</span>
                                                    <span class="color-red"> (*)</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class='field-container'>
                                            <div class='field-control'>

                                                <ui-input label='amount' type='float' v-model='model.amount' v-on:input='onAmount' :status='model.status' />

                                            </div>
                                        </div>
                                    </div>
                                </ui-control-group>
                            </div>
                        </div>
                    `
                }
            })
        }
    },
    computed: {
        errors() {
            return this.source.items.filter((item) => {
                if (!item.pos_type)
                    return true
                if (!item.amount)
                    return true
                return item.status
            })
        }
    },
    methods: {
        create() {
            this.Close(this.source.items.map((item) => {
                return {
                    id: item.id,
                    amount: Number(item.amount),
                    pos_type: item.pos_type?.id || item.pos_type,
                    type: "claim"
                }
            }))
        }
    },
    template: `
        <div>
            <header></header>
            <main>
                <ui-list raw :dataSource='source'>
                    <component  slot='item' slot-scope='props' v-if='source.template' :is='source.template' :model='props.item' />
                </ui-list>
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
                <ui-btn type='primary' :disabled='errors && errors.length > 0' v-on:click.native='create'>{{$t('create_procedure')}}</ui-btn>
            </footer>
        </div>
    `
})

export default {
    data() {
        return {
            claims: new DataSource({
                template: "template-claim",
                query: new Query({
                    year: {
                        type: "entity",
                        group: "year",
                        label: "!current_year",
                        dataSource: new DataSource({
                            store: new ArrayStore({
                                keyType: 'number',
                                data: async () => {
                                    let { error, data } = await Http.api.rpc("ref_schedule", {
                                        'ref': 'schedule_isugf_goods',
                                        'op': 'get_years'
                                    })
                                    if (!error && data && Array.isArray(data)) {
                                        return data.sort().map((year) => {
                                            return {
                                                id: year,
                                                name: year,
                                            }
                                        })
                                    }
                                    return []
                                }
                            })
                        }),
                        has_del: true,
                    },
                    month: {
                        type: "entity",
                        group: "month",
                        label: "!select_month",
                        dataSource: DataSource.get("ref_months"),
                        has_del: true,
                    },
                }),
                actions: [
                    {
                        label: "create_claim",
                        btn_type: "primary",
                        hidden: () => {
                            return !Context.Access.policy["isugf_schedule_create"]
                        },
                        handler: async () => {
                            let product = await Vue.Dialog.SelectProduct.Modal({
                                size: 'right'
                            })

                            if (product) {
                                let claim = await EditClaimDlg.Modal({
                                    context: {
                                        adress: undefined,
                                        conditions: undefined,
                                        expense: undefined,
                                        finyear: undefined,
                                        kls: undefined,
                                        month: undefined,
                                        properties: product.product_properties,
                                        srok: undefined,
                                        tovar: product.product_id,
                                        tovaramount: undefined,
                                        tovarprice: undefined,
                                        tovarname: product.product_name,
                                        unit: product.unit,
                                        type: product.type,

                                    },
                                    //size: "lg"
                                })
                                if (claim) {
                                    this.claims.reload();
                                }
                            }
                        }
                    },
////////////////////////////////////////////////////////////////////////////////////////////
                    {
                        label: "create_procedure",
                        hidden: () => {
                            if (!Context.Access.policy["tender_create"] && !Context.Access.policy["reduction_create"])
                                return true;
                            if (this.claims.checkedItems && this.claims.checkedItems.length > 0)
                                return false;
                            return true
                        },
                        handler: async () => {

                            let source = await mountDlg.Modal({
                                items: this.claims.checkedItems
                            });

                            if (!source)
                                return;

                            let procedure_params = {
                                source: source
                                //type: context.proc_type_code
                            }

                            this.$wait(async () => {
                                let response = await Action["procedure.create"](procedure_params);

                                if (!response)
                                    return;
                                let { error, data } = response;
                                if (!error) {
                                    Router.push({ path: `/procedure/${data[0].proc_id}/core` });
                                }
                            })

                        }
                    },
///////////////////////////////////////////////////////////////////////////////////////////
                    {
                        label: 'Прямой договор',
                        hidden: () => {
                            if (!Context.Access.policy["contract_create"])
                                return true;

                            if (!Context.Access.develop["custom_contract_develop"])
                                return true;

                            if (this.claims.checkedItems && this.claims.checkedItems.length > 0)
                                return false;
                            return true
                        },
                        handler: async () => {
                            let source = await mountDlg.Modal({
                                items: this.claims.checkedItems
                            });

                            if (!source)
                                return;

                            let procedure_params = {
                                source: source
                            }

                            this.$wait(async () => {
                                let response = await Action["contract.create"](procedure_params);
                                if (!response)
                                    return;
                                let { error, data: id } = response;
                                if (error) {
                                    Vue.Dialog.MessageBox.Error(error)
                                }else if(id){
                                    this.$router.push({ path: `/workspace/contract/${id}/core` });
                                }
                            });
                        }
                    },
                    {
                        label: 'Договор упрощённой закупки',
                        hidden: () => {
                            if (!Context.Access.policy["contract_create"])
                                return true;

                            if (!Context.Access.develop["custom_contract_develop"])
                                return true;

                            if (this.claims.checkedItems && this.claims.checkedItems.length > 0)
                                return false;
                            return true
                        },
                        handler: async () => {
                            let source = await mountDlg.Modal({
                                items: this.claims.checkedItems
                            });

                            if (!source)
                                return;

                            // Проверяем лимиты БРВ перед созданием договора
                            const totalSum = source.reduce((sum, item) => sum + (item.summa || 0), 0);
                            const isGosCustomer = Context.User.buyer_type_id == 1;
                            const brvLimit = isGosCustomer ? 25 : 50;

                            if (totalSum > brvLimit) {
                                const customerType = isGosCustomer ? "бюджетных" : "корпоративных";
                                const message = `Вы не можете создать договор упрощенной закупки, так как общая сумма превышает ${brvLimit} БРВ для ${customerType} заказчиков.`;
                                Vue.Dialog.MessageBox.Error(message);
                                return;
                            }

                            let procedure_params = {
                                source: source,
                                contract_type: 'simplified_contract',
                                buyer_type_id: Context.User.buyer_type_id
                            }

                            this.$wait(async () => {
                                let response = await Action["contract.create"](procedure_params);
                                if (!response)
                                    return;
                                let { error, data: id } = response;
                                if (error) {
                                    Vue.Dialog.MessageBox.Error(error)
                                }else if(id){
                                    this.$router.push({ path: `/workspace/contract/${id}/core` });
                                }
                            });
                        }
                    },
///////////////////////////////////////////////////////////////////////////////////////////////
                    {
                        label: "balance.refresh",
                        btn_type: "primary empty",
                        handler: async () => {
                            await this.reload_isugf_goods();
                        }
                    }
                ],
                store: new RefStore({
                    key: 'id',
                    method: 'ref_schedule',
                    ref: 'schedule_isugf_goods',
                    inject: (items) => {
                        if (!items)
                            return

                        let expense_codes = {}
                        items.forEach((item) => {
                            expense_codes[item.expense] = expense_codes[item.expense] || { items: [] }
                            expense_codes[item.expense].items.push(item)
                        })

                        DataSource.get("ref_expense_item").byKeys(Object.keys(expense_codes)).then((expenseList) => {
                            expenseList.forEach((expense) => {
                                let items = expense_codes[expense.code] && expense_codes[expense.code].items;
                                if (items) {
                                    items.forEach((item) => {
                                        item.expense = {
                                            code: expense.code,
                                            name: expense.name,
                                            display: `${expense.code}: ${expense.name}`
                                        }
                                    })
                                }
                            })
                        })

                        return items
                    },
                    context: context => {
                        context.price = context.tovarprice
                        context.total_sum = context.summa

                        if (context.total_sum && !context.proc_id) {
                            context.checkbox = true
                        }

                        Object.defineProperty(context, "status_type", {
                            configurable: true,
                            enumerable: true,
                            get: () => {
                                if (context.tovaramount <= 0) {
                                    return 'danger'
                                }
                                if (context.in_update > 0) {
                                    return 'warning'
                                }
                            },
                        });



                        context.actions = [
                            {
                                label: 'details',
                                hidden: true,
                                handler: async () => {
                                    await DetailsDlg.Modal({ item: context });
                                }
                            },
                            {
                                label: 'edit',
                                hidden: () => {
                                    return context.type != 'internal' || !Context.Access.policy["claim_edit"]
                                },
                                handler: async () => {

                                    let claim = await EditClaimDlg.Modal({ context: context });
                                    if (claim) {
                                        context.finyear = claim.finyear
                                        context.month = claim.month
                                        context.expense = claim.expense

                                        context.tovaramount = claim.tovaramount
                                        context.price = claim.tovarprice
                                        context.tovarprice = claim.tovarprice
                                        context.total_sum = claim.summa

                                        context.conditions = claim.conditions
                                        //context.address = claim.address
                                        context.adress = claim.adress
                                        context.srok = claim.srok

                                    }
                                }
                            },
                            {
                                label: "balance.refresh",
                                handler: async () => {
                                    await this.reload_isugf_goods(context.id);
                                },
                                hidden: () => {
                                    return context.type == 'internal'
                                },
                            },
                            {
                                get label() {
                                    return `create_procedure`
                                },
                                hidden: () => {
                                    if (!Context.Access.policy["tender_create"] && !Context.Access.policy["reduction_create"])
                                        return true;
                                    if (context.tovaramount <= 0)
                                        return true;
                                    return context.proc_id ? true : false
                                },
                                handler: async () => {
                                    let source = await mountDlg.Modal({
                                        items: [context.id]
                                    });
                                    if (!source)
                                        return;

                                    let procedure_params = {
                                        source: source
                                    }

                                    let response = await Action["procedure.create"](procedure_params);

                                    if (!response)
                                        return;
                                    let { error, data } = response;
                                    if (!error) {
                                        Router.push({ path: `/procedure/${data[0].proc_id}/core` });
                                    }

                                }
                            },
                            {
                                get label() {
                                    return `contract.create`
                                },
                                hidden: () => {
                                    if (!Context.Access.policy["contract_create"]) return true;
                                    if (!Context.Access.develop["custom_contract_develop"]) return true;
                                    if (context.tovaramount <= 0) return true;
                                    return context.proc_id ? true : false
                                },
                                handler: async () => {
                                    let source = await mountDlg.Modal({
                                        items: [context.id]
                                    });
                                    if (!source) return;

                                    let procedure_params = {
                                        source: source
                                    }

                                    let response = await Action["contract.create"](procedure_params);
                                    if (!response) return;
                                    let { error, data: id } = response;
                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error)
                                    }else if(id){
                                        this.$router.push({ path: `/workspace/contract/${id}/core` });
                                    }
                                }
                            },
                        ]

                        return context;
                    }
                })
            })
        }
    },
    methods: {
        async reload_isugf_goods(id) {
            this.$wait(async () => {
                let { error, data } = await Http.api.rpc("reload_isugf_goods", {
                    id: id
                })

                if (error) {
                    Vue.Dialog.MessageBox.Error(error);
                } else {
                    if (data && data.message)
                        Vue.Dialog.MessageBox.Success(data.message);

                    this.claims.items.filter(item => {
                        if (item.type == 'internal')
                            return false;
                        return !id || (id == item.id)
                    }).forEach(item => {
                        item.in_update = item.in_update + 1;
                    });
                }
            })
        }
    },

    template: `
    <iac-access :access='$policy.isugf_schedule_list || $policy.isugf_schedule_read || $policy.isugf_schedule_create || $policy.isugf_schedule_sign'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.claim')}}</li>
            </ol>
            <h1>{{$t('nav.claim')}}</h1>
        </iac-section>
        <iac-section>
            <ui-alert v-if='!$route.query.year && $develop.purchase_develop' type='warning'>{{$t("purchase_year_warning")}}</ui-alert>
                <ui-data-view :dataSource='claims'/>
        </iac-section>
    </iac-access>
    `
}
