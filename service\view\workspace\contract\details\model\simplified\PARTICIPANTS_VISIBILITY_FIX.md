# Исправление исчезновения блока Participants

## 🐛 **Проблема:**
При заполнении любого поля в `base_data.js` блок `participants.js` исчезал из интерфейса.

## 🔍 **Причина:**
Все поля в `participants.js` имели условие скрытия:
```javascript
hidden: () => {
  return !contractBody.base_data.contract_type
}
```

Но поле `contract_type` **не было определено** в `base_data.js`, поэтому:
- `contractBody.base_data.contract_type` всегда было `undefined`
- `!undefined` = `true`
- Все поля в `participants.js` были скрыты

## 🔧 **Исправление:**

### **1. Добавили поле `contract_type` в `base_data.js`:**
```javascript
contract_type: {
  type: "hidden",
  value: 2, // Для упрощенных договоров всегда тип 2 (корпоративный)
},
```

### **2. Добавили значение по умолчанию в `simplified_body.js`:**
```javascript
this.base_data = {
  // ... другие поля ...
  contract_type: context.contract_type || 2, // По умолчанию тип 2 для упрощенных договоров
  // ... другие поля ...
}
```

### **3. Добавили отладочную информацию в `participants.js`:**
```javascript
console.log('🔍 Отладка participants.js:', {
  'contractBody.base_data': contractBody.base_data,
  'contractBody.base_data.contract_type': contractBody.base_data?.contract_type,
  'typeof contract_type': typeof contractBody.base_data?.contract_type,
  'contractBody.readonly': contractBody.readonly,
  'contractBody.participants': contractBody.participants
});
```

## 📊 **Типы договоров:**

В системе используются следующие типы договоров:
- **Тип 1** - Государственные договоры (единый поставщик)
- **Тип 2** - Корпоративные договоры (обычные поставщики)

Для упрощенных договоров используется **тип 2** (корпоративный).

## ✅ **Результат:**

### **Теперь логика работает правильно:**
```javascript
// В participants.js:
hidden: () => {
  return !contractBody.base_data.contract_type  // = !2 = false ✅
}
```

### **Блок participants.js:**
- ✅ **Отображается корректно** при любых изменениях в base_data
- ✅ **Показывает нужные поля** для корпоративных договоров
- ✅ **Скрывает поля единого поставщика** (они для типа 1)
- ✅ **Реагирует на изменения** в других полях

### **Поля в participants.js теперь показываются:**
- ✅ `custom_company` - переключатель "Добавить компанию вручную"
- ✅ `company` - выбор компании из списка (когда `custom_company = false`)
- ✅ `custom` - поля ручного ввода (когда `custom_company = true`)
- ✅ `email` - поле email (когда нужно)

## 🧪 **Для проверки:**

1. **Откройте упрощенный договор**
2. **Заполните любое поле в "Основные данные"**
3. **Убедитесь, что блок "Участники" остается видимым**
4. **Откройте консоль браузера** - должны появиться отладочные сообщения:
   ```
   🔍 Отладка participants.js: {
     contractBody.base_data.contract_type: 2,
     typeof contract_type: "number",
     ...
   }
   ```
5. **Проверьте работу переключателя** "Добавить компанию вручную"

## 🎯 **Техническая суть проблемы:**

**Было:**
```javascript
// simplified_body.js
contract_type: context.contract_type,  // undefined из контекста

// base_data.js
// Поле contract_type отсутствовало

// participants.js
hidden: () => !contractBody.base_data.contract_type  // = !undefined = true ❌
```

**Стало:**
```javascript
// simplified_body.js
contract_type: context.contract_type || 2,  // Гарантированно число

// base_data.js
contract_type: { type: "hidden", value: 2 },  // Явно определено

// participants.js
hidden: () => !contractBody.base_data.contract_type  // = !2 = false ✅
```

## 🎉 **Проблема решена!**

Блок "Участники" теперь корректно отображается независимо от заполнения полей в "Основных данных".
