export let SearchInput = {
  props: ["text"],
  data: function () {
    return {
      value: this.text,
      tid: undefined
    }
  },
  watch: {
    value: {
      immediate: true,
      async handler(val, oldVal) {
        if (val == oldVal)
          return;

        if (val == '' || val == null)
          val = undefined

        if (this.tid) {
          clearTimeout(this.tid);
        }
        this.tid = setTimeout(async () => {
          this.$emit("search", val)
        }, 400);
      }
    }
  },
  template: `
        <input style='padding: 10px; border-radius: 5px;width: 100%; border: 1px solid #009ab8; outline: none;' :placeholder='$t("search")' v-model='value' />
    `
}

export let validate = function () {
  if (this.value <= 0)
    return "Укажите значение больше 0"
}

export let validate_advance = function () {
  if (this.value < 0)
    return "Укажите значение не менее 0"

  // Валидация: аванс не должен превышать сумму
  if (this.parent && this.parent.summa && this.value > this.parent.summa) {
    return "Сумма аванса не может превышать общую сумму"
  }
}

// Валидация для графика финансирования
export let validate_graph_finance = function(items, contractTotalCost) {
  const errors = [];

  if (!items || items.length === 0) {
    return ["График финансирования не может быть пустым"];
  }

  let totalSum = 0;
  let totalAdvance = 0;

  // Проверяем каждую строку
  items.forEach((item, index) => {
    // Проверка: аванс <= сумме в строке
    if (item.advance_payment > item.summa) {
      errors.push(`Строка ${index + 1}: Сумма аванса (${item.advance_payment}) не может превышать сумму (${item.summa})`);
    }

    totalSum += parseFloat(item.summa || 0);
    totalAdvance += parseFloat(item.advance_payment || 0);
  });

  // Проверка: общая сумма графика должна равняться сумме договора
  if (contractTotalCost && Math.abs(totalSum - contractTotalCost) > 0.01) {
    errors.push(`Общая сумма по графику финансирования (${totalSum}) должна равняться сумме договора (${contractTotalCost})`);
  }

  return errors;
}
