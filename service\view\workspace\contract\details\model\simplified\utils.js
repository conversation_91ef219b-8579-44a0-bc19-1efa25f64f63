export let SearchInput = {
  props: ["text"],
  data: function () {
    return {
      value: this.text,
      tid: undefined
    }
  },
  watch: {
    value: {
      immediate: true,
      async handler(val, oldVal) {
        if (val == oldVal)
          return;

        if (val == '' || val == null)
          val = undefined

        if (this.tid) {
          clearTimeout(this.tid);
        }
        this.tid = setTimeout(async () => {
          this.$emit("search", val)
        }, 400);
      }
    }
  },
  template: `
        <input style='padding: 10px; border-radius: 5px;width: 100%; border: 1px solid #009ab8; outline: none;' :placeholder='$t("search")' v-model='value' />
    `
}

export let validate = function () {
  if (this.value <= 0)
    return "Укажите значение больше 0"
}

export let validate_advance = function () {
  if (this.value < 0)
    return "Укажите значение не менее 0"
}
