# Добавление конкурентного листа в упрощённые договоры

## Описание изменений

Добавлен обязательный конкурентный лист для упрощённых договоров, где заказчик должен указать не менее трех поставщиков, у которых ранее запрашивал цены.

## Новые файлы

### 1. `competitive_list.js`
Модель элемента конкурентного листа с полями:
- `supplier_name` - наименование поставщика (обязательное)
- `supplier_inn` - ИНН поставщика (обязательное, 9 цифр)
- `price` - цена (обязательное, > 0)
- `currency` - валюта (обязательное)
- `exchange_rate` - курс пересчета (автоматический)
- `price_uzs` - цена в UZS (автоматический расчет)

### 2. `props/competitive_list.js`
UI конфигурация для конкурентного листа:
- Data-grid с кнопками добавления, редактирования, удаления
- Автоматический пересчет валют
- Валидация минимального количества поставщиков

### 3. `COMPETITIVE_LIST_API.md`
Документация API для бэкенда с описанием:
- Нового действия `set_competitive_list_data`
- Структуры данных
- Валидации
- Прав доступа

## Изменённые файлы

### 1. `simplified_body.js`
- Добавлен импорт `SimplifiedContractCompetitiveList`
- Добавлена инициализация `this._competitive_list`
- Добавлен метод `edit_competitive_list()`
- Добавлена валидация в методе `validate()`
- Добавлена обработка в методе `save()`

### 2. `props/index.js`
- Добавлен импорт `getCompetitiveListProps`
- Добавлен вызов в `getAllProps()`

### 3. `README.md`
- Добавлена информация о конкурентном листе
- Обновлена структура файлов
- Добавлено описание валидации

## Функциональность

### Добавление поставщика
1. Пользователь нажимает кнопку "Добавить"
2. Открывается форма с полями поставщика
3. При выборе валюты автоматически подставляется курс
4. При изменении цены или курса пересчитывается цена в UZS
5. Валидация полей при сохранении

### Редактирование поставщика
1. Пользователь нажимает кнопку "Редактировать" в строке
2. Открывается форма с заполненными данными
3. Изменения сохраняются с пересчетом валют

### Удаление поставщика
1. Пользователь нажимает кнопку "Удалить"
2. Строка помечается как удаленная (красная подсветка)
3. Кнопка "Восстановить" для отмены удаления

### Сохранение изменений
1. Кнопка "Сохранить изменения" появляется при наличии несохраненных данных
2. Отправляет запрос `set_competitive_list_data` на бэкенд
3. Проверяет минимальное количество поставщиков (3)

### Валидация при публикации
1. Метод `validate()` проверяет наличие минимум 3 поставщиков
2. Блокирует публикацию договора при нарушении требований
3. Показывает соответствующее сообщение об ошибке

## Требования к бэкенду

### Новое действие: `set_competitive_list_data`
```json
{
  "action": "set_competitive_list_data",
  "data": {
    "competitive_list": [
      {
        "supplier_name": "ООО Поставщик",
        "supplier_inn": "123456789", 
        "price": 1000.00,
        "currency": "UZS",
        "exchange_rate": 1,
        "price_uzs": 1000.00
      }
    ]
  }
}
```

### Новое право: `set_competitive_list_data`
Должно возвращаться в `get_rights` для упрощённых договоров.

### Валидация на бэкенде:
- Минимум 3 поставщика
- ИНН: ровно 9 цифр
- Цена > 0
- Корректный расчет цены в UZS

### Интеграция с публикацией:
При публикации проверять наличие и валидность конкурентного листа.

## Пользовательский интерфейс

### Расположение
Конкурентный лист отображается как отдельная секция между графиком финансирования и спецификацией.

### Внешний вид
- Таблица с колонками: Поставщик, ИНН, Цена, Курс, Цена в UZS
- Кнопки действий для каждой строки
- Общие кнопки "Добавить" и "Сохранить изменения"
- Сообщение "Добавьте не менее трех поставщиков" при пустом списке

### Валидация в реальном времени
- Проверка ИНН при вводе
- Автоматический пересчет валют
- Подсветка ошибок валидации

## Совместимость

### Обратная совместимость
- Существующие упрощённые договоры продолжают работать
- Конкурентный лист обязателен только для новых договоров
- Не влияет на прямые договоры

### Права доступа
- Конкурентный лист доступен только при наличии права `set_competitive_list_data`
- Readonly режим при отсутствии прав редактирования
