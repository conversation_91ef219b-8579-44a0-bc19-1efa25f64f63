# Автозаполнение источников финансирования

## 🎯 **Реализованный функционал:**

Источники финансирования теперь **автоматически формируются** из графика финансирования и **недоступны для редактирования**.

## 🔧 **Что было добавлено:**

### 1. **Метод `updateGraphFromFinance()` в `simplified_body.js`:**
```javascript
// Метод для автоматического обновления источников финансирования из графика финансирования
updateGraphFromFinance(financeItems = null) {
  if (!financeItems) {
    financeItems = this._graph_finance || [];
  }

  const sourceMap = {};

  // Группируем по источникам финансирования (для упрощенных договоров используем общий источник)
  financeItems.forEach(item => {
    const sourceKey = 'Источник финансирования'; // Для упрощенных договоров один источник
    
    if (!sourceMap[sourceKey]) {
      sourceMap[sourceKey] = {
        source: sourceKey,
        summa: 0,
        advance_payment: 0,
        currency: item.currency || 'UZS',
        status: 0
      };
    }
    
    sourceMap[sourceKey].summa += parseFloat(item.summa || 0);
    sourceMap[sourceKey].advance_payment += parseFloat(item.advance_payment || 0);
  });

  const graphData = Object.values(sourceMap);
  
  // Обновляем внутренние данные
  this._graph = graphData;
  
  // Обновляем данные в источниках финансирования (только если properties уже созданы)
  if (this.properties?.graph?.dataSource) {
    this.properties.graph.dataSource.set_items(graphData);
  }

  console.log('🔄 Источники финансирования обновлены автоматически:', graphData);
}
```

### 2. **Автоматическая инициализация в конструкторе:**
```javascript
// В конструкторе SimplifiedContractBody:
this._graph = context.graph || []
this._graph_finance = context.graph_finance || []
this._competitive_list = context.competitive_list || []
this._spec = context.spec || []

// Инициализируем источники финансирования из графика финансирования
this.updateGraphFromFinance();
```

### 3. **Автоматическое обновление при сохранении:**
```javascript
// В методе save() при обработке graph_finance:
actions.push({
  name: 'set_graph_data',
  params: { graph: items, graph_totalcost: graph_totalcost }
});
this.updateGraphFromFinance(items); // ← Добавлено автообновление
```

### 4. **Автоматическое обновление при получении данных с сервера:**
```javascript
// При получении новых данных graph_finance:
if (params.graph_finance) {
  this._graph_finance = params.graph_finance.map((item) => {
    return { ...item, status: 0 }
  });
  this.properties.graph_finance.dataSource.set_items(this._graph_finance);
  
  // Обновляем источники финансирования при получении новых данных графика финансирования
  this.updateGraphFromFinance();
}
```

### 5. **Настройка readonly в `props/graph.js`:**
```javascript
export function getGraphProps(contractBody) {
  return {
    graph: {
      group: "comm_src",
      type: "data-grid",
      readonly: true, // Источники финансирования недоступны для редактирования
      label: "!grid",
      description: "Источники финансирования автоматически формируются из графика финансирования",
      dataSource: new DataSource({
        // Убираем действия добавления/редактирования - только просмотр
        actions: [],
        store: {
          data: contractBody._graph || [], // Используем уже подготовленные данные
          context: (context) => {
            // Убираем все действия редактирования - только просмотр
            context.actions = [];
            return context;
          }
        }
      }),
      attr: {
        buttons: false, // Убираем кнопки действий
        summary: true,
        not_found: "Источники финансирования будут сформированы автоматически после заполнения графика финансирования",
        // ... колонки остаются те же
      }
    }
  };
}
```

## 🎯 **Как это работает:**

1. **При создании договора** - источники финансирования инициализируются из графика финансирования
2. **При изменении графика финансирования** - источники автоматически пересчитываются
3. **При сохранении** - источники обновляются и отправляются на сервер
4. **При получении данных с сервера** - источники автоматически синхронизируются

## ✅ **Результат:**

- ✅ **Источники финансирования формируются автоматически** из графика финансирования
- ✅ **Недоступны для ручного редактирования** (readonly: true, buttons: false)
- ✅ **Автоматически обновляются** при изменении графика финансирования
- ✅ **Синхронизируются с сервером** при сохранении
- ✅ **Показывают корректные суммы и авансы** по валютам
- ✅ **Отображают информативное сообщение** когда данных нет

## 🧪 **Для тестирования:**

1. Откройте упрощенный договор
2. Заполните график финансирования
3. Проверьте, что источники финансирования автоматически обновились
4. Убедитесь, что источники финансирования недоступны для редактирования
5. Сохраните договор и проверьте, что данные корректно отправляются на сервер
