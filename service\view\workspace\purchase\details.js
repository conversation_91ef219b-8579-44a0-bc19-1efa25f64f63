import { Http, Language, Action } from '@iac/core'
import { DataSource } from '@iac/data'
import { Context } from '@iac/kernel'
import Position from './position'
import Item from './item'

export default {
    data: function () {
        return {
            error: undefined,
            model: undefined
        }
    },
    mounted() {
        this.update_model();
    },
    watch: {
        $route(to, from) {
            this.update_model();
        }
    },
    computed: {
        type() {
            return this.model.type.id || this.model.type;
        },
        has_create_proc() {
            if (this.model.proc_id)
                return false;

            if ((this.model.items.state & DataSource.STATE_ERROR) != 0)
                return false;

            if ((this.model.items.state & DataSource.STATE_REQUEST) != 0)
                return false;

            if (this.model.items.items && this.model.items.items.length <= 0) {
                return false;
            }

            return true;
        },
    },
    methods: {
        update_model() {
            this.model = this.error = undefined;
            this.$wait(async () => {
                let { error, data } = await Position.get(this.$route.params.id);
                if (error) {
                    this.error = error
                } else {
                    this.model = data;
                }
            })
        },
        async contract_create() {

            let procedure_params = {
                object: {
                    type: "new_purchase",
                    id: this.model.id
                }
            }

            let response = await Action["contract.create"](procedure_params);
            if (!response)
                return;
            let { error, data: id } = response;
            if (error) {
                Vue.Dialog.MessageBox.Error(error)
            } else if (id) {
                this.$router.push({ path: `/workspace/contract/${id}/core` });
            }


        },
        async simplified_contract_create() {

            let procedure_params = {
                object: {
                    type: "new_purchase",
                    id: this.model.id
                },
                contract_type: 'simplified_contract',
                buyer_type_id: Context.User.buyer_type_id
            }

            let response = await Action["contract.create"](procedure_params);
            if (!response)
                return;
            let { error, data: id } = response;
            if (error) {
                // Проверяем, если это ошибка превышения БРВ
                if (error.code === 'BRV_LIMIT_EXCEEDED') {
                    Vue.Dialog.MessageBox.Error(error.message);
                } else {
                    Vue.Dialog.MessageBox.Error(error);
                }
            } else if (id) {
                this.$router.push({ path: `/workspace/contract/${id}/core` });
            }


        },
        async create_procedure() {
            let procedure_params = {
                object: {
                    type: "new_purchase",
                    id: this.model.id
                }
            }

            let response = await Action["procedure.create"](procedure_params);
            if (!response)
                return;
            let { error, data } = response;
            if (!error) {
                this.$router.push({ path: `/procedure/${data[0].proc_id}/core` });
            }
        },
        async delete_position() {
            if (await Vue.Dialog.MessageBox.Question(Language.t('question_delete_position')) != Vue.Dialog.MessageBox.Result.Yes) {
                return;
            }
            let { error, data } = await Http.api.rpc("ref_schedule", {
                ref: "schedule_position",
                op: "delete",
                filters: {
                    id: this.model.id
                }
            })

            if (error) {
                return await Vue.Dialog.MessageBox.Error(error)
            }
            this.$router.push({ path: `/workspace/purchase` });

        },
        async set_status(status) {
            //await Vue.Dialog.MessageBox.Info(`Попытка установить статус ${status}`)

            if (await Vue.Dialog.MessageBox.Question(Language.t('change_status_question')) != Vue.Dialog.MessageBox.Result.Yes) {
                return;
            }

            let { error, data } = await Http.api.rpc("ref_schedule", {
                ref: "schedule_position",
                op: "set_status",
                filters: {
                    id: [this.model.id]
                },
                data: {
                    status: status
                }
            })

            if (error) {
                return await Vue.Dialog.MessageBox.Error(error)
            }

            this.model.status = status
        },
        async copy_position() {
            let id = await Vue.Dialog.PurchaseAddPosition.Modal({
                size: 'lg',
                position: this.model
            })

            if (id) {
                this.$router.push({ path: `/workspace/purchase/${id}` })
            }
        }
    },
    template: `
            <div class='page-purchase-details'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li><router-link to='/workspace/purchase'>{{$t('nav.purchase')}}</router-link></li>
                    <li v-if='model'>{{$t( (model.proc_id || !$policy.schedule_create) ? 'schedule.details' : 'schedule.edit')}}</li>
                </ol>
                <div class='title' v-if='model'>
                    <h1 style='margin: 0;'>{{$t( (model.proc_id || !$policy.schedule_create) ? 'schedule.details' : 'schedule.edit')}}</h1>
                    <ui-btn-group>

                        <ui-btn v-if='has_create_proc && model.status == "published"' type='primary' v-on:click.native='create_procedure'>{{$t('create_procedure')}}</ui-btn>
                        <ui-btn v-if='has_create_proc && model.status == "published" && $policy.contract_create && $user_develop.custom_contract_develop' type='primary' v-on:click.native='contract_create'>{{$t('contract.create')}}</ui-btn>
                        <ui-btn v-if='has_create_proc && model.status == "published" && $policy.contract_create && $user_develop.custom_contract_develop' type='primary' v-on:click.native='simplified_contract_create'>Договор упрощённой закупки</ui-btn>

                        <ui-btn v-if='model.status == "draft"' type='primary' v-on:click.native='set_status("published")'>{{$t('action.publish_procedure')}}</ui-btn>
                        <ui-btn v-if='!model.proc_id && model.status == "published"' type='primary' v-on:click.native='set_status("draft")'>{{$t('edit')}}</ui-btn>

                        <ui-btn v-if='' type='primary' v-on:click.native='copy_position'>{{$t('copy')}}</ui-btn>
                        <ui-btn v-if='!model.proc_id && model.status == "draft"' type='danger' v-on:click.native='delete_position'>{{$t('delete')}}</ui-btn>

                    </ui-btn-group>
                </div>
            </iac-section>
            <iac-section v-if='error'>
                <ui-error :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
            </iac-section>
            <iac-section v-if='model'>
                <div>

                <ui-layout-group :horizontal=false>
                    <div style='align-self: normal;'>
                        <ui-layout-group label='general'>
                            <ui-layout :fields='model.fields'/>
                        </ui-layout-group>
                    </div>

                    <ui-layout-group>
                        <ui-data-view :search='false' :dataSource='model.items'>
                            <div slot='not-found' slot-scope='props'>
                            </div>
                        </ui-data-view>
                    </ui-layout-group>
                </ui-layout-group>
                </div>
            </iac-section>
        </div>
    `
}