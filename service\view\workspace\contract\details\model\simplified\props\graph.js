import { DataSource } from '@iac/data';
import { Language, Util } from '@iac/core';

export function getGraphProps(contractBody) {
  // Автоматически формируем источники финансирования из графика финансирования
  const generateGraphFromFinance = () => {
    // Используем метод из основного класса для генерации данных
    contractBody.updateGraphFromFinance();
    return contractBody._graph || [];
  };

  return {
    graph: {
      group: "comm_src",
      type: "data-grid",
      readonly: true, // Источники финансирования недоступны для редактирования
      label: "!grid",
      description: "Источники финансирования автоматически формируются из графика финансирования",
      dataSource: new DataSource({
        // Убираем действия добавления/редактирования - только просмотр
        actions: [],
        store: {
          data: generateGraphFromFinance(), // Используем автоматически сгенерированные данные
          context: (context) => {
            // Убираем все действия редактирования - только просмотр
            context.actions = [];
            return context;
          }
        }

      }),
      attr: {
        // Убираем колонку действий для readonly таблицы
        buttons: false,
        summary: true,
        not_found: "Источники финансирования будут сформированы автоматически после заполнения графика финансирования",
        columns: [
          {
            field: "source",
            label: "Источник финансирования",
            style: "width: 100%; text-align: left;",
            summary: (items) => {
              if (!items)
                return;
              let count = items.length;
              return `${count} ${Language.t("source", { count: count })}`
            }
          },
          {
            field: "summa",
            label: "Сумма",
            style: "white-space: nowrap; text-align: right;",
            display: (value, item) => {
              return `${Util.Number(value, ' ', 2)} ${item.currency}`;
            },
            summary: (items) => {
              if (!items)
                return;

              let currency = items.reduce((prev, item) => {
                prev[item.currency] = prev[item.currency] || 0
                prev[item.currency] += item.summa
                return prev
              }, {});

              return Object.keys(currency).map((key) => {
                return `${Util.Number(currency[key], ' ', 2)} ${key}`
              }).join('<br/>')
            }
          },
          {
            field: "advance_payment",
            label: "Сумма аванса",
            style: "white-space: nowrap; text-align: right;",
            display: (value, item) => {
              return `${Util.Number(value || 0, ' ', 2)} ${item.currency}`;
            },
            summary: (items) => {
              if (!items)
                return;

              let currency = items.reduce((prev, item) => {
                prev[item.currency] = prev[item.currency] || 0
                prev[item.currency] += item.advance_payment || 0
                return prev
              }, {});

              return Object.keys(currency).map((key) => {
                return `${Util.Number(currency[key], ' ', 2)} ${key}`
              }).join('<br/>')
            }
          }
        ]
      }
    }
  };
}
