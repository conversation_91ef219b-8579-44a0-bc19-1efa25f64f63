import { DataSource } from '@iac/data';
import { Language, Util } from '@iac/core';

export function getGraphProps(contractBody) {
  return {
    graph: {
      group: "comm_src",
      type: "data-grid",
      readonly: !contractBody.contract.rights || !contractBody.contract.rights.set_graph_data,
      label: "!grid",
      dataSource: new DataSource({
        actions: [
          {
            label: "add",
            btn_type: "success",
            handler: async () => {
              let item = await contractBody.edit_graph();
              if (!item || item == 2)
                return;

              contractBody.properties.graph.dataSource.push_item({
                status: 1,

                source: item.source,
                summa: item.summa,
                currency: item.currency,
                advance_payment: item.advance_payment || 0,
              })
            }
          },
          {
            label: "save_changes",
            hidden: () => {
              let items = contractBody.properties.graph.dataSource.items;
              if (!items)
                return true;
              return items.filter((item) => {
                if ((item.status & 7) != 0)
                  return true
              }).length <= 0;
            },
            handler: async () => {
              await contractBody.save(["graph"]);
            }
          }
        ],
        store: {
          data: contractBody._graph.map((item) => {
            return { ...item, status: 0 }
          }),
          context: (context) => {

            Object.defineProperty(context, "bindClass", {
              configurable: true,
              enumerable: true,
              get: () => {

                if ((context.status & 4) != 0)
                  return "ui-alert ui-alert-danger";

                if ((context.status & 1) != 0)
                  return "ui-alert ui-alert-success";

                if ((context.status & 2) != 0)
                  return "ui-alert ui-alert-warning";

              },
            });

            context.actions = [
              {
                btn_type: "warning",
                icon: "edit",
                hidden: () => {
                  return ((context.status & 4) != 0)
                },
                handler: async () => {
                  let item = await contractBody.edit_graph(context);
                  if (!item || item == 2)
                    return;

                  context.source = item.source
                  context.summa = item.summa
                  context.currency = item.currency
                  context.advance_payment = item.advance_payment || 0,

                    context.status |= 2;
                }
              },
              {
                btn_type: "danger",
                icon: "trash",
                hidden: () => {
                  return ((context.status & 4) != 0)
                },
                handler: () => {
                  context.status |= 4;
                }
              },
              {
                label: "Восстановить",
                btn_type: "danger",
                hidden: () => {
                  return !((context.status & 4) != 0)
                },
                handler: () => {
                  context.status &= ~4;
                }
              }
            ]

            return context;
          }
        }

      }),
      attr: {
        action_name: Language.t("actions"),
        action_style: "min-width: 110px;text-align:left;",
        buttons: true,
        summary: true,
        not_found: "",
        columns: [
          {
            field: "source", label: "product_name", style: "width: 100%; text-align: left;",
            summary: (items) => {
              if (!items)
                return;
              let count = items.filter((item) => {
                return !((item.status & 4) != 0);
              }).length;
              return `${count} ${Language.t("source", { count: count })}`
            }
          },
          {
            field: "summa", style: "white-space: nowrap; text-align: right;",
            display: (value, item) => {
              return `${Util.Number(value, ' ', 2)} ${item.currency}`;
            },
            summary: (items) => {
              if (!items)
                return;

              let currency = items.reduce((prev, item) => {
                if ((item.status & 4) != 0)
                  return prev
                prev[item.currency] = prev[item.currency] || 0
                prev[item.currency] += item.summa
                return prev
              }, {});

              return Object.keys(currency).map((key) => {
                return `${Util.Number(currency[key], ' ', 2)} ${key}`
              }).join('<br/>')
            }
          },
          {
            field: "advance_payment", label: "advance_payment_amount", style: "white-space: nowrap; text-align: right;",
            display: (value, item) => {
              return `${Util.Number(value || 0, ' ', 2)} ${item.currency}`;
            },
            summary: (items) => {
              if (!items)
                return;

              let currency = items.reduce((prev, item) => {
                if ((item.status & 4) != 0)
                  return prev
                prev[item.currency] = prev[item.currency] || 0
                prev[item.currency] += item.advance_payment || 0
                return prev
              }, {});

              return Object.keys(currency).map((key) => {
                return `${Util.Number(currency[key], ' ', 2)} ${key}`
              }).join('<br/>')
            }
          }
        ]
      }
    }
  };
}
