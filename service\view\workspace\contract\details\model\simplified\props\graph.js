import { DataSource } from '@iac/data';
import { Language, Util } from '@iac/core';

export function getGraphProps(contractBody) {
  return {
    graph: {
      group: "comm_src",
      type: "data-grid",
      label: "!grid",
      description: "Источники финансирования автоматически формируются из графика финансирования",
      dataSource: new DataSource({
        // Убираем действия добавления/редактирования - только просмотр
        actions: [],
        store: {
          data: contractBody._graph || [], // Используем уже подготовленные данные
          context: (context) => {
            // Убираем все действия редактирования - только просмотр
            context.actions = [];
            return context;
          }
        }

      }),
      attr: {
        buttons: false, // Убираем кнопки действий
        summary: true,
        not_found: "Источники финансирования будут сформированы автоматически после заполнения графика финансирования",
        columns: [
          {
            field: "source", label: "product_name", style: "width: 100%; text-align: left;",
            summary: (items) => {
              if (!items)
                return;
              let count = items.filter((item) => {
                return !((item.status & 4) != 0);
              }).length;
              return `${count} ${Language.t("source", { count: count })}`
            }
          },
          {
            field: "summa", style: "white-space: nowrap; text-align: right;",
            display: (value, item) => {
              return `${Util.Number(value, ' ', 2)} ${item.currency}`;
            },
            summary: (items) => {
              if (!items)
                return;

              let currency = items.reduce((prev, item) => {
                if ((item.status & 4) != 0)
                  return prev
                prev[item.currency] = prev[item.currency] || 0
                prev[item.currency] += item.summa
                return prev
              }, {});

              return Object.keys(currency).map((key) => {
                return `${Util.Number(currency[key], ' ', 2)} ${key}`
              }).join('<br/>')
            }
          },
          {
            field: "advance_payment",
            label: "Сумма аванса",
            style: "white-space: nowrap; text-align: right;",
            display: (value, item) => {
              return `${Util.Number(value || 0, ' ', 2)} ${item.currency}`;
            },
            summary: (items) => {
              if (!items)
                return;

              let currency = items.reduce((prev, item) => {
                prev[item.currency] = prev[item.currency] || 0
                prev[item.currency] += item.advance_payment || 0
                return prev
              }, {});

              return Object.keys(currency).map((key) => {
                return `${Util.Number(currency[key], ' ', 2)} ${key}`
              }).join('<br/>')
            }
          }
        ]
      }
    }
  };
}
