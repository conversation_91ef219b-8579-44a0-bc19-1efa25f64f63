import { Entity, DataSource } from '@iac/data';
import { Http, Language } from '@iac/core';
import { Config } from '@iac/kernel';

const $t = Language.t;

const group = (name, fields) => {
  if (name == null) return fields;

  Object.keys(fields).map(k => fields[k].group = name);
  return fields;
};

const vote_history = (props, i) => {
  props.consensus = props?.consensus === true ? $t("Yes") : $t("No");
  props.votes = {
    for: props.for,
    against: props.against
  };

  delete props.for;
  delete props.against;
  
  if (props?.votes) {
    let votes = props.votes;
    votes = {
      for: Array.isArray(votes.for) ? votes.for.length : 0,
      against: Array.isArray(votes.against) ? votes.against.length : 0,
      commission: props?.count_in_commission != null ? props.count_in_commission : 0
    };
    
    if (!(votes.for == 0 && votes.against == 0)) {
      props.consensus_description = $t('voting_results').replace('{for}', votes.for).replace('{against}', votes.against).replace('{commission}', votes.commission);
      const percent = (votes.for + votes.against)*100/votes.commission;
      const rounded_percent = Number(percent.toFixed(2));
      if (votes.commission > 0) props.consensus_description += $t('voting_percentage').replace('{percent}', rounded_percent);
    }
  }
  
  props.votes_grid = (props.votes?.["for"] || []).concat(props.votes?.against || []).map(item => {
    return {
      date: item.datetime,
      vote: item.reason == undefined ? 'voted_positively' : 'voted_negatively',
      reason: item.reason,
      name: item.auth_data?.name
    };
  }).sort((a, b) => {
    if (a.date < b.date) return -1;
    if (a.date > b.date) return 1;
    return 0;
  });

  return {
    label: $t('voting_number').replace('{number}', i),
    type: 'model',
    order: 4,
    fields: {
      datetime_commission: {
        label: "--arbitr_date_time_voting",
        type: "widget",
        widget: {
          name: 'iac-date',
          props: {
            get date() {
              return props.datetime_commission;
            },
            full: true
          }
        }
      },
      commission_decision: {
        label: "--arbitr_select_decision",
        type: "static",
        value: props.commission_decision.name
      },
      who_pay_fine: {
        label: "--who_pays",
        type: "static",
        bind: {
          hidden: () => {
            const commission_decision = props?.commission_decision?.code || props?.commission_decision || "undefined";
            return [
              "fine_for_non_registration_contract",
              "fine_with_terminate_contract",
              "fine_without_terminate_contract",
            ].indexOf(commission_decision) == -1;
          }
        },
        value: props?.who_pay_fine?.name
      },
      remark: {
        label: "--arbitr_notes",
        type: "static",
        value: props.remark
      },
      consensus: {
        label: "--consensus",
        type: "static",
        value: props.consensus,
        description: props.consensus_description
      },
      votes: {
        type: "data-grid",
        readonly: true,
        label: "!votes",
        dataSource: DataSource.get(props.votes_grid),
        attr: {
          summary: false,
          columns: [
            { field: "index", label: "№" },
            { field: "date", type: "date" },
            { field: "name", style: "width: 100%; text-align: left;" },
            {
              field: "vote", style: "text-align: right;", component: {
                props: ['item'],
                template: `
                  <div :title='item.reason'>{{$t(item.vote)}}</div>
                `
              }
            }
          ]
        },
      }
    }
  };
}

export default class ArbitrationComplaint extends Entity {
  constructor(context = {}) {
    super(context);
    this.rights = {};
    this.actions = undefined;
    this.save_data = false;
    this.consensus_description = undefined;
    this.vote_history = [];
    this.update_context(context);
  }

  async onChangeProperty(_event) {
    if (this.lock) return;
    this.save_data = false;
  }

  async update_rights(rights) {
    if (!rights) {
      const { error, data } = await this.action('get_rights');
      rights = data || [];
    } 

    for (let name in rights) {
      rights[name] = rights[name] == 'grant';
    }
    

    this.rights = rights || {};

    this.actions =
      Object
      .keys(this.rights)
      .filter(key => this.rights[key] && key.indexOf('__') == 0)
      .map(key => {
        return {
          name: key.replace(/^__/gi, ''),
          method: key
        };
      });
  }

  // Вызывать при обновлении по сокету
  async update_contract_detailed(fields = []) {
    let { data, error } = await Http.api.rpc('contract_detailed', {
      number: this.number,
      fields: (fields && fields.length > 0) ? fields : undefined
    });

    if (error) return Vue.Dialog.MessageBox.Error(error);

    await this.update_context({ ...this.context, ...data, rights: undefined });
  }

  async update_context(context) {
    this.lock = true;
    this.number = context.number;
    this.status = context.status;
    this.comment = context.comment;
    this.who_pay_fine = context.who_pay_fine;
    this.who_pay_fine_outsystem = context.who_pay_fine_outsystem;
    this.fine_amount_outsystem = context.fine_amount_outsystem;
    this.vote_history = context?.vote_history || [];

    this.arbitration_commission_decisions = context.arbitration_commission_decisions;

    //this.complaint_data = context.data || {};
    //this.voting = context.data.voting || {};
    this.initiator = context.initiator && (context.initiator == "system" ? "system" : context.initiator) || {};
    if (this.initiator.user_details) this.initiator = this.initiator.user_details;


    // Информация о жалобе
    this.complaint_from_subject = context.complaint_from_subject;
    if (Array.isArray(this.complaint_from_subject))
      this.complaint_from_subject = this.complaint_from_subject[0];

    this.complaint_object = context.complaint_object;
    this.complaint_to_subjects = context.complaint_to_subjects;
    this.complaint_text = context.complaint_text;
    this.files = context.files;

    // Голосование
    this.datetime_commission = context.datetime_commission;
    this.commission_decision = context.commission_decision;
    this.remark = context.remark;
    this.consensus = context?.votes?.consensus === true ? $t("Yes") : $t("No");

    if (context?.votes) {
      let votes = context.votes;
      votes = {
        for: Array.isArray(votes.for) ? votes.for.length : 0,
        against: Array.isArray(votes.against) ? votes.against.length : 0,
        commission: context?.count_in_commission != null ? context.count_in_commission : 0
      };

      if (!(votes.for == 0 && votes.against == 0)) {
        this.consensus_description = $t('voting_results').replace('{for}', votes.for).replace('{against}', votes.against).replace('{commission}', votes.commission);
        const percent = (votes.for + votes.against)*100/votes.commission;
        const rounded_percent = Number(percent.toFixed(2));
        if (votes.commission > 0) this.consensus_description += $t('voting_percentage').replace('{percent}', rounded_percent);
      }
    }

    let votes = (context.votes?.["for"] || []).concat(context.votes?.against || []).map(item => {
      return {
        date: item.datetime,
        vote: item.reason == undefined ? 'voted_positively' : 'voted_negatively',
        reason: item.reason,
        name: item.auth_data?.name
      };
    }).sort((a, b) => {
      if (a.date < b.date) return -1;
      if (a.date > b.date) return 1;
      return 0;
    });

    this.properties.votes.setAttributes({
      dataSource: DataSource.get(votes),
      hidden: votes && votes.length > 0 ? false : true,
    });

    await this.update_rights(context.rights);

    this.save_data = true;
    this.lock = false;
  }

  async action(name, params) {
    this.wait = true;
    let result = await ArbitrationComplaint.Action(name, this.number, params);
    this.wait = false;

    return result;
  }

  static async Action(name, number, params) {
    return await Http.api.rpc('contract_action', {
      action: name,
      number,
      params,
    });
  }

  async develop(method, show_dlg = false) {
    let params = show_dlg ? await Vue.Dialog({
      props: ["title"],
      data: function () {
        return { json: undefined };
      },
      methods: {
        send() {
          try {
            let result = this.json;

            if (result) result = JSON.parse(this.json);
            else result = {};

            this.Close(result);
          } catch (e) {
            Vue.Dialog.MessageBox.Error(e);
          }
        }
      },
      template: `
        <div>
          <header>{{title}}</header>
          <main>
            <ui-text label='Введите json' v-model='json'/>
          </main>
          <footer>
            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
            <ui-btn type='primary' v-on:click.native='send'>{{$t("send")}}</ui-btn>
          </footer>
        </div>
      `
    }).Modal({ title: method }) : {};

    if (!params) return;

    let { error } = await this.action(method, params);

    if (error) {
      if (error.code != 'AbortError') Vue.Dialog.MessageBox.Error(error);
      return;
    }

    if (method == 'delete_contract') {
      Vue.Dialog.MessageBox.Success("Договор удален");
      return true;
    }

    await this.update_contract_detailed();
  }

  async save() {
    if (!this.rights.save) return;

    if (this.validate()) {
      Vue.Dialog.MessageBox.Error($t('bad_params'));
      return;
    }

    let params = this.fields.filter(field => {
      if (['link', 'model', 'static', 'widget', 'action'].includes(field.type)) {
        return false;
      }

      if (field.hidden && typeof field.hidden == 'function') {
        return !field.hidden();
      }

      return !field.hidden;
    }).map((field) => {
      let value = field.value;
      if (field?.value?.exp?.value) value = field.value.exp.value;
      if ((value == '' || value == undefined)) value = null;
      if (field.type == 'bool' && !value) value = false;

      return {
        name: field.name,
        value: value
      };
    }).reduce((prev, curr) => {
      prev[curr.name] = curr.value;
      return prev;
    }, {});

    this.wait = true;

    if (params.files && Array.isArray(params.files)) {
      for (let index in params.files) {
        let item = params.files[index];
        
        if (item && item.file && item.file.name && item.file instanceof File) {
          let formData = new FormData();
          formData.append('data', item.file, item.file.name);

          let { data, error } = await Http.upload.form('tender/attach', formData);

          if (error) {
            continue;
          } else
            params.files[index] = {
              id: data.uuid,
              name: data.meta.name,
              meta: {
                "type": data.meta.type,
                "content_type": data.meta.content_type,
                "type_group": data.meta.group,
                "size": data.meta.size
              }
            };
        }
      }

      this.files = [...params.files];
    }

    this.wait = false;

    let { error } = await this.action("save", params);

    if (error) {
      Vue.Dialog.MessageBox.Error(error);
      return;
    }

    return true;
  }

  get has_save_data() {
    return !this.validate(false) && this.save_data;
  }

  props() {
    let $self = this;

    return {
      ...group("complainter", {
        initiator: this.initiator_field(this.initiator),
      }),
      ...group("complaint_info", {
        complaint_object: {
          order: 2,
          label: "--complaint_reason",
          type: "link",
          hidden: !this.complaint_object,
          attr: {
            text: `${$t('contract')} № ${this.complaint_object}`,
            to: `/workspace/contract/${this.complaint_object}/core`
          },
        },
        complaint_from_subject: {
          order: 2,
          label: "--complaint_from",
          type: "static",
          required: true,
          bind: {
            type: () => this.rights.save ? "entity" : "static"
          },
          dataSource: {
            valueExp: ["id", "type", "name", "description", "data"],
            descExp: "description",
            query: {
              number: this.complaint_object
            },
            store: {
              method: "get_possible_arbitration_subjects"
            }
          },
        },
        complaint_to_subjects: {
          order: 2,
          label: "--complaint_on_whom",
          type: "static",
          required: true,
          bind: {
            type: () => this.rights.save ? "entity" : "static"
          },
          multiple: true,
          dataSource: {
            valueExp: ["id", "type", "name", "description", "data"],
            descExp: "description",
            query: {
              number: this.complaint_object
            },
            store: {
              method: "get_possible_arbitration_subjects"
            }
          },
        },
        complaint_text: {
          order: 2,
          label: "--the_essence_of_the_complaint",
          type: "text",
          attr: {
            react: true
          },
          bind: {
            type: () => (this.rights.save || this.rights.save) ? "text" : "static"
          },
        },
        files: {
          order: 2,
          label: "--attachment",
          type: "file",
          multiple: true,
          readonly: () => !(this.rights.save || this.rights.save),
          meta: {
            url: (value) => {
              if (!value.id) return;

              return `${Config.api_server}/file/${value.id}`;
            },
          }
        },
        comment: {
          order: 2,
          label: "--comment",
          type: "static",
          readonly: true,
          bind: { hidden: '!comment' }
        },
      }),
      ...group("{arbitr_voting}/current", {
        datetime_commission: {
          group: "{arbitr_voting}/current",
          order: 3,
          label: "--arbitr_date_time_voting",
          type: "widget",
          widget: {
            name: 'iac-date',
            props: {
              get date() {
                return $self.datetime_commission;
              },
              full: true
            }
          },
          bind: {
            hidden: '!datetime_commission'
          }
        },
        commission_decision: {
          group: "{arbitr_voting}/current",
          order: 3,
          label: "--arbitr_select_decision",
          type: "entity",
          required: true,
          bind: {
            type: () => (this.rights.save || this.rights.save) ? "entity" : "static",
            hidden: '!datetime_commission'
          },
          dataSource: {
            valueExp: ["id", "name", "code"],
            query: { code: this.arbitration_commission_decisions },
            store: {
              ref: "ref_arbitration_commission_decisions",
              inject: items => {
                return items.filter(item => {
                  return (this.arbitration_commission_decisions || []).includes(item.code);
                });
              }
            }
          }
        },
        who_pay_fine: {
          label: "--who_pays",
          type: "entity",
          required: true,
          group: "{arbitr_voting}/current",
          order: 3,
          bind: {
            type: () => (this.rights.save || this.rights.save) ? "entity" : "static",
            hidden: () => {
              const commission_decision = this?.commission_decision?.code || this?.commission_decision || "undefined";
              return [
                "fine_with_terminate_contract",
                "fine_without_terminate_contract",
              ].indexOf(commission_decision) == -1;
            }
          },
          dataSource: {
            valueExp: ["id", "name"],
            query: { code: this.who_pay_fine },
            store: [{
              id: "buyer",
              name: $t("buyer")
            },
            {
              id: "seller",
              name: $t("seller")
            },]
          }
        },
        who_pay_fine_outsystem: {
          label: "--who_pays",
          type: "entity",
          required: true,
          group: "{arbitr_voting}/current",
          order: 3,
          bind: {
            type: () => (this.rights.save || this.rights.save) ? "entity" : "static",
            hidden: () => {
              const commission_decision = this?.commission_decision?.code || this?.commission_decision || "undefined";
              return commission_decision != "fine_for_non_registration_contract";
            }
          },
          dataSource: {
            valueExp: ["id", "name"],
            query: { code: this.who_pay_fine_outsystem },
            store: [{
              id: "broker_buyer",
              name: $t("broker_buyer")
            },
            {
              id: "broker_seller",
              name: $t("broker_seller")
            },]
          }
        },
        fine_amount_outsystem: {
          label: "--fine_amount_outsystem",
          type: "number",
          required: true,
          group: "{arbitr_voting}/current",
          description: "fine_amount_outsystem.desc",
          order: 3,
          bind: {
            type: () => (this.rights.save || this.rights.save) ? "number" : "static",
            hidden: () => {
              const commission_decision = this?.commission_decision?.code || this?.commission_decision || "undefined";
              return commission_decision != "fine_for_non_registration_contract";
            }
          }
        },
        remark: {
          label: "--arbitr_notes",
          type: "text",
          group: "{arbitr_voting}/current",
          order: 3,
          attr: {
            react: true
          },
          bind: {
            type: () => this.rights.save ? "text" : "static",
            hidden: '!datetime_commission'
          },
        },
        consensus: {
          label: "--consensus",
          type: "static",
          group: "{arbitr_voting}/current",
          order: 3,
          attr: { react: true },
          bind: {
            hidden: '!datetime_commission',
            description: 'consensus_description'
          },
        },
        votes: {
          type: "data-grid",
          readonly: true,
          group: "{arbitr_voting}/current",
          order: 3,
          label: "!votes",
          attr: {
            summary: false,
            columns: [
              { field: "index", label: "№" },
              { field: "date", type: "date" },
              { field: "name", style: "width: 100%; text-align: left;" },
              {
                field: "vote", style: "text-align: right;", component: {
                  props: ['item'],
                  template: `
                  <div :title='item.reason'>{{$t(item.vote)}}</div>
                `
                }
              }
            ]
          },
        }
      }),
      ...group("{arbitr_voting}/history", {
        ...this.vote_history.map((props, i) => vote_history(props, i + 1))
      }),
      ...group(null, {
        banner: {
          type: "widget",
          order: 4,
          label: "--",
          hidden: () => {
            return this.has_save_data;
          },
          widget: {
            name: "ui-alert",
            props: {
              type: "warning",
            },
            content: $t('save_data_warning')
          }
        },
        actions_model: {
          type: "model",
          order: 4,
          label: "--",
          fields: {
            ...group("-!actions", {
              base_actions: {
                type: "action",
                label: "!",
                buttons: true,
                attr: {
                  style: "min-width: 0; flex: 0 0 auto;"
                },
                bind: {
                  // readonly: "!parent.has_save_data"
                },
                actions: [
                  { name: "send_to_arbitration", label: 'send_to_arb_committee' },
                  { name: "review", label: 'take_under_consideration' },
                  { name: "end_if_more_positive", label: "end_if_more_positive", required_data: true },

                  "start_voting",
                  { name: "vote_for", label: 'voted_positively' },
                  { name: "vote_against", label: 'voted_negatively' },
                  { name: "sign", label: 'subscribe' },
                ].map(action => {
                  if (typeof action == 'string') action = { name: action };

                  let { name, required_data = true, type = 'primary', label = action.name } = action;

                  return {
                    label: label,
                    btn_type: type,
                    question: $t('question.call.action'),
                    hidden: () => !this.rights[name] || (required_data && !this.has_save_data),
                    handler: async () => {
                      let { error } = await this.action(name);

                      if (error) {
                        Vue.Dialog.MessageBox.Error(error);
                        return { error };
                      }

                      await this.update_data();
                    }
                  };
                }),
              },
              voting_actions: {
                type: "action",
                label: "!",
                buttons: true,
                attr: {
                  style: "min-width: 0; flex: 1 0 auto;"
                },
                bind: {
                  //readonly: "!parent.has_save_data"
                },
                actions: [
                  ...[
                    { name: "reject", label: 'reject_consideration', required_data: false },
                    { name: "return", label: 'return_to_revision', required_data: false },
                    { name: "postpone", label: 'arbitr_postpone', required_data: false },
                    { name: "revoke", label: 'arb_revoke', required_data: false },
                    { name: "revisit", label: 'arb_revisit', required_data: false },
                    { name: "end_if_more_negative", label: "end_if_more_negative", required_data: false },
                    { name: "delete", label: 'arb_delete', required_data: false, type: "danger" }
                  ].map(action => {
                    if (typeof action == 'string') action = { name: action };

                    let { name, required_data = true, type = 'warning', label = action.name } = action;

                    return {
                      label: label,
                      btn_type: type,
                      question: $t('question.call.action'),
                      hidden: () => !this.rights[name] || (required_data && !this.has_save_data),
                      handler: async () => {
                        let { error } = await this.action(name);

                        if (error) {
                          Vue.Dialog.MessageBox.Error(error);
                          return { error };
                        }

                        await this.update_data();
                      }
                    };
                  })],
              },
              save_actions: {
                type: "action",
                label: "!",
                buttons: true,
                bind: {
                  //readonly: "parent.has_save_data"
                },
                attr: {
                  style: "min-width: 0; flex: 0 1 auto;",
                  onHandler: (wait) => {
                    this.wait = wait;
                  }
                },
                actions: [
                  {
                    type: "request",
                    method: "render_free_report",
                    label: "download_protocol",
                    host: "report",
                    download: `${this.number}_protocol.pdf`,
                    btn_type: "success",
                    params: {
                      type: "pdf",
                      template: "arbitration_complaint_base",
                      ref: "arbitration_complaint",
                      params: {
                        complaint_number: this.number
                      }
                    },
                    hidden: () => {
                      return !this.commission_decision || !this.has_save_data;
                    },
                  },
                  {
                    label: "save_changes",
                    hidden: () => {
                      return !(this.rights.save || this.rights.save) || this.has_save_data;
                    },
                    handler: async () => {
                      if (!await this.save()) return;
                      await this.update_data();
                    }
                  }
                ]
              }
            })
          }
        },
        debug_buttons: {
          order: 10000,
          label: "!",
          type: "widget",
          widget: {
            name: {
              computed: {
                rights_list: () => $self.rights,
              },
              template: `
              <template v-if="$develop.access_debug">
                <details style="padding: 7px 0px;" open>
                  <summary style="padding-bottom: 5px;">Permission list (only for dev/qa): </summary>
                  <span v-for="(v, key) in rights_list" style="padding-left: 8px; padding-right: 8px; margin: 0px" class="ui-tag-item" :style='{color: v  ? "green" : "rgb(204, 60, 60)"}'>
                    {{ key }}
                  </span>
                </details>
              </template>
            `
            },
          }
        }
      })
    };
  }

  initiator_field(initiator) {
    if (initiator == "system") return {
      type: 'static',
      label: "--name",
      value: "Система",
      readonly: true,
    };

    return {
      order: 1,
      type: 'model',
      label: "!",
      //readonly: true,
      fields: {
        full_name: {
          label: "--name",
          type: "static",
        },
        email: {
          label: "--email",
          type: "static"
        },
        phone: {
          label: "--phone",
          type: "static"
        },
        tin: {
          label: "--company_inn",
          type: "static"
        },
        address: {
          label: "--address",
          type: "static"
        }
      }
    };
  }

  async update_data() {
    this.wait = true;
    let { error, data } = await Http.api.rpc('contract_detailed', {
      number: this.number,
    });

    this.wait = false;

    if (error) {
      Vue.Dialog.MessageBox.Error(error);
      return { error };
    }

    this.update_context(data);
  }

  static async get(id) {
    let { error, data } = await Http.api.rpc('contract_detailed', { number: id });

    if (error) return { error };

    return { data: new ArbitrationComplaint(data) };
  }
}
