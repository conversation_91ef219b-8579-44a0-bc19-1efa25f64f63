export function getActionsProps(contractBody) {
  return {
    actions: {
      type: "action",
      label: "!",
      buttons: true,
      actions: [
        {
          label: "Save",
          hidden: () => {
            return (
              !contractBody.contract.rights
              || (
                !contractBody.contract.rights.set_contract_data
                && !contractBody.contract.rights.set_contragent_data
                && !contractBody.contract.rights.set_org_requisites
                && !contractBody.contract.rights.set_part_requisites
              )
            )
          },
          handler: async () => {
            await contractBody.save(["base_data", "participants", "org_requisites", "part_requisites"]);
          }
        }
      ]
    }
  };
}
