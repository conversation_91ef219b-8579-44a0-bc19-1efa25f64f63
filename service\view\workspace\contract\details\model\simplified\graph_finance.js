import { Entity } from '@iac/data';
import { Settings } from '@iac/kernel';
import { validate, validate_advance } from './utils';

export default class SimplifiedContractGraphFinance extends Entity {
  constructor(context) {
    super(context);

    this.date = context.date;
    this.summa = context.summa;
    this.advance_payment = context.advance_payment || 0;
    this.currency = context.currency || Settings._default_currency;
    this.kls = context.kls;
    this.expense_item_code = context.expense_item_code;
    this.is_gos = context.is_gos;
    this.proc_type = context.proc_type || 'simplified_contract';
    this.claim_accounts = context.claim_accounts || [];
  }

  props() {
    return {
      date: {
        type: "model",
        label: "!",
        fields: {
          month: {
            type: "entity",
            dataSource: "ref_months",
            group: "<date>",
            required: true,
          },
          year: {
            type: "number",
            group: "<date>",
            max: 2100,
            required: true,
          }
        }
      },
      kls: {
        label: "bank_account",
        type: "entity",
        required: this.is_gos,
        hidden: !this.is_gos,
        dataSource: this.claim_accounts,
      },
      expense_item_code: {
        group: '!type-',
        type: "entity",
        dataSource: "ref_expense_item",
        required: this.is_gos,
        hidden: !this.is_gos
      },
      summa: {
        group: "<summa>",
        type: "float",
        required: true,
        attr: { react: true },
        validate: validate
      },
      currency: {
        group: "<summa>",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true,
        required: true,
      },
      advance_payment: {
        type: "float",
        label: "advance_payment_amount",
        required: false,
        attr: { react: true },
        validate: validate_advance
      },
    }
  }
}
