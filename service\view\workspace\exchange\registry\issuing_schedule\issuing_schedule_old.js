import { DataSource, RefStore, Entity } from '@iac/data'
import { Http, Language } from '@iac/core';
import { Context } from '@iac/kernel'


const query = () => {
    return {        
        plan_date_from_gte: {
            group: "issuing_schedule.plan_date_from",
            type: "date",
            label: "from",
            has_del: true,
            bind: {
                status: `plan_date_from_error && {"type":"error"}`
            }
        },
        plan_date_from_lte: {
            group: "issuing_schedule.plan_date_from",
            type: "date",
            label: "to",
            has_del: true,
            bind: {
                status: `plan_date_from_error && {"type":"error"}`
            }
        },
        plan_date_from_error:  {
            sync: false,
            group: "issuing_schedule.plan_date_from",
            type: "model",
            label: "!",
            bind: {
                value: "plan_date_from_gte > plan_date_from_lte",
                status: `plan_date_from_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
            },
        },
        plan_date_to_gte: {
            group: "issuing_schedule.plan_date_to",
            type: "date",
            label: "from",
            has_del: true,
            bind: {
                status: `plan_date_to_error && {"type":"error"}`
            }
        },
        plan_date_to_lte: {
            group: "issuing_schedule.plan_date_to",
            type: "date",
            label: "to",
            has_del: true,
            bind: {
                status: `plan_date_to_error && {"type":"error"}`
            }
        },
        plan_date_to_error:  {
            sync: false,
            group: "issuing_schedule.plan_date_to",
            type: "model",
            label: "!",
            bind: {
                value: "plan_date_to_gte > plan_date_to_lte",
                status: `plan_date_to_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
            },

        },

    }
}

class ContractsSelector extends Entity {
    constructor(context) {
        super(context);
        this.contract = undefined
    }

    props() {
        return {
            contract: {
                label: "which_contract_for_schedule",
                type: "entity",
                dataSource: new DataSource({
                    valueExp: ["id", "checkGroup", "amount_of_good_in_lot"],
                    store: new RefStore({
                        method: "ref",
                        ref: "ref_exchanges_contracts",
                        injectQuery: (params) => (params.filters = { ...params.filters, status: 'approved' }, params),
                        context: (context) => {
                            context.contracts = context.claim_ids?.length && DataSource.get(context.claim_ids)
                            context.name = `${context.name} (${context.id})`
                            context.desc = `${context.amount_of_good_in_lot} ${context.unit}`
                            return context
                        }
                    })
                })
            }
        }
    }
}

class IssuingSchedule extends Entity {
    constructor(context = {}) {
        super(context)
        this.idArray = context.idArray;
    }

    validate_date() {
        this.properties.plan_date_from.status = undefined
        this.properties.plan_date_to.status = undefined

        if (this.plan_date_from && new Date(this.plan_date_from) <= new Date()) {
            this.properties.plan_date_from.status = {
                type: "error",
                message: Language.t("select_date_not_today")
            }
            return true;//plan_date_release
        }

        if (this.plan_date_from && this.plan_date_to && new Date(this.plan_date_from) > new Date(this.plan_date_to)) {
            this.properties.plan_date_to.status = {
                type: "error",
                message: Language.t("warning_end_date")
            }
            return true;
        }
    }

    props() {
        return {
            plan_date_from: {
                label: "schedule_start_date",
                type: "date",
                onChange: () => {
                    this.validate_date();
                }
            },
            plan_date_to: {
                label: "schedule_end_date",
                type: "date",
                onChange: () => {
                    this.validate_date();
                }
            },
            claim_ids: {
                label: "selling_requests",
                value: this.idArray,
                type: "static"
            },
            files: {
                label: "attached_files",
                type: "file",
                multiple: true,
                required: true,
            }
        }
    }

    async loadFiles() {
        if (this.files && this.files.length > 0) {
            const errorData = [];
            for (let index in this.files) {
                let fieldData = this.files[index];
                if (!fieldData.file)
                    continue;
                let formData = new FormData();
                formData.append('scope_tender_participant', this.proc_id);
                formData.append('data', fieldData.file, fieldData.file.name);

                let { data, error } = await Http.upload.form('tender/attach', formData);
                if (error) {
                    errorData.push({ i: index, message: error.message })
                } else {
                    this.files[index] = {
                        id: data.uuid,
                        name: data.meta.name,
                        meta: {
                            "type": data.meta.type,
                            "content_type": data.meta.content_type,
                            "type_group": data.meta.group,
                            "size": data.meta.size
                        }
                    }
                }
            }

            if (errorData && errorData.length > 0) {
                this.properties.files.status = {
                    type: "error",
                    data: errorData
                }
            }
        }
    }

    async create() {
        if (this._properties.plan_date_from.status || this._properties.plan_date_to.status || this.validate())
            return {};

        await this.loadFiles()
        const dataForSending = {
            files: (this.files || []).map(item => item.id),
            plan_date_from: this.plan_date_from,
            plan_date_to: this.plan_date_to,
            claim_ids: this.claim_ids
        }
        return await Http.api.rpc("ref_schedule", {
            ref: "exc_schedule_pgv",
            op: "create",
            data: dataForSending
        })
    }
}

export default {
    data() {
        return {
            IssuingScheduleDataSource: new DataSource({
                store: new RefStore({
                    method: "ref_schedule",
                    ref: "exc_schedule_pgv",
                    context: context => {
                        context.actions = [
                            {
                                label: "delete",
                                hidden: () => {
                                    return !(Context.Access.policy['exc_pgv_delete'])
                                },
                                handler: async () => {
                                    const { error, data } = await Http.api.rpc('ref_schedule', {
                                        ref: "exc_schedule_pgv",
                                        op: "delete",
                                        data: { id: context.id }
                                    });
                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error)
                                    } else {
                                        this.IssuingScheduleDataSource.reload()
                                    }
                                }
                            }
                        ]
                        return context
                    },
                    injectQuery: params => {
                        params.filters = params.filters || {};
                        params.filters.plan_date_to_error = undefined;
                        params.filters.plan_date_from_error = undefined;
                        return params;
                    },
                }),            
                query: query(),
                actions: [
                    {
                        label: 'add',
                        hidden: () => {
                            return !(Context.Access.policy['exc_pgv_create'])
                        },
                        handler: async () => {
                            const contractSelection = await Vue.Dialog.MessageBox.Form({ fields: (new ContractsSelector()).fields }, Language.t("select_contract_from_schedule"))

                            const { contract } = contractSelection ?? {}
                            if (!contract) {
                                Vue.Dialog.MessageBox.Success("Не выбран договор.")
                                return
                            }
                            const idArray = [contract.id]
                            const model = new IssuingSchedule({ idArray })
                            Vue.Dialog({
                                props: ["model"],
                                methods: {
                                    async save() {
                                        await this.wait(async e => {
                                            const { error, data } = await this.model.create()
                                            if (error) {
                                                Vue.Dialog.MessageBox.Error(error);
                                            } else if (data && data.id != undefined) {
                                                Vue.Dialog.MessageBox.Success(Language.t("selling_plan_created"))
                                                this.Close()
                                            }
                                        })
                                    }
                                },
                                template: `
                                  <div>
                                    <header>{{$t("create_schedule")}}</header>
                                    <main>
                                        <ui-layout :fields='model.fields' />
                                    </main>
                                    <footer>
                                        <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                                        <ui-btn type='primary' v-on:click.native='save'>{{$t('Create')}}</ui-btn>
                                    </footer>
                                  </div>
                                `
                            }).Modal({ model })
                        }
                    }
                ],
                template: "template-issuing_schedule"
            })
        }
    },
    template: `
        <ui-data-view :dataSource='IssuingScheduleDataSource'/>
    `
}