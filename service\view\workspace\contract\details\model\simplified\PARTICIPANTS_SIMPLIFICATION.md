# Упрощение логики выбора компании поставщика

## 🎯 **Цель:**
Упростить логику выбора компании поставщика в упрощенных договорах, оставив только:
1. **Выбор компании из списка**
2. **Добавление компании вручную**

## 🔧 **Изменения в `participants.js`:**

### **Убрали лишние поля:**

#### ❌ **Удалено:**
```javascript
small_business: {
  type: "bool",
  label: "small_business",
},
```

### **Упростили условия отображения:**

#### **1. Поле `custom_company` (переключатель "Добавить компанию вручную"):**
```javascript
// Было:
hidden: () => {
  return (
    contractBody.readonly
    || !contractBody.base_data.contract_type
    || contractBody.base_data.contract_type == 1  // ← Убрали эту проверку
  )
},

// Стало:
hidden: () => {
  return contractBody.readonly || !contractBody.base_data.contract_type
},
```

#### **2. Поле `company` (выбор из списка компаний):**
```javascript
// Было:
hidden: () => {
  return (
    !contractBody.base_data.contract_type
    || contractBody.base_data.contract_type == 1  // ← Убрали эту проверку
    || contractBody.participants.contragent?.custom_company
  )
},

// Стало:
hidden: () => {
  return (
    !contractBody.base_data.contract_type
    || contractBody.participants.contragent?.custom_company
  )
},
```

#### **3. Поле `custom` (ручной ввод данных компании):**
```javascript
// Было:
hidden: () => {
  return (
    !contractBody.base_data.contract_type
    || contractBody.base_data.contract_type == 1  // ← Убрали эту проверку
    // || !contractBody.participants.contragent?.custom_company  // ← Раскомментировали
  )
},

// Стало:
hidden: () => {
  return (
    !contractBody.base_data.contract_type
    || !contractBody.participants.contragent?.custom_company
  )
},
```

#### **4. Поле `email`:**
```javascript
// Было:
hidden: () => {
  return (
    contractBody.base_data.contract_type != 1  // ← Убрали эту проверку
    && !contractBody.participants.contragent?.custom_company
    && contractBody.participants.contragent?.company?.email
  )
},

// Стало:
hidden: () => {
  return (
    !contractBody.participants.contragent?.custom_company
    && contractBody.participants.contragent?.company?.email
  )
},
```

### **Исправили dataSource:**
```javascript
// Было:
dataSource: "ref_area",

// Стало:
dataSource: "ref_area_",  // Приведено в соответствие с custom_body.js
```

## ✅ **Результат:**

### **Упрощенная логика работы:**

1. **По умолчанию** отображается поле выбора компании из списка
2. **Пользователь может включить** переключатель "Добавить компанию вручную"
3. **При включении переключателя:**
   - Скрывается поле выбора из списка
   - Показываются поля для ручного ввода данных компании
4. **Поле email** показывается:
   - Всегда при ручном вводе
   - При выборе из списка, если у компании нет email

### **Убранные сложности:**
- ❌ Поле "Малый бизнес" (не нужно для упрощенных договоров)
- ❌ Проверки на `contract_type == 1` (единый поставщик - не актуально для упрощенных)
- ❌ Сложная логика скрытия полей

### **Преимущества:**
- ✅ **Простая и понятная логика** для пользователя
- ✅ **Меньше условий** в коде
- ✅ **Соответствие концепции** упрощенных договоров
- ✅ **Единообразие** с основной логикой в custom_body.js

## 🧪 **Для тестирования:**

1. **Откройте упрощенный договор**
2. **Перейдите в раздел "Участники"**
3. **Проверьте, что по умолчанию** показывается поле выбора компании из списка
4. **Включите переключатель** "Добавить компанию вручную"
5. **Убедитесь, что:**
   - Поле выбора из списка скрылось
   - Появились поля для ручного ввода данных компании
6. **Выключите переключатель** и убедитесь, что все вернулось к исходному состоянию

## 🎉 **Готово!**

Логика выбора компании поставщика в упрощенных договорах теперь максимально простая и понятная.
