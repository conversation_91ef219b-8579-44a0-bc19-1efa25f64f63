import { DataSource, RefStore, ArrayStore, Query, Entity } from '@iac/data'
import { Http, Action, Language } from '@iac/core'
import Position from './position'
import { Context, Settings, Config } from '@iac/kernel'

import Router from '../../../router';

import './_dlg_add_position';

let pb = false;

export default {
    data: function () {
        let $this = this;
        return {
            dataSourcePgz: new DataSource({
                template: 'template-pgz',
                query: {
                    plan_date_from_gte: {
                        group: "pgz.date_start",
                        type: "date",
                        label: "!",
                        prefix: this.$t("from"),
                        has_del: true,
                        bind: {
                            status: `date_start_error && {"type":"error"}`
                        },
                    },
                    plan_date_from_lte: {
                        group: "pgz.date_start",
                        type: "date",
                        label: "!",
                        prefix: this.$t("to"),
                        has_del: true,
                        bind: {
                            status: `date_start_error && {"type":"error"}`
                        },
                    },
                    plan_date_to_gte: {
                        group: "pgz.date_end",
                        type: "date",
                        label: "!",
                        prefix: this.$t("from"),
                        has_del: true,
                        bind: {
                            status: `date_end_error && {"type":"error"}`
                        },
                    },
                    plan_date_to_lte: {
                        group: "pgz.date_end",
                        type: "date",
                        label: "!",
                        prefix: this.$t("to"),
                        has_del: true,
                        bind: {
                            status: `date_end_error && {"type":"error"}`
                        },
                    },
                    date_start_error: {
                        sync: false,
                        group: "pgz.date_start",
                        type: "model",
                        label: "!",
                        bind: {
                            value: "plan_date_from_gte > plan_date_from_lte",
                            status: `date_start_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
                        },
                    },
                    date_end_error: {
                        sync: false,
                        group: "pgz.date_end",
                        type: "model",
                        label: "!",
                        bind: {
                            value: "plan_date_to_gte > plan_date_to_lte",
                            status: `date_end_error && {"type":"error","message":"${this.$t('to_from_error')}"}`
                        },
                    },
                },
                form: {
                    fields: {
                        title: {
                            required: true,
                            bind: {
                                suffix: `((title || '').length)+'/250'`
                            },
                            attr: {
                                predicate: '^.{0,250}$'
                            },
                        },
                        plan_date_from: {
                            type: "date",
                            label: "pgz.date_from",
                            has_del: true,
                            required: true
                        },
                        plan_date_to: {
                            type: "date",
                            label: "pgz.date_to",
                            has_del: true,
                            required: true,
                            validate(status) {
                                if (!status)
                                    return

                                return this.model.plan_date_from >= this.model.plan_date_to && Language.t("warning_end_date")
                            }
                        },
                        files: {
                            label: "attached_files",
                            type: "file",
                            required: true,
                            multiple: true,
                        }
                    },
                    actions: {
                        create: {
                            label: "add",
                            policy: "schedule_create",
                            reload: true,
                        },
                        update: "schedule_create",
                        delete: {
                            policy: "schedule_create",
                            question: Language.t("question_do_you_really_want_to_delete_this_record"),

                        },
                    }
                },
                store: {
                    method: 'ref_schedule',
                    ref: 'schedule_items_pgz',
                    injectQuery: params => {
                        params.filters = params.filters || {};
                        params.filters.date_start_error = undefined;
                        params.filters.date_end_error = undefined;
                        return params;
                    },
                },
            }),
            dataSource: new DataSource({
                template: "template-purchase",
                valueExp: ["id", "month_begin_end"],
                actions: [
                    {
                        get label() {
                            let count = $this.dataSource.checkedItems.length
                            return `${count} ${Language.t("create_procedure")}`

                        },
                        icon: "check2",
                        hidden: () => {
                            if (!Context.Access.Develop.create_lots_procedures && !Settings.procedures?._multilot)
                                return true

                            if (!Context.Access.policy["tender_create"] && !Context.Access.policy["reduction_create"])
                                return true;

                            return this.dataSource.checkedItems.length <= 1
                        },
                        handler: async () => {

                            let procedure_params = {
                                /*object: {
                                    type: "new_purchase",
                                    id: context.id
                                }*/
                                source: this.dataSource.checkedItems.map((item) => {
                                    return {
                                        type: "purchase",
                                        id: item.id
                                    }
                                })
                            }

                            let response = await Action["procedure.create"](procedure_params);
                            if (!response)
                                return;
                            let { error, data } = response;
                            if (!error) {
                                Router.push({ path: `/procedure/${data[0].proc_id}/core` });
                            }
                        }
                    },
                    {
                        label: 'add_procurement',
                        hidden: () => {
                            if (!Context.Access.policy["schedule_create"])
                                return true;
                        },
                        handler: () => {
                            this.add_position();
                        }
                    }
                ],
                query: new Query({
                    year: {
                        type: "entity",
                        group: "year",
                        label: "!current_year",
                        dataSource: new DataSource({
                            store: new ArrayStore({
                                keyType: 'number',
                                data: async () => {
                                    let { error, data } = await Http.api.rpc("ref_schedule", {
                                        'ref': 'schedule_position',
                                        'op': 'get_years'
                                    })
                                    if (!error && data && Array.isArray(data)) {
                                        return data.sort().map((year) => {
                                            return {
                                                id: year,
                                                name: year,
                                            }
                                        })
                                    }
                                    return []
                                }
                            })
                        }),
                        has_del: true,
                    },
                    month: {
                        type: "entity",
                        group: "month",
                        label: "!select_month",
                        dataSource: DataSource.get("ref_months"),
                        has_del: true,
                    },
                    status: {
                        group: "status",
                        label: "!status",
                        type: "entity",
                        has_del: true,
                        dataSource: ["draft", "published"],
                        onChange: function () {
                            this.model.has_proc = undefined;
                            this.model.proc_type = undefined;
                        }
                    },
                    has_proc: {
                        group: "status",
                        label: "!procedure_existing",
                        type: "entity",
                        has_del: true,
                        dataSource: [{ id: 'no', name: Language.t("positions_without_procedures") }, { id: 'yes', name: Language.t("positions_with_procedures") }],
                        hidden: function () {
                            let status = this.model.status;
                            if (!status || !status.id || status.id != "published")
                                return true;
                        },
                        onChange: function () {
                            this.model.proc_type = undefined;
                        }
                    },
                    proc_type: {
                        type: 'enum',
                        group: "status",
                        label: "!",
                        dataSource: (() => {
                            if (Settings._country == 'KG') {
                                return ["selection", "reduction"]
                            } else {
                                const statuses = ["tender", "selection", "reduction", "master_agreement"]
                                Context.Access.Develop.custom_contract_develop ? statuses.push("custom_contract") : undefined
                                return statuses
                            }
                        })(),
                        hidden: function () {
                            let status = this.model.status;
                            let has_proc = this.model.has_proc;

                            if (!status || !status.id || status.id != "published")
                                return true;

                            if (!has_proc || !has_proc.id || has_proc.id != "yes")
                                return true;

                        }
                    }

                    // proc_type:
                    //      undefined - отображаем все
                    //      null - пгз у которых нет процедур
                    //      [tender, selection, ad, reduction] - есть процедуры
                    //      [tender] - позиции по которым создан тендер

                    /*type: {
                        type: "entity",
                        group: "type",
                        label: "!type",
                        dataSource: [
                            { id: 'position', name: 'type.position' },
                            { id: 'project', name: 'type.project' },
                            { id: 'category', name: 'type.category' },
                        ],
                        has_del: true,
                    },*/
                }),
                store: new RefStore({
                    key: 'id',
                    method: 'ref_schedule',
                    ref: 'schedule_position',
                    injectQuery: params => {
                        if (!params.filters.has_proc) {
                            params.filters.proc_type = undefined
                        } if (params.filters.has_proc == 'no') {
                            params.filters.proc_type = null
                        } if (params.filters.has_proc == 'yes' && !params.filters.proc_type) {
                            params.filters.proc_type = [];
                        }
                        delete params.filters.has_proc

                        return params
                    },
                    context: (context) => {
                        context.month_begin_end = `${context.month_begin}_${context.month_end}`
                        
                        Object.defineProperty(context, "checkbox", {
                            configurable: true,
                            enumerable: true,
                            get: () => {

                                if ((!Context.Access.Develop.create_lots_procedures && !Settings.procedures?._multilot) || context.status != 'published' || context.proc_id)
                                    return false

                                if (!Context.Access.policy["tender_create"] && !Context.Access.policy["reduction_create"])
                                    return false;

                                if(this.dataSource.checkedItems.length > 0 && this.dataSource.checkedItems?.[0]?.month_begin_end != context.month_begin_end){
                                    return false;
                                }
                                
                                return true;
                            },
                        });

                        context.actions = [
                            {
                                label: 'action.publish_procedure',
                                handler: () => {
                                    this.public_position([context.id])
                                },
                                hidden: () => {
                                    if (!Context.Access.policy["schedule_create"])
                                        return true;
                                    if (!context.count || context.status != 'draft' || context.proc_id)
                                        return true;
                                }
                            },
                            {
                                get label() {
                                    return (context.status == "published" || context.proc_id || !Context.Access.policy["schedule_create"]) ? 'view' : 'edit';
                                },
                                //hidden: () => {

                                //    return !Context.Access.policy["schedule_create"];
                                // },
                                handler: () => {
                                    this.$router.push({ path: `/workspace/purchase/${context.id}` })
                                }
                            },
                            {
                                get label() {
                                    return 'create_procedure'
                                },
                                hidden: () => {
                                    if (!Context.Access.policy["tender_create"] && !Context.Access.policy["reduction_create"])
                                        return true;
                                    return (!context.count || context.status != 'published' || context.proc_id) ? true : false
                                },
                                handler: async () => {
                                    let procedure_params = {
                                        object: {
                                            type: "new_purchase",
                                            id: context.id
                                        }
                                    }

                                    let response = await Action["procedure.create"](procedure_params);
                                    if (!response)
                                        return;
                                    let { error, data } = response;
                                    if (!error) {
                                        Router.push({ path: `/procedure/${data[0].proc_id}/core` });
                                    }

                                }
                            },
                            {
                                get label() {
                                    return 'contract.create'
                                },
                                hidden: () => {
                                    if (!Context.Access.Develop.custom_contract_develop)
                                        return true;

                                    if (!Context.Access.policy["contract_create"])
                                        return true;

                                    return (!context.count || context.status != 'published' || context.proc_id) ? true : false
                                },
                                handler: async () => {
                                    let procedure_params = {
                                        object: {
                                            type: "new_purchase",
                                            id: context.id
                                        }
                                    }

                                    let response = await Action["contract.create"](procedure_params);
                                    if (!response)
                                        return;
                                    let { error, data: id } = response;
                                    if (error) {
                                        Vue.Dialog.MessageBox.Error(error)
                                    }else if(id){
                                        Router.push({ path: `/workspace/contract/${id}/core` });
                                    }

                                }
                            },
                            {
                                get label() {
                                    return 'Договор упрощённой закупки'
                                },
                                hidden: () => {
                                    if (!Context.Access.Develop.custom_contract_develop)
                                        return true;

                                    if (!Context.Access.policy["contract_create"])
                                        return true;

                                    return (!context.count || context.status != 'published' || context.proc_id) ? true : false
                                },
                                handler: async () => {
                                    let procedure_params = {
                                        object: {
                                            type: "new_purchase",
                                            id: context.id
                                        },
                                        contract_type: 'simplified_contract',
                                        buyer_type_id: Context.User.buyer_type_id
                                    }

                                    let response = await Action["contract.create"](procedure_params);
                                    if (!response)
                                        return;
                                    let { error, data: id } = response;
                                    if (error) {
                                        // Проверяем, если это ошибка превышения БРВ
                                        if (error.code === 'BRV_LIMIT_EXCEEDED') {
                                            Vue.Dialog.MessageBox.Error(error.message);
                                        } else {
                                            Vue.Dialog.MessageBox.Error(error);
                                        }
                                    }else if(id){
                                        Router.push({ path: `/workspace/contract/${id}/core` });
                                    }

                                }
                            },
                            {
                                label: "Тонкая настройка",
                                hidden: () => {
                                    return !Context.Access.policy.system_entity_editor
                                },
                                handler: async () => {
                                    let { data, error } = await Http.api.rpc("debug_panel", {
                                        action: "get_entity",
                                        id: context.id,
                                        _: "%{table: \"positions\", service: :common, module: MsPurchaseList.Repo}"
                                    })
                                    if (error && error.code != "AbortError") {
                                        Vue.Dialog.MessageBox.Error(error);
                                    }
                                }
                            },
                            {
                                type: 'sep'
                            },
                            {
                                label: 'delete',
                                hidden: () => {
                                    if (!Context.Access.policy["schedule_create"])
                                        return true;
                                    return (context.status != 'draft' || context.proc_id) ? true : false
                                },
                                handler: async () => {
                                    if (await Vue.Dialog.MessageBox.Question(Language.t('question_delete_position')) != Vue.Dialog.MessageBox.Result.Yes) {
                                        return;
                                    }

                                    let { error, data } = await Http.api.rpc("ref_schedule", {
                                        ref: "schedule_position",
                                        op: "delete",
                                        filters: {
                                            id: context.id
                                        }
                                    })

                                    if (error) {
                                        return await Vue.Dialog.MessageBox.Error(error)
                                    }


                                    this.dataSource.reload();
                                }
                            }
                        ]
                        return context;
                    }
                })
            })
        }
    },
    methods: {
        async public_position(id = []) {
            if (await Vue.Dialog.MessageBox.Question(Language.t('publish_question')) != Vue.Dialog.MessageBox.Result.Yes) {
                return;
            }

            let { error, data } = await Http.api.rpc("ref_schedule", {
                ref: "schedule_position",
                op: "set_status",
                filters: {
                    id: id
                },
                data: {
                    status: "published"
                }
            })

            if (error) {
                return await Vue.Dialog.MessageBox.Error(error)
            }

            //this.dataSource.reload();

            this.dataSource.checkedItems = []

            this.dataSource.items.forEach((item) => {
                if (id.indexOf(item.id) >= 0) {
                    item.status = "published";
                }
            })

        },
        async add_position() {
            let id = await Vue.Dialog.PurchaseAddPosition.Modal({
                size: 'lg'
            })

            if (id) {
                this.$router.push({ path: `/workspace/purchase/${id}` })
            }
        },
        showProducts(id) {
            this.$wait(async () => {
                const { data, error } = await Position.get(id);
                if (error !== undefined) {
                    await Vue.Dialog.MessageBox.Error(error);
                    return;
                }
                await data.items.load();

                const { items } = data.items;

                Vue.Dialog.products.Modal({
                    size: 'right',
                    model: {
                        title: data.name,
                        count: items.length,
                        items,
                    },
                });
            });
        },
    },
    template: `
        <iac-access :access='$policy.schedule_create || $policy.schedule_sign || $policy.schedule_list' class='page-purchase'>
            <iac-section type='header'>
                <ol class='breadcrumb'>
                    <li><router-link to='/'>{{$t('home')}}</router-link></li>
                    <li>{{$t('nav.purchase')}}</li>
                </ol>
                <div class='title'>
                    <h1 style='margin: 0;'>{{$t('nav.purchase')}}</h1>
                </div>
            </iac-section>
            <iac-section>
            <ui-layout-tab v-if='$policy.schedule_create || $policy.schedule_list'>
                <ui-layout-group label="pgz.announced">
                    <ui-alert v-if='!$route.query.year && $develop.purchase_develop' type='warning'>{{$t("purchase_year_warning")}}</ui-alert>
                    <ui-data-view :showCheckedItems='false' :dataSource='dataSource' />
                </ui-layout-group>
                <ui-layout-group v-if="$settings.purchase_list && $settings.purchase_list._approved" label="pgz.approved">
                <a href='https://hayotbirja.uz/files/2505/17530fa1-8d0c-4a7b-9331-3b1f24edc212.xlsx?ololo=123' style="margin-bottom: 10px;">{{$t('purchase_ref1')}}</a>
                    <ui-data-view :dataSource='dataSourcePgz' />
                </ui-layout-group>
            </ui-layout-tab>
            <ui-error v-else code='403' :message='$t("NoAccess")'  />
            </iac-section>
        </iac-access>
    `
}