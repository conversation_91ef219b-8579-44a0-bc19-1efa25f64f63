import { DataSource } from '@iac/data';
import { Language } from '@iac/core';
import { Context } from '@iac/kernel';
import IssuingScheduleOld from './issuing_schedule_old';


export default {
    components: {
        IssuingScheduleOld
    },
    props: ['scope'],
    data() {
        return {
            source: new DataSource({
                scope: this.scope,
                form: this.scope == 'private' && {
                    fields: {
                        name: {
                            required: true,
                            bind: {
                                suffix: `((name || '').length)+'/50'`
                            },
                            attr: {
                                predicate: '^.{0,50}$'
                            },
                        },    
                        product:{ 
                            required: true,
                            type: "entity",
                            dataSource: {
                                store: {
                                    ref: "ref_enkt_tree_products"
                                }
                            },
                        }, 
                        company_id: {
                            required: true,
                            type: "entity",
                            label: "company",
                            dataSource: {
                                search: true,
                                displayExp: 'title',
                                store: {
                                    method: 'company_ref',
                                    ref: 'companies',
                                    injectQuery: (params)=>{
                                        params.filters = {
                                            roles: ['exchanger']
                                        }
                                        return params;
                                    }
                                }
                            },
                            hidden: () => {
                                return !Context.Access.policy.system_pgv_create
                            }
                        },
                        meta: {
                            type: 'model',
                            label: '!',
                            fields: {
                                company_title: {
                                    type: 'hidden',
                                    bind: {
                                        value: "parent.company_id.title"
                                    }
                                }
                            },
                            hidden: () => {
                                return !Context.Access.policy.system_pgv_create
                            }
                        },
                        comment: {
                            //required: true,
                            type: "text",
                            description: "Небольше 200 символов",
                            bind: {
                                suffix: `((comment || '').length)+'/200'`
                            },
                            max: 200,
                            attr: {
                                predicate: '^.{0,200}$'
                            },
                        }, 
                        date_begin: {
                            required: true,
                            label: "schedule_start_date",
                            type: "date",
                            bind: {
                                status: `date_error && {"type":"error"}`,
                            }
                        },
                        date_end: {
                            required: true,
                            label: "schedule_end_date",
                            type: "date",
                            bind: {
                                status: `date_error && {"type":"error"}`,
                              }
                        },
                        date_error : {
                            type: 'info',
                            label: '!',
                            sync: false,
                            validate(status){
                                return this.value
                            },
                            bind: {
                              value: "date_begin > date_end",
                              status: `date_error && {"type":"error","message":"${Language.t('to_from_error')}"}`,
                            },
                          },
                        files: {
                            required: true,
                            label: "attached_files",
                            type: "file",
                            multiple: true,
                        }              
                    },
                    actions: {
                        create: {
                            label: "add",
                            policy: "exc_pgv_create",
                            reload: true,
                        },
                        update:{
                            policy: "exc_pgv_update",
                            reload:true,
                        },
                        delete: {
                            policy: "exc_pgv_delete",
                            question: Language.t("question_do_you_really_want_to_delete_this_record"),
                        },
                    }
                },
                store: {
                    method: "ref_pgv",
                    ref: "pgv",
                    injectQuery: (params) => {
                        params.filters = params.filters || {};
                        params.filters.date_error = undefined;

                        if(params.filters.date_begin_gte || params.filters.date_end_lte){
                            params.filters.date_overlaps = {
                                l: params.filters.date_begin_gte,
                                r: params.filters.date_end_lte
                            }
                            params.filters.date_begin_gte = undefined;
                            params.filters.date_end_lte = undefined;
                        }

                        if(this.scope === 'private' && !Context.Access.policy.system_pgv_create){
                            params.filters.company_id = Context.User.team_id;
                        }

                        return params;
                    },
                    context: (context) => {
                        context.product_name = context.product_spec.map(item => item.name).join(' / ');
                        return context;
                    }
                },
                template:`template-issuing_schedule_new`,  
                query: {
                    product: {
                        group: "Товар",
                        label:"!product",
                        type: "enum-tree",
                        dataSource: {
                            store: {
                                ref: "ref_enkt_tree_products"
                            }
                        }
                    },
                    company_id: {
                        group: "company",
                        label:"!company",
                        type: "entity",
                        has_del: true,
                        dataSource: {
                            search: true,
                            displayExp: 'title',
                            store: {
                                method: 'company_ref',
                                ref: 'companies',
                                injectQuery: (params)=>{
                                    params.filters = {
                                        roles: ['exchanger']
                                    }
                                    return params;
                                }
                            }
                        },
                        hidden: ()=>{
                            if(this.scope === 'private' && !Context.Access.policy.system_pgv_create){
                                return true;
                            }
                            return false;
                        }
                    },
                    date_begin_gte: {
                        group: "issuing_schedule.plan_date",
                        type: "date",
                        label: "!from",
                        prefix: Language.t('from'),
                        has_del: true,
                        bind: {
                            status: `date_error && {"type":"error"}`
                        }
                    },
                    date_end_lte: {
                        group: "issuing_schedule.plan_date",
                        type: "date",
                        label: "!to",
                        prefix: Language.t('to'),
                        has_del: true,
                        bind: {
                            status: `date_error && {"type":"error"}`
                        }
                    },

                    date_error:  {
                        sync: false,
                        group: "issuing_schedule.plan_date",
                        type: "model",
                        label: "!",
                        bind: {
                            value: "date_begin_gte > date_end_lte",
                            status: `date_error && {"type":"error","message":"${Language.t('to_from_error')}"}`
                        },
                    },
                }
            })
        }
    },
    template: `
    <iac-access :access='$policy.exc_pgv_read || scope != "private"'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.issuing_schedule')}}</li>
            </ol>
            <h1>{{$t('nav.issuing_schedule')}}</h1>
        </iac-section>
        <iac-section>
            <div v-if='$develop.new_issuing_schedule || $settings.exchange._new_issuing_schedule'>
            <ui-data-view :dataSource='source'/>
            </div>
            <div v-else>
                <IssuingScheduleOld />
            </div>
        </iac-section>
    </iac-access>
    `
};


