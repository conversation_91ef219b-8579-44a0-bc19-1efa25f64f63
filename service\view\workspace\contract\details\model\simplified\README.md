# Структура модулей упрощённых договоров

## Структура файлов

### Основные компоненты

- **`simplified_body.js`** - Главн<PERSON>й класс `SimplifiedContractBody` с основной логикой
- **`product.js`** - Класс `SimplifiedContractProduct` для редактирования продуктов
- **`view_product.js`** - Класс `SimplifiedContractViewProduct` для отображения продуктов
- **`graph.js`** - Класс `SimplifiedContractGraph` для графика источников финансирования
- **`graph_finance.js`** - Класс `SimplifiedContractGraphFinance` для детального графика финансирования
- **`property.js`** - Класс `SimplifiedContractBodyProperty` для кастомных свойств
- **`utils.js`** - Утилиты и вспомогательные функции

### UI Properties (папка `props/`)

Каждый файл в папке `props/` содержит функцию, которая возвращает конфигурацию UI для определённой секции:

- **`base_data.js`** - Базовые данные договора (название, даты, условия и т.д.)
- **`participants.js`** - Участники договора (инициатор, контрагент)
- **`requisites.js`** - Банковские реквизиты сторон
- **`actions.js`** - Действия (кнопки сохранения)
- **`graph.js`** - График источников финансирования
- **`graph_finance.js`** - Детальный график финансирования
- **`spec.js`** - Спецификация товаров/услуг
- **`index.js`** - Объединяет все props в один объект

## Принципы архитектуры

### 1. Разделение ответственности
- **Модели данных** - отдельные классы для каждого типа сущности
- **UI конфигурация** - отдельные файлы для каждой секции интерфейса
- **Утилиты** - вынесены в отдельный файл

### 2. Переиспользование кода
- Общие функции валидации в `utils.js`
- Базовые классы наследуются от `Entity`
- UI компоненты переиспользуются между секциями

### 3. Модульность
- Каждый файл имеет четкую ответственность
- Легко добавлять новые секции или модифицировать существующие
- Простое тестирование отдельных компонентов

## Использование

```javascript
// Импорт основного класса
import SimplifiedContractBody from './simplified_body';

// Создание экземпляра
const contractBody = new SimplifiedContractBody(context, contract);

// Получение UI конфигурации
const props = contractBody.props();
```

## Отличия от прямых договоров

1. **Флаг типа**: `is_simplified` вместо `is_gos`
2. **Упрощённые поля**: скрыты поля, специфичные для госзакупок
3. **Другие источники данных**: используются справочники для упрощённых закупок
4. **Адаптированная валидация**: под требования упрощённых процедур

## Расширение функциональности

### Добавление новой секции UI:
1. Создать файл в `props/new_section.js`
2. Экспортировать функцию `getNewSectionProps(contractBody)`
3. Добавить импорт и вызов в `props/index.js`

### Добавление нового типа модели:
1. Создать класс, наследующийся от `Entity`
2. Определить метод `props()` с конфигурацией полей
3. Добавить методы редактирования в основной класс

### Модификация валидации:
1. Добавить функции валидации в `utils.js`
2. Использовать в соответствующих props файлах
