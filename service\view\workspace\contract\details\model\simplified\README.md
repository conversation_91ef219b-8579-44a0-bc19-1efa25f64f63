# Структура модулей упрощённых договоров

## Структура файлов

### Основные компоненты

- **`simplified_body.js`** - Главн<PERSON>й класс `SimplifiedContractBody` с основной логикой
- **`product.js`** - Класс `SimplifiedContractProduct` для редактирования продуктов
- **`view_product.js`** - Класс `SimplifiedContractViewProduct` для отображения продуктов
- **`graph.js`** - Класс `SimplifiedContractGraph` для графика источников финансирования
- **`graph_finance.js`** - Класс `SimplifiedContractGraphFinance` для детального графика финансирования
- **`competitive_list.js`** - Класс `SimplifiedContractCompetitiveList` для конкурентного листа
- **`property.js`** - Класс `SimplifiedContractBodyProperty` для кастомных свойств
- **`utils.js`** - Утилиты и вспомогательные функции

### UI Properties (папка `props/`)

Каждый файл в папке `props/` содержит функцию, которая возвращает конфигурацию UI для определённой секции:

- **`base_data.js`** - Базовые данные договора (название, даты, условия и т.д.)
- **`participants.js`** - Участники договора (инициатор, контрагент)
- **`requisites.js`** - Банковские реквизиты сторон
- **`actions.js`** - Действия (кнопки сохранения)
- **`graph.js`** - График источников финансирования
- **`graph_finance.js`** - Детальный график финансирования
- **`competitive_list.js`** - Конкурентный лист поставщиков
- **`spec.js`** - Спецификация товаров/услуг
- **`index.js`** - Объединяет все props в один объект

## Принципы архитектуры

### 1. Разделение ответственности
- **Модели данных** - отдельные классы для каждого типа сущности
- **UI конфигурация** - отдельные файлы для каждой секции интерфейса
- **Утилиты** - вынесены в отдельный файл

### 2. Переиспользование кода
- Общие функции валидации в `utils.js`
- Базовые классы наследуются от `Entity`
- UI компоненты переиспользуются между секциями

### 3. Модульность
- Каждый файл имеет четкую ответственность
- Легко добавлять новые секции или модифицировать существующие
- Простое тестирование отдельных компонентов

## Использование

```javascript
// Импорт основного класса
import SimplifiedContractBody from './simplified_body';

// Создание экземпляра
const contractBody = new SimplifiedContractBody(context, contract);

// Получение UI конфигурации
const props = contractBody.props();
```

## Отличия от прямых договоров

1. **Тип процедуры**: `proc_type = 'simplified_contract'` для идентификации упрощённых закупок
2. **Сохранена логика `is_gos`**: определяет тип заказчика (государственный/корпоративный)
3. **Адаптированные поля**: поля показываются/скрываются в зависимости от типа заказчика
4. **Упрощённая валидация**: менее строгие требования по сравнению с прямыми договорами
5. **Поддержка обоих типов заказчиков**: как бюджетные, так и корпоративные организации

## Расширение функциональности

### Добавление новой секции UI:
1. Создать файл в `props/new_section.js`
2. Экспортировать функцию `getNewSectionProps(contractBody)`
3. Добавить импорт и вызов в `props/index.js`

### Добавление нового типа модели:
1. Создать класс, наследующийся от `Entity`
2. Определить метод `props()` с конфигурацией полей
3. Добавить методы редактирования в основной класс

### Модификация валидации:
1. Добавить функции валидации в `utils.js`
2. Использовать в соответствующих props файлах

## Конкурентный лист

### Описание
Конкурентный лист - обязательная секция для упрощённых договоров, где заказчик должен указать не менее трех поставщиков, у которых ранее запрашивал цены.

### Поля конкурентного листа:
- **Наименование поставщика** (обязательное)
- **ИНН организации поставщика** (обязательное, 9 цифр)
- **Цена** (обязательное, больше 0)
- **Валюта** (обязательное)
- **Курс пересчета** (автоматический для валют, отличных от UZS)
- **Цена в UZS** (автоматический расчет)

### Валидация:
- Минимум 3 поставщика для публикации договора
- ИНН должен содержать ровно 9 цифр
- Цена должна быть больше 0
- Автоматический пересчет в UZS при изменении валюты или курса

### Права доступа:
- `set_competitive_list_data` - право на редактирование конкурентного листа
