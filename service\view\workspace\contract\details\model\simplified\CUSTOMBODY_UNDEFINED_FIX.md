# Исправление ошибки "Cannot read properties of undefined (reading 'validate')"

## 🐛 **Ошибка:**
```
TypeError: Cannot read properties of undefined (reading 'validate')
at Contract.get (iac.service.js?v=1.13.0:27712:28)
```

## 🔍 **Причина:**
Код пытался обратиться к `this.customBody.validate()`, но для упрощенных договоров `this.customBody` не определен. Вместо этого создается `this.simplifiedBody`.

## 🔧 **Исправления:**

### **1. Геттер `public_contract` в `index.js`:**

```javascript
// ❌ БЫЛО:
get public_contract() {
  if (this.customBody.validate(false))  // ← customBody undefined для упрощенных
    return;
  
  // ... остальной код использовал this.customBody
}

// ✅ СТАЛО:
get public_contract() {
  // Получаем правильный body в зависимости от типа договора
  const contractBody = this.proc_type === "simplified_contract" ? this.simplifiedBody : this.customBody;
  
  if (!contractBody || contractBody.validate(false))
    return;

  return async () => {
    // ... код использует contractBody вместо this.customBody
    let spec_items = contractBody.properties.spec.dataSource.items || [];
    // ...
    if (!!await contractBody.save(null, false)) {
      await this.publish()
    }
  }
}
```

### **2. Геттер `org_sign_contract` в `index.js`:**

```javascript
// ❌ БЫЛО:
get org_sign_contract() {
  if (this.customBody.validate(false))  // ← customBody undefined для упрощенных
    return;
  
  // ... остальной код использовал this.customBody
}

// ✅ СТАЛО:
get org_sign_contract() {
  // Получаем правильный body в зависимости от типа договора
  const contractBody = this.proc_type === "simplified_contract" ? this.simplifiedBody : this.customBody;
  
  if (!contractBody || contractBody.validate(false))
    return;

  return async () => {
    // ... код использует contractBody вместо this.customBody
    let spec_items = contractBody.properties.spec.dataSource.items || [];
    // ...
    if (!!await contractBody.save(null, false)) {
      await this.org_sign()
    }
  }
}
```

### **3. Шаблон в `core.js`:**

```javascript
// ❌ БЫЛО:
<ui-layout :wait='model.customBody.wait'  :readonly='model.customBody.readonly' class='row' :fields='model.customBody.fields' />

// ✅ СТАЛО:
<ui-layout v-if='model.customBody' :wait='model.customBody.wait'  :readonly='model.customBody.readonly' class='row' :fields='model.customBody.fields' />
<ui-layout v-if='model.simplifiedBody' :wait='model.simplifiedBody.wait'  :readonly='model.simplifiedBody.readonly' class='row' :fields='model.simplifiedBody.fields' />
```

## ✅ **Результат:**

### **Логика работы:**

1. **Для прямых договоров** (`proc_type === "custom_contract"`):
   - Создается `this.customBody`
   - Геттеры используют `this.customBody`
   - Шаблон отображает `model.customBody`

2. **Для упрощенных договоров** (`proc_type === "simplified_contract"`):
   - Создается `this.simplifiedBody`
   - Геттеры используют `this.simplifiedBody`
   - Шаблон отображает `model.simplifiedBody`

### **Преимущества:**
- ✅ **Нет ошибок** - код корректно работает для обоих типов договоров
- ✅ **Универсальность** - один код поддерживает разные типы
- ✅ **Безопасность** - проверка существования объекта перед обращением
- ✅ **Читаемость** - понятно, какой body используется

## 🧪 **Для тестирования:**

### **Упрощенные договоры:**
1. Откройте упрощенный договор
2. Убедитесь, что нет ошибок в консоли
3. Проверьте, что все блоки отображаются
4. Протестируйте кнопки "Опубликовать" и "Подписать" (если доступны)

### **Прямые договоры:**
1. Откройте прямой договор
2. Убедитесь, что функциональность не нарушена
3. Проверьте работу всех блоков и кнопок

## 🎯 **Техническая суть:**

**Проблема была в том, что:**
- Код был написан только для прямых договоров
- Использовал жестко закодированные ссылки на `this.customBody`
- Не учитывал существование упрощенных договоров

**Решение:**
- Добавили динамическое определение типа body
- Используем условную логику для выбора правильного объекта
- Добавили проверки существования объекта

## 🔄 **Паттерн для будущих изменений:**

При добавлении новой функциональности, которая работает с body договора:

```javascript
// ✅ Правильный подход:
const contractBody = this.proc_type === "simplified_contract" ? this.simplifiedBody : this.customBody;

if (!contractBody) {
  // Обработка случая, когда body не определен
  return;
}

// Использование contractBody вместо прямого обращения к customBody
```

## 🎉 **Ошибка исправлена!**

Теперь упрощенные договоры корректно работают без ошибок в консоли.
