# Исправления конкурентного листа

## Проблема 1: Неправильное действие API

### Было:
Конкурентный лист отправлял данные через действие `set_graph_finance_data`, что неправильно смешивало разные типы данных.

```json
{
  "action": "set_graph_finance_data",
  "params": {
    "data": {
      "competitive_list": [...]
    }
  }
}
```

### Стало:
Конкурентный лист теперь отправляет данные через собственное действие `set_competitive_list_data`.

```json
{
  "action": "set_competitive_list_data", 
  "params": {
    "data": {
      "competitive_list": [...]
    }
  }
}
```

### Изменения в коде:
**Файл**: `simplified_body.js`
```javascript
// Было:
actions.push({
  name: 'set_graph_finance_data',
  params: { competitive_list: items }
});

// Стало:
actions.push({
  name: 'set_competitive_list_data',
  params: { competitive_list: items }
});
```

## Проблема 2: Неправильный пересчет валют

### Было:
- При выборе валюты, отличной от UZS, курс оставался 1
- Цена в UZS отображалась как NaN
- Поля формы не обновлялись автоматически

### Стало:
- Автоматическое получение курса валюты при выборе
- Корректный пересчет цены в UZS
- Обновление полей формы в реальном времени

### Изменения в коде:
**Файл**: `competitive_list.js`

#### 1. Исправлен метод расчета:
```javascript
// Было:
calculatePriceUzs() {
  if (this.currency === 'UZS') {
    return this.price;
  }
  return this.price * this.exchange_rate;
}

// Стало:
calculatePriceUzs() {
  const price = parseFloat(this.price) || 0;
  const rate = parseFloat(this.exchange_rate) || 1;
  
  if (this.currency === 'UZS') {
    return price;
  }
  return price * rate;
}
```

#### 2. Улучшен обработчик изменения валюты:
```javascript
onChange: async () => {
  if (this.currency === 'UZS') {
    this.exchange_rate = 1;
  } else {
    // Получаем курс валюты
    this.exchange_rate = await this.getExchangeRate(this.currency);
  }
  // Пересчитываем цену в UZS
  this.price_uzs = this.calculatePriceUzs();
  
  // Обновляем поле курса в форме
  const exchangeRateField = this.properties?.exchange_rate;
  if (exchangeRateField) {
    exchangeRateField._value = this.exchange_rate;
  }
  
  // Обновляем поле цены в UZS в форме
  const priceUzsField = this.properties?.price_uzs;
  if (priceUzsField) {
    priceUzsField._value = this.price_uzs;
  }
}
```

#### 3. Добавлено обновление полей при изменении цены и курса:
```javascript
// Для поля price:
onChange: () => {
  this.price_uzs = this.calculatePriceUzs();
  
  // Обновляем поле цены в UZS в форме
  const priceUzsField = this.properties?.price_uzs;
  if (priceUzsField) {
    priceUzsField._value = this.price_uzs;
  }
}

// Для поля exchange_rate:
onChange: () => {
  this.price_uzs = this.calculatePriceUzs();
  
  // Обновляем поле цены в UZS в форме
  const priceUzsField = this.properties?.price_uzs;
  if (priceUzsField) {
    priceUzsField._value = this.price_uzs;
  }
}
```

## Курсы валют

### Интеграция с API
Курсы валют теперь получаются автоматически через API `ref_exchange_rate`:

```javascript
const { error, data } = await Http.api.rpc("ref", {
  ref: "ref_exchange_rate",
  op: "read",
  limit: 1
});
```

### Кэширование
- **Время жизни кэша**: 5 минут
- **Автоматическое обновление** при истечении времени
- **Fallback курсы** при недоступности API

### Актуальные курсы (пример):
- **USD**: 12,758.36 UZS
- **EUR**: 14,820.11 UZS
- **RUB**: 163.34 UZS
- **UZS**: 1 UZS (базовая валюта)

## Требования к бэкенду

### Новое действие: `set_competitive_list_data`

Бэкенд должен обрабатывать отдельное действие для конкурентного листа:

```json
{
  "method": "contract_action",
  "params": {
    "action": "set_competitive_list_data",
    "number": "contract_number", 
    "params": {
      "data": {
        "competitive_list": [
          {
            "supplier_name": "ООО Поставщик",
            "supplier_inn": "123456789",
            "price": 1000.00,
            "currency": "USD",
            "exchange_rate": 12500,
            "price_uzs": 12500000.00
          }
        ]
      }
    }
  }
}
```

### Права доступа

Конкурентный лист продолжает использовать право `set_graph_finance_data`, но отправляет отдельное действие.

### Валидация

- Минимум 3 поставщика
- ИНН: ровно 9 цифр
- Цена > 0
- Корректность расчета цены в UZS

## Результат исправлений

✅ **Отдельное API действие** - `set_competitive_list_data`  
✅ **Корректный пересчет валют** - автоматическое получение курса  
✅ **Обновление полей формы** - в реальном времени  
✅ **Защита от NaN** - проверка на корректность значений  
✅ **Логическое разделение** - конкурентный лист отделен от графика финансирования

Теперь конкурентный лист работает корректно с автоматическим пересчетом валют и отправляет данные через собственное API действие!
