import { Entity, DataSource, Query, Property } from '@iac/data';
import { Http, Language, Util } from '@iac/core';
import { Config, Context, Settings } from '@iac/kernel';


let SearchInput = {
  props: ["text"],
  data: function () {
    return {
      value: this.text,
      tid: undefined
    }
  },
  watch: {
    value: {
      immediate: true,
      async handler(val, oldVal) {
        if (val == oldVal)
          return;

        if (val == '' || val == null)
          val = undefined

        if (this.tid) {
          clearTimeout(this.tid);
        }
        this.tid = setTimeout(async () => {
          this.$emit("search", val)
        }, 400);
      }
    }
  },
  template: `
        <input style='padding: 10px; border-radius: 5px;width: 100%; border: 1px solid #009ab8; outline: none;' :placeholder='$t("search")' v-model='value' />
    `
}

let validate = function () {
  if (this.value <= 0)
    return "Укажите значение больше 0"
}

let validate_advance = function () {
  if (this.value < 0)
    return "Укажите значение не менее 0"
}

class CustomContractProduct extends Entity {
  constructor(context, contract) {
    super(context);

    this.is_gos = context.is_gos;

    this.claim_id = context.claim_id;
    this.product = context.product;
    this.start_price = context.start_price || context.price;
    this.start_currency = context.currency || Settings._default_currency;
    this.price = context.price;
    this.currency = context.currency || Settings._default_currency;

    if (context.product?.unit) {
      this.amount = `${context.amount} ${context.product.unit}`;
    } else {
      this.amount = context.amount;
    }

    this.delivery_month = context.delivery_month;
    this.delivery_year = context.delivery_year;

    this.conditions = context.conditions;
    this.delivery_address = context.delivery_address;
    this.country = context.country;
  }

  props() {
    return {
      product: {
        type: "product",
        readonly: true,
        attr: {
          eye: true,
          short: true
        },
      },
      start_price: {
        group: "<start_price>",
        type: "float",
        label: "Стартовая цена",
        readonly: true,
        hidden: !this.is_gos,
        attr: { react: true },
      },
      start_currency: {
        group: "<start_price>",
        label: "currency",
        type: "entity",
        dataSource: 'ref_currency',
        hidden: !this.is_gos,
        readonly: true
      },
      price: {
        group: "<price>",
        type: "float",
        required: this.is_gos,
        hidden: !this.is_gos,
        attr: { react: true },
        validate: () => {
          if (this.price <= 0) {
            return "Укажите значение больше 0";
          }

          if (this.price > this.start_price) {
            return "Значение не может превышать стартовую цену";
          }
        }
      },
      currency: {
        group: "<price>",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true,
        hidden: !this.is_gos,
      },
      delivery_month: {
        group: '<delivery>',
        type: "entity",
        label: "contract.delivery_month",
        dataSource: DataSource.get("ref_months"),
        required: this.is_gos,
        hidden: !this.is_gos,
      },
      delivery_year: {
        group: "<delivery>",
        label: "contract.delivery_year",
        max: 2100,
        type: "number",
        required: this.is_gos,
        hidden: !this.is_gos,
      },
      country: {
        type: "entity",
        label: "country",
        dataSource: 'ref_country_',
        required: true,
      },
      conditions: {
        type: "text",
        label: "conditions_characteristics",
        required: true,
      },
      delivery_address: {
        type: "text",
        required: !this.is_gos,
        hidden: this.is_gos,
      },
    }
  }
}


class CustomContractViewProduct extends Entity {
  constructor(context, contract) {
    super(context);

    this.is_gos = context.is_gos;

    this.account = context.account;
    this.expense_item_code = context.expense;

    this.claim_id = context.claim_id;
    this.price = `${Util.Number(context.price, ' ', 2)} ${context.currency || Settings._default_currency}`;

    if (context.product?.unit) {
      this.amount = `${context.amount} ${context.product.unit}`;
    } else {
      this.amount = context.amount;
    }

    if (context.delivery_month) {
      this.delivery_month = Language.t(`month_${context.delivery_month}`);
    }
    this.delivery_year = context.delivery_year;

    this.conditions = context.conditions;
    this.delivery_address = context.delivery_address;
    this.country = context.country;
  }

  props() {
    return {
      amount: {
        type: "static",
        label: "-amount",
      },
      price: {
        type: "static",
        label: "-price",
      },
      account: {
        type: "static",
        label: "-account",
        hidden: !this.is_gos,
      },
      expense_item_code: {
        type: "static",
        label: "-expense_item_code",
        hidden: !this.is_gos,
      },
      delivery_month: {
        type: "static",
        label: "-contract.delivery_month",
        // dataSource: "ref_months",
        hidden: !this.is_gos,
      },
      delivery_year: {
        type: "static",
        label: "-contract.delivery_year",
        hidden: !this.is_gos,
      },
      country: {
        type: "static",
        label: "-country",
        dataSource: 'ref_country_',
        readonly: true,
      },
      conditions: {
        type: "static",
        label: "-conditions_characteristics",
      },
      delivery_address: {
        type: "static",
        label: "-delivery_address"
      },
    }
  }
}

class CustomContractGraph extends Entity {
  constructor(context, contract) {
    super(context);

    this.source = context.source;
    this.summa = context.summa;
    this.advance_payment = context.advance_payment || 0;
    this.currency = context.currency || Settings._default_currency;
  }

  props() {
    return {
      source: {
        label: "comm_src",
        required: true,
      },
      summa: {
        group: "<summa>",
        type: "float",
        required: true,
        attr: { react: true },
        validate: validate
      },
      currency: {
        group: "<summa>",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true,
        required: true,
      },
      advance_payment: {
        type: "float",
        label: "advance_payment_amount",
        required: false,
        attr: { react: true },
        validate: validate_advance
      },
    }
  }
}


class CustomContractGraphFinance extends Entity {
  constructor(context, contract) {
    super(context);

    this.date = context.date;
    this.summa = context.summa;
    this.advance_payment = context.advance_payment || 0;
    this.currency = context.currency || Settings._default_currency;
    this.kls = context.kls;
    this.expense_item_code = context.expense_item_code;
    this.is_gos = context.is_gos;
    this.claim_accounts = context.claim_accounts || [];
  }

  props() {
    return {
      date: {
        type: "model",
        label: "!",
        fields: {
          month: {
            type: "entity",
            dataSource: "ref_months",
            group: "<date>",
            required: true,
          },
          year: {
            type: "number",
            group: "<date>",
            max: 2100,
            required: true,
          }
        }
      },
      kls: {
        label: "bank_account",
        type: "entity",
        required: this.is_gos,
        hidden: !this.is_gos,
        dataSource: this.claim_accounts,
      },
      expense_item_code: {
        group: '!type-',
        type: "entity",
        dataSource: "ref_expense_item",
        required: this.is_gos,
        hidden: !this.is_gos
      },
      summa: {
        group: "<summa>",
        type: "float",
        required: true,
        attr: { react: true },
        validate: validate
      },
      currency: {
        group: "<summa>",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true,
        required: true,
      },
      advance_payment: {
        type: "float",
        label: "advance_payment_amount",
        required: false,
        attr: { react: true },
        validate: validate_advance
      },
    }
  }
}

class CustomContractBodyProperty extends Property {
  constructor(context) {
    super(context)
  }

  get meta() {
    if (this.type == 'file' && this.value) {
      return {
        ...this._meta,
        url: (value) => {
          if (!value.id)
            return;
          return `${Config.api_server}/file/${value.id}`
        },
      }
    }
    return this._meta
  }

}

export default class CustomContractBody extends Entity {

  constructor(context, contract) {
    super(context);

    this.context = context;
    this.contract = contract;
    this.wait = false;

    this.claim_data = context.claim_data || [];

    this.claim_accounts = this.claim_data.map((item) => { return { id: item.kls, name: item.kls }; });

    this.claim_data = this.claim_data.reduce((acc, item) => {
      acc[item.id] = {
        account: item.kls,
        expense_item_code: item.expense
      };

      return acc;
    }, {});

    this.currency = context.currency || Settings._default_currency;

    this.base_data = {
      lot_id: context.lot_id,
      ckepik_id: context.ckepik_id,
      version_number: context.version_number,
      contract_name: context.contract_name,
      hide_in_public: context.hide_in_public,
      contract_close_at: context.contract_close_at,
      execution_date: context.execution_date,
      spec_totalcost_amount: `${Util.Number(context.spec_totalcost_amount, ' ', 2)} ${this.currency}`,
      vat_nds: context.vat_nds,
      contract_type: context.contract_type,
      contract_reason: context.contract_reason,
      reason: context.reason,
      addons: context.addons,
      footing: context.footing,
      beneficiary: context.beneficiary,
      delivery_days: context.delivery_days,
      payment_days: context.payment_days,
      advance_payment_days: context.advance_payment_days,
      special_conditions: context.special_conditions,
    }

    this.reason_cancellation = context.reason_cancellation;
    this.grounds_cancellation = context.grounds_cancellation;

    this.additional_contract = context.additional_contract;

    context.contragent = context.contragent || {};

    if (context.contragent.company_details) {
      let source = context.contragent.company_details.source || "company";

      context.contragent[source] = { ...context.contragent.company_details, source: source }
      if (context.contragent.custom) {
        context.contragent.custom_company = true
      }
    }

    this.participants = {
      initiator: contract.initiator,
      contragent: context.contragent,
    }

    this.requisites = {
      org_banking_details: this.participants.initiator.banking_details,
      part_banking_details: this.participants.contragent.banking_details,
    };

    this._graph = context.graph || []
    this._graph_finance = context.graph_finance || []
    this._spec = context.spec || []
  }

  get propertyModel() {
    return CustomContractBodyProperty;
  }

  async edit_product(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new CustomContractProduct({ is_gos: this.contract.is_gos, ...item })).fields
    });
  }

  async edit_graph(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new CustomContractGraph({ currency: this.currency, ...item })).fields
    })
  }

  async edit_graph_finance(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new CustomContractGraphFinance({ currency: this.currency, is_gos: this.contract.is_gos, claim_accounts: this.claim_accounts, ...item })).fields
    })
  }

  validate_fields(fields) {
    for (let field of fields) {
      if (field.hidden && typeof field.hidden != "function") {
        continue;
      }

      if (field.hidden && field.hidden()) {
        continue;
      }

      if (field.status) {
        Vue.Dialog.MessageBox.Error(Language.t("error_check_data_validity"));

        return false;
      }

      if (field.type == 'model' && field.fields && !this.validate_fields(field.fields)) {
        return false;
      }
    }

    return true;
  }

  build_action_params(fields) {
    return fields.filter((field) => {
      if (field.type == 'static') return false;

      if (field.hidden && typeof field.hidden == 'function') return !field.hidden();

      return !field.hidden;
    }).map((field) => {
      let value = field.value;

      if (value && value.exp && value.exp.value != undefined) value = value.exp.value

      if (Array.isArray(value) && value.length <= 0) value = undefined;

      if (value && !Array.isArray(value) && typeof value == 'object') value = { ...value }

      return { name: field.name, value: value }
    }).filter((field) => {
      return field.value != undefined
    }).reduce((acc, field) => {
      acc[field.name] = field.value;

      return acc;
    }, {});
  }

  async update_graph() {
    let { error, data } = await this.contract.action('get_graph', {});

    if (!error && data) {
      this.properties.graph.dataSource.set_items(data);
    }
  }

  async save(handle_actions = null, notify = true) {
    if (handle_actions != null && typeof (handle_actions) != "object") {
      handle_actions = [handle_actions];
    }

    this.validate();

    let actions = [];

    if (this.contract.rights?.set_contract_data && (!handle_actions || handle_actions.indexOf("base_data") != -1)) {
      if (!this.validate_fields(this.properties.base_data.fields)) { return null; }

      actions.push({
        name: 'set_contract_data',
        params: this.build_action_params(this.properties.base_data.fields),
      });
    }

    if (this.contract.rights.set_contragent_data && (!handle_actions || handle_actions.indexOf("participants") != -1)) {
      if (!this.validate_fields(this.properties.participants.fields)) { return null; }

      actions.push({
        name: 'set_contragent_data',
        params: this.build_action_params([this.properties.participants.properties.contragent]),
      });
    }

    if (this.contract.rights.set_org_requisites && (!handle_actions || handle_actions.indexOf("org_requisites") != -1)) {
      if (!this.validate_fields([this.properties.requisites.properties.org_banking_details])) { return null; }

      actions.push({
        name: 'set_org_requisites',
        params: { banking_details_id: this.requisites.org_banking_details.id },
      });
    }

    if (this.contract.rights.set_part_requisites && (!handle_actions || handle_actions.indexOf("part_requisites") != -1)) {
      if (!this.validate_fields([this.properties.requisites.properties.part_banking_details])) { return null; }

      actions.push({
        name: 'set_part_requisites',
        params: { banking_details_id: this.requisites.part_banking_details.id },
      });
    }

    if (this.contract.rights.set_graph_data && (!handle_actions || handle_actions.indexOf("graph") != -1)) {
      let items = this.properties.graph.dataSource.items || [];

      items = items.filter((item) => {
        return !((item.status & 4) != 0)
      }).map((item) => {
        return {
          source: item.source,
          summa: item.summa,
          advance_payment: item.advance_payment || 0,
          currency: item.currency,
        }
      })

      let currency = items.reduce((prev, item) => {
        prev[item.currency] = prev[item.currency] || 0
        prev[item.currency] += item.summa
        return prev
      }, {});

      var graph_totalcost = Object.keys(currency).map(function (key) {
        return { currency: key, amount: currency[key] };
      });

      actions.push({
        name: 'set_graph_data',
        params: { graph: items, graph_totalcost: graph_totalcost }
      });
    }

    if (this.contract.rights.set_graph_finance_data && (!handle_actions || handle_actions.indexOf("graph_finance") != -1)) {
      let items = this.properties.graph_finance.dataSource.items || [];

      items = items.filter((item) => {
        return !((item.status & 4) != 0)
      }).map((item) => {
        return {
          date: item.date,
          summa: item.summa,
          advance_payment: item.advance_payment || 0,
          currency: item.currency,
          kls: item.kls,
          expense_item_code: item.expense_item_code,
        }
      })

      let currency = items.reduce((prev, item) => {
        prev[item.currency] = prev[item.currency] || 0
        prev[item.currency] += item.summa
        return prev
      }, {});

      var graph_finance_totalcost = Object.keys(currency).map(function (key) {
        return { currency: key, amount: currency[key] };
      });

      actions.push({
        name: 'set_graph_finance_data',
        params: { graph_finance: items, graph_finance_totalcost: graph_finance_totalcost }
      });
    }

    if (this.contract.rights.set_specification && (!handle_actions || handle_actions.indexOf("spec") != -1)) {
      let items = this.properties.spec.dataSource.items || [];

      let invalid_items = items.filter((item) => {
        return (
          !((item.status & 4) != 0)
          && (
            (this.contract.is_gos && !item.price)
            || (this.contract.is_gos && !item.delivery_month)
            || (this.contract.is_gos && !item.delivery_year)
            || !item.country
            || !item.conditions
            || (!this.is_gos && !item.delivery_address)
          )
        )
      });

      if (invalid_items.length > 0) {
        invalid_items.forEach((items) => { items.status |= 8; });
        await Vue.Dialog.MessageBox.Error(Language.t("error_check_data_validity"));
        return null;
      }

      items = items.filter((item) => {
        return !((item.status & 4) != 0)
      }).map((item) => {
        return {
          id: item.id,
          product: item.product,
          account: item.account,
          expense_item_code: item.expense_item_code,
          currency: item.currency,
          amount: item.amount,
          unit: item.unit,
          price: item.price,
          conditions: item.conditions,
          delivery_address: item.delivery_address,
          country: item.country,
          delivery_month: item.delivery_month,
          delivery_year: item.delivery_year,
        }
      })

      let currency = items.reduce((prev, item) => {
        prev[item.currency] = prev[item.currency] || 0
        prev[item.currency] += item.amount * item.price
        return prev
      }, {});

      var spec_totalcost = Object.keys(currency).map(function (key) {
        return { currency: key, amount: currency[key] };
      });

      actions.push({
        name: 'set_specification',
        params: { spec: items, spec_totalcost: spec_totalcost }
      });
    }

    this.wait = true;

    let data = {}
    for (let action of actions) {
      let params = action.params

      let { error, data } = await this.contract.action(action.name, { data: params });

      if (error && error.code != 'AbortError') {
        await Vue.Dialog.MessageBox.Error(error);

        this.wait = false;

        return null;
      }

      data = { ...data, ...params };

      if (params.graph) {
        this._graph = params.graph.map((item, index) => {
          return { ...item, status: 0 }
        });
        this.properties.graph.dataSource.set_items(this._graph);
      }

      if (params.graph_finance && this.contract.is_gos) {
        this.update_graph();
      }

      if (params.graph_finance) {
        this._graph_finance = params.graph_finance.map((item, index) => {
          return { ...item, status: 0 }
        });
        this.properties.graph_finance.dataSource.set_items(this._graph_finance);
      }

      if (params.spec) {
        await this.contract.update_context();

        this._spec = params.spec.map((item, index) => {
          return { ...item, status: 0 }
        });
        this.properties.spec.dataSource.set_items(this._spec);
      }
    }

    this.wait = false;

    if (notify) {
      await Vue.Dialog.MessageBox.Success(Language.t("data_updated"));
    }

    return data;
  }

  async onChangeProperty(field) {

    if (!field || field.property.type != 'file')
      return;

    var send = async (file) => {
      let formData = new FormData();
      //formData.append('scope_tender_participant', path[0] && path[0].id);
      formData.append('data', file, file.name);
      let { data, error } = await Http.upload.form('tender/attach', formData);
      if (error) {
        //field.property.wait = false;
        return;
      }

      return {
        id: data.uuid,
        name: data.meta.name,
        //desc: context.description,
        meta: {
          "type": data.meta.type,
          "content_type": data.meta.content_type,
          "type_group": data.meta.group,
          "size": data.meta.size
        }
      }
    }


    let value = field.value;
    this.properties.setting_general.wait = true;
    if (Array.isArray(value)) {
      for (let key in value) {
        let item = value[key];
        if (item && item.file && item.file.name) {
          let _value = await send(item.file);
          value[key] = _value;
        }
      }
      field.property._value = [...value]

    } else {
      let item = value;
      if (item && item.file && item.file.name) {
        let _value = await send(item.file);
        value = field.property._value = _value

      }
    }
    this.properties.setting_general.wait = false;
  }

  props() {
    return {
      base_settings: {
        group: "!base/direct_contract_gen_data",
        type: "setting",
        attr: {
          style: "max-width: 800px;"
        }
      },
      base_data: {
        type: "model",
        label: "!",
        group: "!base/direct_contract_gen_data",
        readonly: !this.contract.rights?.set_contract_data,
        fields: {
          contract_status: {
            label: '-status',
            type: "widget",
            widget: {
              name: {
                props: ['status', 'reason_cancellation', 'grounds_cancellation'],
                methods: {
                  download(e) {
                    e.preventDefault();
                    let link = `${Config.api_server}/file/${this.grounds_cancellation.id}`
                    this.$wait(async () => {
                      if (Context.User.id) {
                        await Context.User.refreshToken();
                        window.location = `${link}?token=${Context.User.access_token}`
                      } else {
                        window.location = `${link}`
                      }
                    })
                  }
                },
                template: `
                    <div>
                      <ui-ref source='custom_contract_status' :value='status'/>
                      <div style='color: #777' v-if='grounds_cancellation'>{{$t("dir_contr_reason")}}:
                        <a :href='grounds_cancellation.name' @click.prevent='download'>{{ grounds_cancellation.name }}</a>
                      </div>
                      <div style='color: #777' v-if='reason_cancellation'>Причина: {{reason_cancellation}}</div>
                    </div>
                  `
              },
              props: {
                status: this.contract.status,
                reason_cancellation: this.reason_cancellation,
                grounds_cancellation: this.grounds_cancellation
              }
            }
          },
          version_number: {
            label: '-contract.extra_number',
            type: "static",
            hidden: () => {
              return !this.version_number
            }
          },
          lot_id: {
            type: "static",
            label: '-contract.lot_id',
            readonly: true,
          },
          ckepik_id: {
            type: "number",
            label: '-contract.ckepik_id',
            readonly: !this.contract.rights?.set_contragent_data,
            required: false,
          },
          contract_name: {
            label: '-contract.contract_name',
            required: true,
            readonly: !this.contract.rights?.set_contragent_data,
          },
          hide_in_public: {
            label: '-contract.hide_in_public',
            type: "bool",
            readonly: this.additional_contract,
          },
          contract_close_at: {
            label: "-contract.contract_close_at",
            type: "date",
            required: true,
            validate: () => {
              let value = this.base_data.contract_close_at;
              if (!value || !this.base_data.execution_date) { return }

              return (
                new Date(this.base_data.execution_date) < new Date(value)
                  ? Language.t("contract.must_be_before_expiration")
                  : null
              );
            }
          },
          execution_date: {
            label: "-contract.execution_date",
            type: "date",
            required: true,
            validate: () => {
              let value = this.base_data.execution_date
              if (!value || !this.base_data.contract_close_at) { return }

              return (
                new Date(value) < new Date(this.base_data.contract_close_at)
                  ? Language.t("contract.must_be_after_signing")
                  : null
              );
            }
          },
          spec_totalcost_amount: {
            label: "-contract.contract_totalcost",
            type: "static",
            attr: {
              style: "margin-bottom: 0;"
            },
          },
          vat_nds: {
            type: "bool",
            label: "-contract.vat",
          },
          contract_type: {
            label: '-contract_type',
            type: "entity",
            readonly: !this.contract.rights?.set_contragent_data,
            has_del: true,
            required: true,
            dataSource: "ref_contract_type",
          },
          contract_reason: {
            label: '-dir_contr_reason',
            type: "entity",
            has_del: true,
            readonly: !this.contract.rights?.set_contragent_data,
            required: true,
            dataSource: {
              limit: 99,
              store: {
                method: "ref",
                ref: "ref_contract_reasons",
                injectQuery: (params) => {
                  params.filters = params.filters || {};
                  params.filters.proc_type = "custom_contract";
                  return params;
                }
              }
            },
          },
          reason: {
            label: "-!reason",
            type: "file",
            multiple: true,
            required: false,
          },
          addons: {
            label: "-!",
            type: "enum",
            hidden: () => {
              return !this.contract_type || this.contract_type.id != 4
            },
            dataSource: [
              { id: "1", name: Language.t("direct_contract_type1") },
              { id: "2", name: Language.t("direct_contract_type2") },
              { id: "3", name: Language.t("direct_contract_type3") },
              { id: "4", name: Language.t("direct_contract_type4") },
            ],
          },
          beneficiary: {
            type: "model",
            label: "-dir_contract_beneficiary",
            fields: {
              title: {
                label: "!title",
                group: "<beneficiary>"
              },
              inn: {
                label: "!inn",
                group: "<beneficiary>",
                type: "number"
              }
            },
          },
          delivery_days: {
            label: "-contract.delivery_days",
            type: "integer",
            required: true,
            validate: validate,
          },
          payment_days: {
            label: "-contract.payment_days",
            type: "integer",
            required: true,
            validate: () => {
              let value = this.base_data.advance_payment_days;
              if (!value || !this.base_data.payment_days) { return }

              return (
                value > this.base_data.payment_days
                  ? Language.t("payment_term_not_less_than_preliminary")
                  : null
              );
            },
          },
          advance_payment_days: {
            label: "-contract.advance_payment_days",
            type: "integer",
            required: false,
            validate: () => {
              let value = this.base_data.advance_payment_days;
              if (!value || !this.base_data.payment_days) { return }

              return (
                value > this.base_data.payment_days
                  ? Language.t("payment_term_not_exceed")
                  : null
              );
            }
          },
          special_conditions: {
            group: "!base/!special_terms",
            label: "-special_terms",
            required: true,
            type: "text",
          },
        }
      },
      participants_settings: {
        group: "participants",
        type: "setting",
        attr: {
          style: "max-width: 800px;"
        },
      },
      participants: {
        type: "model",
        group: "!participants",
        label: "!",
        fields: {
          initiator: {
            type: "widget",
            readonly: true,
            label: "-contract.organizer",
            widget: {
              name: 'router-link',
              content: this.participants.initiator.company_details.title,
              props: {
                to: `/company/${this.participants.initiator.company_details.id}`,
                style: '-webkit-line-clamp: 2; padding-top: 12px; overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; line-height: 20px;'
              },
            }
          },
          contragent: {
            label: "-company.seller",
            type: "model",
            readonly: !this.contract.rights?.set_contragent_data,
            hidden: () => {
              return !this.base_data.contract_type
            },
            fields: {
              small_business: {
                type: "bool",
                label: "small_business",
              },
              custom_company: {
                type: "bool",
                label: "add_company_manually",
                hidden: () => {
                  return (
                    this.readonly
                    || !this.base_data.contract_type
                    || this.base_data.contract_type == 1
                  )
                },
              },
              special_supplier: {  // Единый поставщик
                label: "!supplier_single",
                type: "entity",
                has_del: true,
                required: true,
                hidden: () => {
                  return !this.base_data.contract_type || this.base_data.contract_type != 1
                },
                dataSource: new DataSource({
                  valueExp: ["id", "inn", "name"],
                  search: true,
                  store: {
                    ref: "ref_special_suppliers",
                    context: (context) => {
                      context.desc = context.description
                      return context;
                    },
                    injectQuery: (params) => {
                      params.fields = ["id", "inn", "name", "description"]
                      return params
                    }
                  }
                })
              },
              company: {  // субъект государственных закупок
                label: "!company",
                type: "entity",
                required: true,
                hidden: () => {
                  return (
                    !this.base_data.contract_type
                    || this.base_data.contract_type == 1
                    || this.participants.contragent?.custom_company
                  )
                },
                has_del: true,
                dataSource: new DataSource({
                  valueExp: ["id", "title", "inn", "email"],
                  search: true,
                  displayExp: "title",
                  store: {
                    method: "company_ref",
                    ref: "companies",
                    context: (context) => {
                      context.desc = `ИНН: ${context.inn}`
                      return context;
                    },
                  }
                })
              },
              
              custom: {  // Кастомная компания
                type: "model",
                label: "!",
                hidden: () => {
                  return (
                    !this.base_data.contract_type
                    || this.base_data.contract_type == 1
                    || !this.participants.contragent?.custom_company
                  )
                },
                fields: {
                  company_name: {
                    required: true,
                    label: "-direct_contract_comp_name"
                  },
                  activity_area_id: {
                    label: "-activity_area_id",
                    type: "entity",
                    dataSource: "ref_area_",
                    required: true,
                  },
                  legal_address: {
                    label: "-legal_address",
                    required: true,
                  },
                  phone: {
                    label: "-phone",
                    required: true,
                  },
                  fax: {
                    label: "-fax"
                  },
                  inn: {
                    label: "-inn",
                    required: true,
                  },
                  oked_code: {
                    label: "-oked_code",
                    type: 'entity',
                    required: true,
                    dataSource: "ref_economic_acivity_type_"
                  },
                }
              },
              email: {
                type: "email",
                label: "-email",
                hidden: () => {
                  return (
                    this.base_data.contract_type != 1
                    && !this.participants.contragent?.custom_company
                    && this.participants.contragent?.company?.email
                  )
                },
                required: true,
              },
            }
          },
        }
      },
      requisites: {
        type: "model",
        group: "!company_bank",
        label: "!",
        fields: {
          org_banking_details: {
            group: "!company_bank/!requisites-/contract.requisites_org",
            type: 'entity',
            label: "requisites_title",
            description: "$requisites_title",
            dataSource: 'get_company_bank_accounts_struct',
            readonly: !this.contract.rights?.set_org_requisites,
            has_del: true,
            required: this.contract.rights?.set_org_requisites,
            attr: {
              details: [
                "bank_name",
                { label: Language.t([Settings._country + ".bank_mfo_code", "bank_mfo_code"]), field: "bank_mfo_code" },
                "bank_account"
              ]
            }
          },
          part_banking_details: {
            group: "!company_bank/!requisites/contract.requisites_contragent",
            type: 'entity',
            label: "requisites_title",
            description: "$requisites_title",
            dataSource: 'get_company_bank_accounts_struct',
            readonly: () => { return !this.contract.rights?.set_part_requisites; },
            has_del: true,
            required: this.contract.rights?.set_part_requisites,
            attr: {
              details: [
                "bank_name",
                { label: Language.t([Settings._country + ".bank_mfo_code", "bank_mfo_code"]), field: "bank_mfo_code" },
                "bank_account"
              ]
            }
          },
        }
      },
      actions: {
        type: "action",
        label: "!",
        buttons: true,
        actions: [
          {
            label: "Save",
            hidden: () => {
              return (
                !this.contract.rights
                || (
                  !this.contract.rights.set_contract_data
                  && !this.contract.rights.set_contragent_data
                  && !this.contract.rights.set_org_requisites
                  && !this.contract.rights.set_part_requisites
                )
              )
            },
            handler: async () => {
              await this.save(["base_data", "participants", "org_requisites", "part_requisites"]);
            }
          }
        ]
      },
      graph: {
        group: "comm_src",
        type: "data-grid",
        readonly: !this.contract.rights || !this.contract.rights.set_graph_data,
        label: "!grid",
        dataSource: new DataSource({
          actions: !this.contract.is_gos && [
            {
              label: "add",
              btn_type: "success",
              handler: async () => {
                let item = await this.edit_graph();
                if (!item || item == 2)
                  return;

                this.properties.graph.dataSource.push_item({
                  status: 1,

                  source: item.source,
                  summa: item.summa,
                  currency: item.currency,
                  advance_payment: item.advance_payment || 0,
                })
              }
            },
            {
              label: "save_changes",
              hidden: () => {
                let items = this.properties.graph.dataSource.items;
                if (!items)
                  return true;
                return items.filter((item) => {
                  if ((item.status & 7) != 0)
                    return true
                }).length <= 0;
              },
              handler: async () => {
                await this.save(["graph"]);
              }
            }
          ],
          store: {
            data: this._graph.map((item, index) => {
              return { ...item, status: 0 }
            }),
            context: (context) => {

              Object.defineProperty(context, "bindClass", {
                configurable: true,
                enumerable: true,
                get: () => {

                  if ((context.status & 4) != 0)
                    return "ui-alert ui-alert-danger";

                  if ((context.status & 1) != 0)
                    return "ui-alert ui-alert-success";

                  if ((context.status & 2) != 0)
                    return "ui-alert ui-alert-warning";

                },
              });

              context.actions = !this.contract.is_gos && [
                {
                  //label: "delete",
                  btn_type: "warning",
                  icon: "edit",
                  hidden: () => {
                    return ((context.status & 4) != 0)
                  },
                  handler: async () => {
                    let item = await this.edit_graph(context);
                    if (!item || item == 2)
                      return;

                    context.source = item.source
                    context.summa = item.summa
                    context.currency = item.currency
                    context.advance_payment = item.advance_payment || 0,

                      context.status |= 2;
                  }
                },
                {
                  //label: "delete",
                  btn_type: "danger",
                  icon: "trash",
                  hidden: () => {
                    return ((context.status & 4) != 0)
                  },
                  handler: () => {
                    context.status |= 4;
                  }
                },
                {
                  label: "Восстановить",
                  btn_type: "danger",
                  hidden: () => {
                    return !((context.status & 4) != 0)
                  },
                  handler: () => {
                    context.status &= ~4;
                  }
                }
              ]

              return context;
            }
          }

        }),
        attr: {
          action_name: !this.contract.is_gos && Language.t("actions"),
          action_style: !this.contract.is_gos && "min-width: 110px;text-align:left;",
          buttons: true,
          summary: true,
          not_found: "",
          columns: [
            {
              field: "source", label: "product_name", style: "width: 100%; text-align: left;",
              summary: (items) => {
                if (!items)
                  return;
                let count = items.filter((item) => {
                  return !((item.status & 4) != 0);
                }).length;
                return `${count} ${Language.t("source", { count: count })}`
              }
            },
            {
              field: "summa", style: "white-space: nowrap; text-align: right;",
              display: (value, item) => {
                return `${Util.Number(value, ' ', 2)} ${item.currency}`;
              },
              summary: (items) => {
                if (!items)
                  return;

                let currency = items.reduce((prev, item) => {
                  if ((item.status & 4) != 0)
                    return prev
                  prev[item.currency] = prev[item.currency] || 0
                  prev[item.currency] += item.summa
                  return prev
                }, {});

                return Object.keys(currency).map((key) => {
                  return `${Util.Number(currency[key], ' ', 2)} ${key}`
                }).join('<br/>')
              }
            },
            {
              field: "advance_payment", label: "advance_payment_amount", style: "white-space: nowrap; text-align: right;",
              display: (value, item) => {
                return `${Util.Number(value || 0, ' ', 2)} ${item.currency}`;
              },
              summary: (items) => {
                if (!items)
                  return;

                let currency = items.reduce((prev, item) => {
                  if ((item.status & 4) != 0)
                    return prev
                  prev[item.currency] = prev[item.currency] || 0
                  prev[item.currency] += item.advance_payment || 0
                  return prev
                }, {});

                return Object.keys(currency).map((key) => {
                  return `${Util.Number(currency[key], ' ', 2)} ${key}`
                }).join('<br/>')
              }
            }
          ]
        }
      },
      graph_finance: {
        group: "contract.graphic",
        type: "data-grid",
        readonly: !this.contract.rights || !this.contract.rights.set_graph_finance_data,
        label: "!grid",
        dataSource: new DataSource({
          actions: [
            {
              label: "add",
              btn_type: "success",
              handler: async () => {
                let item = await this.edit_graph_finance();
                if (!item || item == 2)
                  return;

                this.properties.graph_finance.dataSource.push_item({
                  status: 1,

                  date: item.date,
                  summa: item.summa,
                  advance_payment: item.advance_payment || 0,
                  currency: item.currency,
                  kls: item.kls,
                  expense_item_code: item.expense_item_code,

                })
              }
            },
            {
              label: "save_changes",
              hidden: () => {
                let items = this.properties.graph_finance.dataSource.items;
                if (!items)
                  return true;
                return items.filter((item) => {
                  if ((item.status & 7) != 0)
                    return true
                }).length <= 0;
              },
              handler: async () => {
                await this.save(["graph_finance"]);
              }
            }
          ],
          store: {
            data: this._graph_finance.map((item, index) => {
              return { ...item, status: 0 }
            }),
            context: (context) => {

              Object.defineProperty(context, "bindClass", {
                configurable: true,
                enumerable: true,
                get: () => {

                  if ((context.status & 4) != 0)
                    return "ui-alert ui-alert-danger";

                  if ((context.status & 1) != 0)
                    return "ui-alert ui-alert-success";

                  if ((context.status & 2) != 0)
                    return "ui-alert ui-alert-warning";

                },
              });

              context.actions = [
                {
                  //label: "delete",
                  btn_type: "warning",
                  icon: "edit",
                  hidden: () => {
                    return ((context.status & 4) != 0)
                  },
                  handler: async () => {
                    let item = await this.edit_graph_finance(context);
                    if (!item || item == 2)
                      return;

                    context.date = item.date;
                    context.summa = item.summa;
                    context.advance_payment = item.advance_payment || 0;
                    context.currency = item.currency;
                    context.kls = item.kls;
                    context.expense_item_code = item.expense_item_code;

                    context.status |= 2;
                  }
                },
                {
                  //label: "delete",
                  btn_type: "danger",
                  icon: "trash",
                  hidden: () => {
                    return ((context.status & 4) != 0)
                  },
                  handler: () => {
                    context.status |= 4;
                  }
                },
                {
                  label: "Восстановить",
                  btn_type: "danger",
                  hidden: () => {
                    return !((context.status & 4) != 0)
                  },
                  handler: () => {
                    context.status &= ~4;
                  }
                }
              ]

              return context
            }
          }
        }), attr: {
          action_name: Language.t("actions"),
          action_style: "min-width: 110px;text-align:left;",
          buttons: true,
          summary: true,
          not_found: "",
          columns: [{
            field: "date",
            display: (value, item) => {
              return `${Language.t('month_' + value.month)}, ${value.year}`;
            },
            style: "white-space: nowrap; text-align: right;"
          },
          this.contract.is_gos && { field: "kls", style: "text-align: left;" },
          this.contract.is_gos && { field: "expense_item_code", style: "width: 100%; text-align: left;" },
          {
            field: "summa", style: "white-space: nowrap; text-align: right;",
            display: (value, item) => {
              return `${Util.Number(value, ' ', 2)} ${item.currency}`;
            },
            summary: (items) => {
              if (!items)
                return;

              let currency = items.reduce((prev, item) => {
                if ((item.status & 4) != 0)
                  return prev
                prev[item.currency] = prev[item.currency] || 0
                prev[item.currency] += item.summa
                return prev
              }, {});

              return Object.keys(currency).map((key) => {
                return `${Util.Number(currency[key], ' ', 2)} ${key}`
              }).join('<br/>')
            }
          },
          {
            field: "advance_payment", label: "advance_payment_amount", style: "white-space: nowrap; text-align: right;",
            display: (value, item) => {
              return `${Util.Number(value || 0, ' ', 2)} ${item.currency}`;
            },
            summary: (items) => {
              if (!items)
                return;

              let currency = items.reduce((prev, item) => {
                if ((item.status & 4) != 0)
                  return prev
                prev[item.currency] = prev[item.currency] || 0
                prev[item.currency] += item.advance_payment || 0
                return prev
              }, {});

              return Object.keys(currency).map((key) => {
                return `${Util.Number(currency[key], ' ', 2)} ${key}`
              }).join('<br/>')
            }
          },
          ]
        }
      },
      spec: {
        group: "contract.spec",
        readonly: !this.contract.rights || !this.contract.rights.set_specification,
        type: "data-grid",
        label: "!spec",
        dataSource: {
          actions: [
            {
              label: "save_changes",
              hidden: () => {
                if (!this.contract.rights || !this.contract.rights.set_specification) {
                  return true;
                }

                let items = this.properties.spec.dataSource.items;
                if (!items)
                  return true;
                return items.filter((item) => {
                  if ((item.status & 7) != 0)
                    return true
                }).length <= 0;
              },
              handler: async () => {
                await this.save(["spec"]);
              }
            }
          ],
          store: {
            data: this._spec.map((item, index) => {
              //item = item.product;
              return { ...item, status: 0, country: item.country, is_gos: this.contract.is_gos }
            }),
            context: (context) => {

              Object.defineProperty(context, "readonly", {
                configurable: true,
                enumerable: true,
                get: () => {
                  return !this.contract.rights || !this.contract.rights.set_specification;
                }
              });

              context.setCountry = (country) => {
                context.country = country;
                context.status &= ~8
                this.save(["spec"])
              }

              context.checkbox = true;
              Object.defineProperty(context, "bindClass", {
                configurable: true,
                enumerable: true,
                get: () => {

                  if ((context.status & 4) != 0)
                    return "ui-alert ui-alert-danger";

                  if ((context.status & 1) != 0)
                    return "ui-alert ui-alert-success";

                  if ((context.status & 2) != 0)
                    return "ui-alert ui-alert-warning";

                  if ((context.status & 8) != 0)
                    return "ui-alert ui-alert-danger";

                },
              });

              context.actions = [
                {
                  btn_type: "warning",
                  icon: "edit",
                  hidden: () => {
                    return (
                      !context.id
                      || !this.contract.rights
                      || !this.contract.rights.set_specification
                      || ((context.status & 4) != 0)
                    );
                  },
                  handler: async () => {
                    let item = await this.edit_product(context);
                    if (!item || item == 2)
                      return;

                    context.claim_id = item.claim_id || context.claim_id;
                    context.product = item.product || context.product;
                    context.amount = item.amount || context.amount;
                    context.unit = item.unit || context.unit;
                    context.start_price = item.start_price || context.start_price || context.price;
                    context.price = item.price || context.price;
                    context.currency = item.currency || context.currency;
                    context.country = item.country || context.country;
                    context.delivery_address = item.delivery_address || context.delivery_address;
                    context.conditions = item.conditions || context.conditions;
                    context.delivery_month = item.delivery_month || context.delivery_month;
                    context.delivery_year = item.delivery_year || context.delivery_year;

                    context.status |= 2;
                  }
                }
              ]
              return context;
            }
          }
        },
        attr: {
          action_name: Language.t("actions"),
          action_style: "min-width: 110px;text-align:left;",
          buttons: true,
          summary: true,
          not_found: "",
          columns: [
            {
              field: "product", label: "product_name", style: "width: 100%; text-align: left;",
              display: (product) => {
                return product.product_name || product.name
              },
              component: {
                props: ["item"],
                methods: {
                  show_properties(item) {
                    Vue.Dialog({
                      props: ['position', 'product'],
                      template: `
                            <div>
                              <header>{{product.product_name || product.name}}</header>
                              <main>
                                <ui-layout :fields='position.fields' />
                                <ui-layout-group label="Свойства">
                                  <iac-layout-static :value='product.product_properties || product.properties' />
                                </ui-layout-group>
                              </main>
                              <footer>
                                <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                              </footer>
                            </div>
                          `
                    }).Modal({
                      position: new CustomContractViewProduct(item),
                      product: item.product,
                    })
                  }
                },
                template: `
                    <div>
                      <a href='#' v-on:click.prevent='show_properties(item)'>{{item.product.product_name || item.product.name}}</a>
                    </div>
                  `
              },
              summary: (items) => {
                if (!items)
                  return;
                let count = items.filter((item) => {
                  return !((item.status & 4) != 0);
                }).length;
                return `${count} ${Language.t("product", { count: count })}`
              }
            },
            {
              field: "country", label: "country", display: (value) => {
                return (value && value.name) ? value.name : value
              },
              component: {
                props: ["item"],
                readonly: true,
                data: function () {
                  return {
                    dataSource: DataSource.get('ref_country_')
                  }
                },
                methods: {
                  async select_country() {
                    let country = await Vue.Dialog({
                      data: function () {
                        return {
                          search_text: undefined,
                          dataSource: DataSource.get('ref_country_')
                        }
                      },
                      methods: {
                        onItem(item) {
                          this.Close(item);
                        },
                        search(text) {
                          if (this.search_text == text)
                            return;
                          this.search_text = text
                          this.dataSource.query.search(text);
                        },
                      },
                      components: {
                        SearchInput: SearchInput
                      },
                      template: `
                          <div>
                            <main style='flex: 0 0 auto; padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid #ccc; z-index: 1; top: 0; position: sticky; background: #fff'>
                              <SearchInput :text='search_text' v-on:search="search" />
                            </main>                         
                            <main>
                              <ui-list :dataSource='dataSource'  v-on:item='onItem'/>
                            </main>
                            <footer></footer>
                          </div>
                        `
                    }).Modal()

                    if (country && country.exp) {
                      this.item.setCountry(country.exp.value)
                    }
                  }
                },
                template: `
                    <div style='white-space: nowrap'>
                      <span v-if='item.country'>{{item.country.name}}</span>
                      <span v-else>Не указана</span>
                    </div>
                  `
              }
            },
            this.contract.is_gos && {
              field: "account",
              display: (value, item) => {
                let data = this.claim_data[item.claim_id];
                return data && data.account;
              }
            },
            this.contract.is_gos && {
              field: "expense_item_code",
              display: (value, item) => {
                let data = this.claim_data[item.claim_id];
                return data && data.expense_item_code;
              }
            },
            {
              field: "amount",
              style: "white-space: nowrap; text-align: right;",
              component: {
                props: ["item"],
                template: `
                    <div><iac-number :value='item.amount' delimiter=' ' /> {{item.unit}}</div>
                  `
              }
            },
            {
              field: "price", label: "price_unit", style: "white-space: nowrap; text-align: right;",
              display: (value, item) => {
                return `${Util.Number(value, ' ', 2)} ${item.currency}`;
              }
            },
            {
              field: "summary", label: "total", style: "white-space: nowrap; text-align: right;",
              display: (value, item) => {
                return `${Util.Number(item.amount * item.price, ' ', 2)} ${item.currency}`;
              },

              summary: (items) => {
                if (!items)
                  return;

                let currency = items.reduce((prev, item) => {
                  if ((item.status & 4) != 0)
                    return prev
                  prev[item.currency] = prev[item.currency] || 0
                  prev[item.currency] += item.amount * item.price
                  return prev
                }, {});

                var total = Object.keys(currency).map(function (key) {
                  return { currency: key, amount: currency[key] };
                });

                return total.map((item) => {
                  return `${Util.Number(item.amount, ' ', 2)} ${item.currency}`
                }).join('<br/>')
              },

            },

          ]
        }
      },
    }
  }
}
