# Отладка курсов валют в конкурентном листе

## Добавлена отладочная информация

В файл `competitive_list.js` добавлено подробное логирование для выяснения проблемы с курсами валют.

## Что логируется

### 1. Конструктор объекта
```
🏗️ Создание SimplifiedContractCompetitiveList с контекстом: {...}
🏗️ Инициализированы значения: {...}
```

### 2. Изменение валюты
```
🔄 Валюта изменена на: USD
💱 Запрашиваем курс для валюты: USD
💱 Получен курс: 12658.19
💰 Пересчитана цена в UZS: 126581.9
🔄 Обновлено поле exchange_rate: 12658.19
🔄 Обновлено поле price_uzs: 126581.9
```

### 3. Запрос курсов через API
```
🔍 getExchangeRate вызван для валюты: USD
🌐 Запрашиваем курсы валют через API...
📡 Ответ API: {...}
💱 Данные курсов: {...}
💰 Курсы валют: {"USD": 12658.19, "RUB": 161.44, "EUR": 14672.11}
✅ Найден курс для USD : 12658.19
```

### 4. Расчет цены в UZS
```
🧮 calculatePriceUzs: {
  currency: "USD",
  price: "100",
  parsedPrice: 100,
  exchange_rate: 12658.19,
  parsedRate: 12658.19
}
💱 Расчет: 100 * 12658.19 = 1265819
```

## Как тестировать

### 1. Откройте консоль браузера (F12)
- Перейдите на вкладку Console
- Очистите консоль (Ctrl+L)

### 2. Откройте форму добавления поставщика
- Перейдите к конкурентному листу
- Нажмите "Добавить поставщика"
- Следите за сообщениями в консоли

### 3. Выберите валюту
- Выберите "Кыргызский сом" (KGS) или другую валюту
- Проверьте логи в консоли

### 4. Введите цену
- Введите любую цену (например, 100)
- Проверьте, как пересчитывается цена в UZS

## Возможные проблемы и их диагностика

### Проблема 1: Курс не запрашивается
**Симптомы**: Нет логов `🔍 getExchangeRate вызван`
**Причина**: Обработчик `onChange` не срабатывает
**Решение**: Проверить, что поле валюты имеет `attr: { react: true }`

### Проблема 2: API не отвечает
**Симптомы**: Лог `❌ Некорректный ответ API`
**Причина**: Проблемы с API или неправильная структура ответа
**Решение**: Проверить сетевые запросы в DevTools

### Проблема 3: Курс не найден в ответе
**Симптомы**: Лог `❌ Курс для валюты XXX не найден`
**Причина**: Валюта отсутствует в ответе API
**Решение**: Проверить, какие валюты поддерживает API

### Проблема 4: Поля формы не обновляются
**Симптомы**: Курс получен, но поле остается 1
**Причина**: Проблемы с обновлением полей формы
**Решение**: Проверить логи `🔄 Обновлено поле`

### Проблема 5: Неправильный расчет
**Симптомы**: NaN в цене UZS
**Причина**: Некорректные значения цены или курса
**Решение**: Проверить логи `🧮 calculatePriceUzs`

## Структура ответа API

Согласно предоставленному примеру, API возвращает:

```json
{
  "result": [
    {
      "rates": {
        "USD": 12658.19,
        "RUB": 161.44,
        "EUR": 14672.11
      }
    }
  ]
}
```

**Важно**: Данные находятся в поле `result`, а не `data`!

## Исправление проблемы

Если проблема в структуре ответа API, нужно изменить код:

```javascript
// Было:
const { error, data } = response;
const actualData = data || result;

// Должно быть:
const { error, data, result } = response;
const actualData = result || data;
```

## Fallback курсы

Обновлены согласно актуальным данным:
- **USD**: 12,658.19 UZS
- **EUR**: 14,672.11 UZS  
- **RUB**: 161.44 UZS

## Следующие шаги

1. **Протестируйте с отладкой** - выберите валюту и проследите логи
2. **Определите проблему** - где именно прерывается цепочка
3. **Исправьте код** - на основе найденной проблемы
4. **Уберите отладку** - после исправления проблемы

## Ожидаемое поведение

При выборе валюты USD должны появиться логи:
```
🔄 Валюта изменена на: USD
🔍 getExchangeRate вызван для валюты: USD
🌐 Запрашиваем курсы валют через API...
📡 Ответ API: {result: [...]}
💱 Данные курсов: {rates: {...}}
💰 Курсы валют: {USD: 12658.19, ...}
✅ Найден курс для USD : 12658.19
💱 Получен курс: 12658.19
🧮 calculatePriceUzs: {...}
💱 Расчет: 0 * 12658.19 = 0
💰 Пересчитана цена в UZS: 0
🔄 Обновлено поле exchange_rate: 12658.19
🔄 Обновлено поле price_uzs: 0
```

После этого поле "Курс пересчета" должно показать 12658.19 вместо 1.
