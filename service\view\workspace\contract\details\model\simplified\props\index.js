import { getBaseDataProps } from './base_data';
import { getParticipantsProps } from './participants';
import { getRequisitesProps } from './requisites';
// import { getActionsProps } from './actions';
import { getGraphProps } from './graph';
import { getGraphFinanceProps } from './graph_finance';
import { getCompetitiveListProps } from './competitive_list';
import { getSpecProps } from './spec';

export function getAllProps(contractBody) {
  return {
    ...getBaseDataProps(contractBody),
    ...getParticipantsProps(contractBody),
    ...getRequisitesProps(contractBody),
    // ...getActionsProps(contractBody),
    ...getGraphProps(contractBody),
    ...getGraphFinanceProps(contractBody),
    ...getCompetitiveListProps(contractBody),
    ...getSpecProps(contractBody)
  };
}
