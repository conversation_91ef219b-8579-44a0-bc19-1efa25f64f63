# Требования к API бэкенда для упрощённых договоров

## Метод: `contract_create`

### Новые параметры

#### Обязательные для упрощённых договоров:
- **`contract_type`**: `"simplified_contract"` - идентификатор упрощённой закупки
- **`buyer_type_id`**: `number` - тип заказчика (1 = государственный, другие = корпоративные)

### Типы запросов

#### 1. Государственные заказчики (из заявок):
```json
{
  "method": "contract_create",
  "params": {
    "source": [
      {
        "id": "42fb26b5-3079-48b4-b284-9f411a84e2b1",
        "amount": 200000,
        "pos_type": "good",
        "type": "claim"
      }
    ],
    "contract_type": "simplified_contract",
    "buyer_type_id": 1
  }
}
```

#### 2. Корпоративные заказчики (из закупок):
```json
{
  "method": "contract_create",
  "params": {
    "object": {
      "type": "new_purchase",
      "id": "c4613062-d6f9-4756-bea9-87264027c05d"
    },
    "contract_type": "simplified_contract",
    "buyer_type_id": 2
  }
}
```

## Логика проверки БРВ

### Лимиты:
- **Государственные заказчики** (`buyer_type_id == 1`): **25 БРВ**
- **Корпоративные заказчики** (`buyer_type_id != 1`): **50 БРВ**

### Алгоритм:
1. Получить общую сумму договора из `source` или `object`
2. Определить лимит БРВ по `buyer_type_id`
3. Если сумма > лимит → вернуть ошибку `BRV_LIMIT_EXCEEDED`
4. Иначе → создать договор как обычно

## Ответы

### Успешное создание:
```json
{
  "result": {
    "data": "contract_number_123"
  }
}
```

### Ошибка превышения БРВ:
```json
{
  "error": {
    "code": "BRV_LIMIT_EXCEEDED",
    "message": "Вы не можете создать договор упрощенной закупки, так как общая сумма превышает 25 БРВ."
  }
}
```

### Сообщения об ошибках:

#### Для государственных заказчиков:
```
"Вы не можете создать договор упрощенной закупки, так как общая сумма превышает 25 БРВ."
```

#### Для корпоративных заказчиков:
```
"Вы не можете создать договор упрощенной закупки, так как общая сумма превышает 50 БРВ."
```

## Обратная совместимость

### Существующие запросы остаются без изменений:

#### Прямые договоры государственных заказчиков:
```json
{
  "method": "contract_create",
  "params": {
    "source": [...]
  }
}
```

#### Прямые договоры корпоративных заказчиков:
```json
{
  "method": "contract_create",
  "params": {
    "object": {
      "type": "new_purchase",
      "id": "..."
    }
  }
}
```

## Определение типа договора

Бэкенд должен определять тип договора по наличию параметра `contract_type`:
- **Если `contract_type === "simplified_contract"`** → упрощённый договор
- **Иначе** → прямой договор (существующая логика)

## Места вызова на фронтенде

1. **`service/view/workspace/claim/claim_list.js`** - государственные заказчики
2. **`service/view/workspace/purchase/details.js`** - корпоративные заказчики  
3. **`service/view/workspace/purchase/list.js`** - корпоративные заказчики

## Тестовые сценарии

### Тест 1: Государственный заказчик, сумма < 25 БРВ
- **Ожидаемый результат**: Договор создан успешно

### Тест 2: Государственный заказчик, сумма > 25 БРВ
- **Ожидаемый результат**: Ошибка `BRV_LIMIT_EXCEEDED`

### Тест 3: Корпоративный заказчик, сумма < 50 БРВ
- **Ожидаемый результат**: Договор создан успешно

### Тест 4: Корпоративный заказчик, сумма > 50 БРВ
- **Ожидаемый результат**: Ошибка `BRV_LIMIT_EXCEEDED`

### Тест 5: Обратная совместимость
- **Ожидаемый результат**: Существующие прямые договоры работают без изменений
