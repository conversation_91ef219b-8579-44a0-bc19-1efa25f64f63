# Изменения в логике упрощённых договоров

## Проблема
Изначально использовался флаг `is_simplified = true` для всех упрощённых договоров, что было неправильно, поскольку:
- Упрощённые закупки могут проводить как **государственные**, так и **корпоративные** заказчики
- Флаг `is_gos` в системе означает **тип заказчика**, а не тип договора

## Решение
Заменили `is_simplified` на правильную логику:

### 1. Определение типа заказчика (`is_gos`)
```javascript
// В SimplifiedContractBody.constructor()
if (this.context.position_src) {
  this.is_gos = this.context.position_src == 'claim'
} else {
  this.is_gos = this.contract.graphic?.filter((item) => {
    return item.details || item.base
  }).length > 0;
}
```

### 2. Тип процедуры
```javascript
this.proc_type = 'simplified_contract'; // Идентификатор упрощённой закупки
```

## Изменённые файлы

### Основные классы:
- `simplified_body.js` - добавлена логика определения `is_gos`
- `product.js` - поля показываются/скрываются в зависимости от типа заказчика
- `view_product.js` - добавлены поля для государственных заказчиков
- `graph_finance.js` - поля `kls` и `expense_item_code` только для госзаказчиков

### UI Properties:
- `props/spec.js` - добавлены колонки для госзаказчиков, обновлена валидация
- `props/graph_finance.js` - условное отображение колонок

## Логика отображения полей

### Для государственных заказчиков (`is_gos = true`):
- ✅ Стартовая цена и валюта
- ✅ Цена за единицу и валюта  
- ✅ Месяц и год поставки
- ✅ Банковский счет (kls)
- ✅ Код статьи расходов (expense_item_code)
- ❌ Адрес поставки (скрыт)

### Для корпоративных заказчиков (`is_gos = false`):
- ❌ Стартовая цена (скрыта)
- ❌ Цена за единицу (скрыта)
- ❌ Месяц и год поставки (скрыты)
- ❌ Банковский счет (скрыт)
- ❌ Код статьи расходов (скрыт)
- ✅ Адрес поставки

### Общие поля (для всех):
- ✅ Продукт и количество
- ✅ Страна происхождения
- ✅ Условия и характеристики

## Валидация
Обновлена валидация в методе `save()`:
```javascript
let invalid_items = items.filter((item) => {
  return (
    !((item.status & 4) != 0)
    && (
      (this.is_gos && !item.price)           // Цена обязательна для госзаказчиков
      || (this.is_gos && !item.delivery_month) // Месяц поставки для госзаказчиков
      || (this.is_gos && !item.delivery_year)  // Год поставки для госзаказчиков
      || !item.country                         // Страна обязательна для всех
      || !item.conditions                      // Условия обязательны для всех
      || (!this.is_gos && !item.delivery_address) // Адрес поставки для корпоративных
    )
  )
});
```

## Результат
Теперь упрощённые договоры корректно поддерживают:
- ✅ Государственных заказчиков с полным набором полей
- ✅ Корпоративных заказчиков с упрощённым набором полей
- ✅ Правильную валидацию в зависимости от типа заказчика
- ✅ Совместимость с существующей логикой системы
