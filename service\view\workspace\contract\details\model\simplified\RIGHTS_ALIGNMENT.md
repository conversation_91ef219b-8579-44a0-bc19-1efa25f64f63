# Выравнивание прав доступа в упрощённых договорах

## Проблема
В упрощённых договорах использовались специфические права доступа, которые не возвращались бэкендом:
- `set_competitive_list_data` - для конкурентного листа
- Возможно, другие специфические права

## Решение
Приведены права доступа в соответствие с прямыми договорами, используя существующие права:

### Конкурентный лист
**Было**: `set_competitive_list_data`  
**Стало**: `set_graph_finance_data`

**Обоснование**: Конкурентный лист логически связан с финансовой частью договора, поэтому использует то же право, что и график финансирования.

### График источников финансирования (graph)
**Используется**: `set_graph_data` ✅ (уже правильно)

### Детальный график финансирования (graph_finance)  
**Используется**: `set_graph_finance_data` ✅ (уже правильно)

### Базовые данные
**Используется**: `set_contract_data` ✅ (уже правильно)

### Участники
**Используется**: `set_contragent_data` ✅ (уже правильно)

### Реквизиты
**Используется**: `set_org_requisites`, `set_part_requisites` ✅ (уже правильно)

## Изменённые файлы

### 1. `props/competitive_list.js`
```javascript
// Было:
readonly: !contractBody.contract.rights || !contractBody.contract.rights.set_competitive_list_data

// Стало:
readonly: !contractBody.contract.rights || !contractBody.contract.rights.set_graph_finance_data
```

### 2. `simplified_body.js`
```javascript
// Валидация - было:
if (this.contract.rights?.set_competitive_list_data) {

// Валидация - стало:
if (this.contract.rights?.set_graph_finance_data) {

// Сохранение - было:
if (this.contract.rights.set_competitive_list_data && (!handle_actions || handle_actions.indexOf("competitive_list") != -1)) {

// Сохранение - стало:
if (this.contract.rights.set_graph_finance_data && (!handle_actions || handle_actions.indexOf("competitive_list") != -1)) {

// Действие - было:
name: 'set_competitive_list_data'

// Действие - стало:
name: 'set_graph_finance_data'
```

## Обновлённая документация

### API документация (`COMPETITIVE_LIST_API.md`)
- Изменено действие с `set_competitive_list_data` на `set_graph_finance_data`
- Обновлены примеры запросов
- Исправлена документация прав доступа

### Описание изменений (`COMPETITIVE_LIST_CHANGES.md`)
- Обновлены требования к бэкенду
- Исправлены примеры API

## Преимущества изменений

### 1. Совместимость с существующим бэкендом
- Не требуется добавление новых прав доступа
- Используются уже существующие и работающие права
- Немедленная работоспособность функционала

### 2. Логическая согласованность
- Конкурентный лист связан с финансовой частью договора
- Использует то же право, что и график финансирования
- Единообразие в управлении правами

### 3. Упрощение интеграции
- Бэкенду нужно только добавить обработку параметра `competitive_list`
- Не нужно изменять систему прав доступа
- Минимальные изменения на стороне сервера

## Требования к бэкенду

### Обработка конкурентного листа в `set_graph_finance_data`
Действие должно принимать дополнительный параметр:

```json
{
  "action": "set_graph_finance_data",
  "data": {
    "graph_finance": [...],           // Существующий параметр
    "graph_finance_totalcost": [...], // Существующий параметр
    "competitive_list": [             // Новый параметр
      {
        "supplier_name": "ООО Поставщик",
        "supplier_inn": "123456789",
        "price": 1000.00,
        "currency": "UZS",
        "exchange_rate": 1,
        "price_uzs": 1000.00
      }
    ]
  }
}
```

### Валидация
- Если передан `competitive_list`, проверить минимум 3 поставщика
- Валидировать ИНН (9 цифр)
- Проверить корректность цен и расчётов

### Возврат данных
Включить `competitive_list` в ответ `contract_detailed`:

```json
{
  "number": "contract_number",
  "graph_finance": [...],
  "competitive_list": [...],  // Добавить
  // ... другие данные
}
```

## Результат

✅ **Конкурентный лист теперь использует существующие права доступа**  
✅ **Совместимость с текущим бэкендом**  
✅ **Минимальные изменения для интеграции**  
✅ **Логическая согласованность прав доступа**

Теперь конкурентный лист должен отображаться и работать корректно, используя те же права, что и график финансирования!
