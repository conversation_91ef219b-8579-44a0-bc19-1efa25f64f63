import Vue from "vue";
import { Context } from "@iac/kernel";
import { DataSource } from "@iac/data";
import { Http } from '@iac/core';

const Component = {
  props: ["model"],
  data: function () {
    return {
      user: Context.User,
      calculated_ref_status: this.model.ref_status || "contract_status",
    }
  },
  computed: {},
  methods: {
    openProducts(item) {
      const { lotNum, model: { goods_count, meta } } = this;
      Vue.Dialog.products.Modal({
        size: 'right',
        model: {
          title: `${this.$t('contract')} №: ${this.model.number}`,
          count: goods_count,
          items: new DataSource({
            limit: 1000,
            query: {
              number: {
                value: item.number
              }
            },
            store: {
              method: "contract_ref",
              ref: "specs",
              injectQuery(params) {
                return { ...params, limit: undefined, offset: undefined }
              },
              inject: (items) => {
                return items[0]?.spec.map((item) => {
                  item.product_name = item.product?.name;
                  item.product_properties = item.product?.properties;
                  return item;
                })
              }
            }
          }),
        }
      })
    },
    link(item) {
      return `/workspace/contract/${item.number}`
    },
    async open_file(e) {
      e.preventDefault();
      this.$wait(async () => {
        if (this.model.contract_body.custom || !this.model.contract_body.link) {
          await Vue.Dialog.editContractFileBody.Modal({
            size: 'full',
            readonly: true,
            value: this.model.contract_body,
            number: this.model.number
          })
          return;
        }
        if (Context.User.id) {
          await Context.User.refreshToken();
          window.location = `${this.model.contract_body.link}?token=${Context.User.access_token}`
        } else {
          window.location = `${this.model.contract_body.link}`
        }
      })

    },
    async download_contract() {

      this.$wait(async () => {
        const { data, error } = await Http.report.rpc('render_free_report', {
          ref: this.model.ref_status == "auction_contract_status" ? 'contract_auction' : 'contract',
          template: this.model.ref_status == "auction_contract_status" ? 'electron_contract_auction_base' : 'electron_contract_base',
          type: 'pdf',
          params: {
            number: this.model.number,
          },
        }, {
          format: 'blob'
        });
        if (error && error.code == "AbortError")
          return;
        if (error !== undefined) {
          await Vue.Dialog.MessageBox.Error(error);
          return;
        }
        const url = URL || webkitURL;
        const fileUrl = url.createObjectURL(data);
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = `${this.$t('contract')}_№${this.model.number}.pdf`;
        link.click();
        url.revokeObjectURL(data);
      });
    },
  },
  template: `
        <ui-data-view-item :model='model'>
            
            <template slot='header'>
                <div>
                  <iac-entity-edit  v-if='model.number' :value='{id: model.id, type: "contract", title: "№"+model.number}' />
                  <iac-date :title='$t("contract.contract_close_at")' v-if='model.contract_close_at' :date="model.contract_close_at" withoutTime /></div>
                <div><ui-ref :title='model.reason_cancellation' :source='calculated_ref_status' :value='model && model.status' /></div>
            </template>
            
            <template slot='title'>
                <router-link v-if='user.id' :to='link(model)'>{{ model.contract_name || ($t('contract') + ' №' + model.number) }}</router-link>
                <div v-else>{{ model.contract_name || ($t('contract') + ' №' + model.number) }}</div>
            </template>

            <template slot='sub_title'>
              <a href="#" v-if='model.goods_count > 0' @click.prevent='openProducts(model)'>{{ model.goods_count }} {{ $t('position', { count: model.goods_count }) }}</a>
            </template>

            <template slot='description'>
                <div class='clamp_2'>
                    <label>{{ $t([model.proc_type+'.initiator','contract.initiator']) }}: </label>
                    <span :title='model.org_company.company_title'>{{ model.org_company.company_title }}</span>
                </div>
                <div class='clamp_2' v-if='model.contragent_company.company_title'> 
                    <label>{{ $t([model.proc_type+'.contragent','contract.contragent']) }}: </label>
                    <span :title='model.contragent_company.company_title'>{{ model.contragent_company.company_title }}</span>
                </div>
                <div v-if='model.contract_type'>
                  <label>{{ $t('contract_type') }}: </label>
                  <span><ui-ref source='ref_contract_type' :value='model.contract_type'/></span>
                </div>
                <div class='clamp_4' v-if='model.reason_cancellation'>
                    <label>{{ $t('contract.reason_cancellation') }}: </label>
                    <span :title='model.reason_cancellation'>{{ model.reason_cancellation }}</span>
                </div>
                <div class='clamp_4' v-if='model.real_end_date'>
                    <label>{{ $t('contract.delivery_date') }}: </label>
                    <span><iac-date :date="model.real_end_date" withoutTime /></span>
                </div>
                <div class='clamp_4' v-if='model.confirm_income_date'>
                    <label>{{ $t('contract.confirm_income_date') }}: </label>
                    <span><iac-date :date="model.confirm_income_date" withoutTime /></span>
                </div>
                <div class='clamp_4 content_debug' v-if='$develop.content_debug && model.stuck_tries'>
                    <label>{{ $t('stuck_tries') }}: </label>
                    <span :title='model.stuck_tries'>{{ model.stuck_tries }}</span>
                </div>
            </template>
            <template slot='props'>
                <div v-if='model.lot_id'>
                  <label>{{ $t('contract.lot_id') }}:</label>
                  <div style='text-align: right'>{{ model.lot_id }}</div>
                </div>

                <div v-if='model.tender_totalcost'>
                  <label>{{ $t('contract.tender_totalcost') }}:</label>
                  <div><iac-number :value='model.tender_totalcost' delimiter=' ' part='2' />&nbsp;<span :title="model.currency">{{ model.currency }}</span></div>
                </div>

                <div v-if='model.winner_totalcost'>
                  <label>{{ $t('contract.winner_totalcost') }}:</label>
                  <div><iac-number :value='model.winner_totalcost' delimiter=' ' part='2' />&nbsp;<span style="white-space: break-spaces;" :title="model.contragent_currency || model.currency">{{ model.contragent_currency || model.currency }}</span></div>
                </div>

                <div v-if='model.spec_totalcost'>
                  <label>{{ $t('contract.totalcost') }}:</label>
                  <div style='text-align: right'>
                    <div v-for='total in model.spec_totalcost'>
                      <iac-number :value='total.amount' delimiter=' ' part='2' />&nbsp;{{ total.currency }}
                    </div>
                  </div>
                </div>

                <div v-if='model.ref_status == "auction_contract_status" || model.ref_status == "online_shop_contract_status"' >
                  <label>{{$t('contract.file')}}:</label>
                  <div>
                    <a :href='$t("contract") + "_№"+model.number+".pdf"' @click.prevent='download_contract'>{{ $t('contract') }}</a>
                  </div>
                </div>
                <div v-else-if='model.contract_body' >
                  <label>{{$t('contract.file')}}:</label>
                  <div  style='text-align: right'>
                    <a v-if='model.contract_body.link && !model.contract_body.custom' v-bind:href='model.contract_body.link' v-on:click='open_file'>{{model.contract_body.meta.name}}</a>
                    <a v-else href='javascript:void(0)' v-on:click='open_file'>{{$t('contract')}}</a>
                  </div>
                </div>

                <div v-if='model.public_date'>
                  <label>{{ $t('contract.published_at') }}:</label>
                  <div style='text-align: right'>
                    <iac-date :date="model.public_date" withoutTime />                   
                  </div>
                </div>

                <div v-if='model.status=="__wait_close_other"'>
                  <label>{{ $t('contract.wait_count') }}:</label>
                  <div>{{ model.total_count - model.close_count }} ({{model.total_count}}) {{ $t('contract.wait_count_text',{count: model.total_count - model.close_count}) }}</div>
                </div>

            </template>
        
        </ui-data-view-item>
    `
}

Vue.component("template-contract", Component)
