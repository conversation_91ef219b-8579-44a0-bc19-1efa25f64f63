import { Http, Language, ModelProvider } from '@iac/core'
import { Entity, Property } from '@iac/data'
import { Settings, Context, ecp, Develop, ecpManger } from '@iac/kernel'

class CompanyModelProperty extends Property {
    constructor(context) {
        super(context)
        if (this.type != 'model' && this.model.external_company && Settings.external_company)
            this.required = true;

        this.update_field_react();
    }

    update_field_react(field = this) {
        field.attr = field.attr || {};
        field.attr.react = true;
        if (field.type == 'model') {
            field.fields.forEach(field => {
                this.update_field_react(field);
            });
        }
    }

}

@ModelProvider("company_model")
export default class CompanyModel extends Entity {
    constructor(context = {}) {
        super(context)

        this.external_company = context.external_company;

        this.id = context.id;

        this.inn = context.inn;
        this.broker_public_id = `${Language.t('company.broker_code')} ${context.broker_public_id}`;

        this.sustainability_rating = (() => {
            let { type, score } = context?.meta?.org_rating_minfin || {}
            if (!type)
                return;            
            let ratingLevel = "";
            switch (type[0]) {
                case 'A':
                    ratingLevel = "rating_high";
                    break;
                case 'B':
                    ratingLevel = "rating_medium";
                    break;
                case 'C':
                    ratingLevel = "rating_satisfactory";
                    break;
                default:
                    ratingLevel = "rating_low";
                    break;
            }
            return `${type} ${score} ${Language.t(ratingLevel)}`
        })()

        this.fulltitle = context.fulltitle;
        this.title = context.title;
        this.anno = context.anno;
        this.activity_area_id = context.activity_area_id;
        this.parent_org_name = context.parent_org_name;
        this.parent_org_inn = context.parent_org_inn && Number(context.parent_org_inn);
        this.company_type_code = context.company_type_code;
        this.oked_code = context.oked_code;
        //this.isFilial = context.isFilial;
        this.org_code = context.org_code;
        if (this.org_code) {
            this.isFilial = true
        }
        this.registration_company_status = context.registration_status + (context.meta?.status_sync_with_gnk?.status_full_name ? " (" + context.meta?.status_sync_with_gnk?.status_full_name + ")" : "")
        this.registration_status_date = context.meta?.status_sync_with_gnk?.last_synced_at

        this.director_name = context.director_name;
        this.director_inn = context.director_inn && Number(context.director_inn);
        this.director_pinfl = context.director_pinfl && Number(context.director_pinfl);

        if (context.roles && Array.isArray(context.roles)) {
            context.roles.forEach((key) => {
                context[`is_${key}`] = true
            })
        }
        context.is_exchange_client = context.is_exchanger;



        this.is_seller = context.is_seller
        this.is_buyer = context.is_buyer
        this.is_broker = context.is_broker
        this.is_exchanger = context.is_exchanger //is_exchange_client
        this.is_exchange_client = context.is_exchange_client //is_exchange_client
        this.is_cargoer = context.is_cargoer
        this.is_comm_buyer = context.is_comm_buyer
        this.is_bank_depository = context.is_bank_depository;

        this.is_broker_request = context.is_broker_request;

        this.is_small_business = context.is_small_business;
        this.free_economic_zone_id = context.free_economic_zone_id ? context.free_economic_zone_id + "" : undefined;
        this.soogu_code = context.soogu_code;
        this.is_strategical = context.is_strategical;

        // alert(this.is_strategical);

        this.email = context.email;
        this.web = context.web;
        this.phone = context.phone;
        this.beneficiary = context.beneficiary;

        this.postal_area_id = context.postal_area_id;
        this.postal_index = context.postal_index;
        this.postal_address = context.postal_address;
        this.is_legal_address_same_as_postal = context.is_legal_address_same_as_postal;
        this.legal_area_id = context.legal_area_id;
        this.legal_index = context.legal_index;
        this.legal_address = context.legal_address;

        this.country = context.country;
        this.address = context.address;


        this.context = context;
        this.director_passport = context.director_passport;
        this.director_passport_date = context.director_passport_date;
        this.bank_requisites = context.bank_requisites;
    }
    get propertyModel() {
        return CompanyModelProperty;
    }
    props() {
        let self = this;
        let hidden_edit = function (for_create = false) {
            if (self.external_company && Settings.external_company && self.id && !Context.Access.policy['system_page_edit']) {
                return true;
            }

            let context_value = self.context[this.name];
            return (self.id || for_create) && !Context.Access.policy['system_page_edit'] && (context_value || this.name == 'is_legal_address_same_as_postal')
        }
        let hidden_edit_bank_requisites = function () {
            if (self.external_company && Settings.external_company && self.id && !(Context.Access.policy['system_page_edit'] && Context.Access.policy['company_foreign_edit_root_payment_account'])) {
                return true
            }
            else {
                if (self.id) {
                    if (self.context.roles.includes("seller")) //если счет уже был активирован то запрет редактирования
                        return true
                }
                return false
            }
        }

        if (this.external_company && Settings.external_company)
            return {

                fulltitle: {
                    label: "full_title",
                    group: 'general/-!title',
                    readonly: hidden_edit
                },
                title: {
                    label: 'short_title',
                    group: 'general/-!title',
                    readonly: hidden_edit
                },

                inn: {
                    label: '~inn_or_reg',
                    group: 'general',
                    readonly: hidden_edit
                },
                country: {
                    label: '~company.country_residence',
                    group: 'general',
                    type: "entity",
                    dataSource: "ref_country",
                    readonly: hidden_edit
                },
                address: {
                    label: 'address',
                    group: 'general',
                    type: "text",
                    readonly: hidden_edit
                },
                registration_status: {
                    group: 'general',
                    type: 'static',
                    label: "~registration_status",
                    hidden: () => {
                        return !this.id
                    }
                },
                registration_status_date: {
                    group: 'general',
                    type: 'widget',
                    widget: {
                        name: 'iac-date',
                        props: {
                            date: this.registration_status_date,
                            full: true
                        }
                    },
                    label: '~registration_status_date',
                    hidden: () => {
                        return !this.id || !this.registration_status_date
                    }
                },

                // Данные руководителя
                director_name: {
                    group: 'company.director_data',
                    label: '~company.director_name_representative',
                    readonly: hidden_edit
                },
                director_passport: {
                    group: 'company.director_data',
                    label: '~company.passport_series_number',
                    readonly: hidden_edit
                },
                director_passport_date: {
                    group: 'company.director_data',
                    label: '~company.passport_valid_until',
                    type: "date",
                    readonly: hidden_edit
                },


                email: {
                    label: '~email',
                    group: 'company.contact_data',
                    readonly: hidden_edit
                },
                phone: {
                    label: '~phone',
                    group: 'company.contact_data',
                    readonly: hidden_edit
                },

                bank_requisites: {
                    label: "!",
                    group: 'company.bank_requisites',
                    type: "model",
                    readonly: hidden_edit_bank_requisites,
                    fields: {
                        bank_name: {
                            label: "~company.bank_name",
                            required: true
                        },
                        bank_bik: {
                            label: "~company.bank_code_bik",
                            required: true
                        },
                        bank_swift: {
                            label: "~company.swift",
                            required: true
                        },
                        bank_country: {
                            label: "~company.bank_country",
                            type: "entity",
                            dataSource: "ref_country",
                            required: true
                        },
                        bank_address: {
                            label: "~company.bank_address",
                            required: true
                        },
                        account: {
                            label: "~company.client_account",
                            required: true
                        },
                        account_currency: {
                            label: "~company.account_currency",
                            type: "entity",
                            dataSource: "ref_currency",
                            required: true
                        }
                    }
                },
                requirement: {
                    type: 'widget',
                    label: "!",
                    hidden: this.id,
                    widget: {
                        name: {
                            template: `
                            <ui-layout-group label='external_company_requirements' v-if='$content.external_company_requirements'>
                                <ui-alert type='warning' v-for='requirement in $content.external_company_requirements'>
                                    <p style='color: #333; font-size: 16px'>{{requirement.title}}</p>
                                    <p style='white-space: break-spaces; color: #333; font-size: 14px'>{{requirement.content}}</p>
                                </ui-alert>
                            </ui-layout-group>
                            `
                        }
                    }
                }
            }

        return {
            inn: {
                label: '-inn',
                group: 'general',
                type: "static",
            },
            broker_public_id: {
                label: '-real_broker',
                group: 'general',
                type: "static",
                description: "$public_broker_id",
                hidden: () => {
                    return !this.broker_public_id || !this.is_broker
                }
            },
            sustainability_rating: {
                label: '-sustainability_rating',
                group: 'general',
                type: "static",
                hidden: () => {
                    return !this.id
                }
            },           
            fulltitle: {
                label: "full_title",
                group: 'general/-!title',
                required: true,
                readonly: function () {
                    return hidden_edit.call(this, true);
                }
            },
            title: {
                label: 'short_title',
                group: 'general/-!title',
                required: true,
                readonly: function () {
                    return hidden_edit.call(this, true);
                }
            },

            anno: {
                group: 'general',
                type: 'text',
                required: true
            },

            activity_area_id: {
                type: "entity",
                group: "general",
                required: true,
                dataSource: "ref_country_area",
                readonly: hidden_edit
            },

            parent_org_name: {
                group: 'general/!parent_org-'
            },
            parent_org_inn: {
                group: 'general/!parent_org-',
                type: "number",
                attr: {
                    predicate: "^(\\d+)$",
                },
                hidden() {
                    return Settings._country == 'KG'
                }
            },

            company_type_code: {
                group: "general/-!codes",
                type: 'entity',
                required: true,
                dataSource: "ref_company_type",
                readonly: function () {
                    return hidden_edit.call(this, true);
                }
            },
            oked_code: {
                group: "general/-!codes",
                type: 'entity',
                required: true,
                dataSource: "ref_economic_acivity_type"
            },

            isFilial: {
                label: 'is_filial',
                group: 'general/-!filial/!left',
                type: 'bool',
                readonly: () => {
                    return this.context.org_code
                },
            },
            org_code: {
                group: 'general/-!filial/!right',
                description: '$org_code_description',
                readonly: () => {
                    return this.context.org_code
                },
                hidden: () => {
                    return !this.isFilial
                }
            },

            director_name: {
                group: 'general/!director-',
                required: true,
                readonly: function () {
                    return hidden_edit.call(this, true);
                }
            },
            director_inn: {
                group: 'general/!director-',
                type: "number",
                //required: true,
                attr: {
                    predicate: "^(\\d+)$",
                },
                readonly: () => {
                    //if (!this.company.direktor)
                    //    return false
                    //return this.company.direktor.inn
                },
                hidden() {
                    return true;// Settings._country == 'KG'
                }
            },
            director_pinfl: {
                group: 'general/!director-',
                type: "number",
                required: true,
                attr: {
                    predicate: "^(\\d+)$",
                },
                readonly: function () {
                    return hidden_edit.call(this, true);
                }
            },

            registration_company_status: {
                group: 'general',
                type: 'static',
                label: "~registration_company_status",
                hidden: () => {
                    return !this.id
                }
            },
            registration_status_date: {
                group: 'general',
                type: 'widget',
                widget: {
                    name: 'iac-date',
                    props: {
                        date: this.registration_status_date,
                        full: true
                    }
                },
                label: '~registration_status_date',
                hidden: () => {
                    return !this.id || !this.registration_status_date
                }
            },

            role_banner_1: {
                type: "widget",
                group: "role_in_the_system",
                label: "!",
                hidden: () => {
                    if (this.id &&
                        this.context.is_seller &&
                        this.context.is_buyer &&
                        (this.context.is_broker || !Settings.exchange)) {
                        return true;
                    }
                },
                widget: {
                    name: {
                        props: ["id", "is_cargoer", "is_broker", "is_exchange_client", "is_buyer", "is_seller","is_bank_depository"],
                        template: `
                            <ui-alert type='info'>
                                <ul>
                                    <li v-if='!id'>{{$t('company.reg.info_1')}}</li>
                                    <li v-if='!is_buyer || !is_seller'>{{$t('company.reg.info_2')}}</li>
                                    <li v-if='!is_broker' v-if='$settings.exchange' >{{$t('company.reg.info_3')}}</li>
                                </ul>
                            </ui-alert>
                        `
                    },
                    props: {
                        id: this.id,
                        is_seller: this.context.is_seller,
                        is_buyer: this.context.is_buyer,
                        is_broker: this.context.is_broker,
                        is_exchange_client: this.context.is_exchange_client,
                        is_cargoer: this.context.is_cargoer,
                        is_bank_depository: this.context.is_bank_depository
                    },
                }
            },

            is_seller: {
                label: "seller",
                group: "role_in_the_system/-!seller/!left",
                type: 'bool',
                readonly: () => {
                    return this.context.is_seller
                },
                //description: Language.t("company_reg_is_seller_attention"),
                onChange: function (value) {
                    if (value) {
                        this.description = undefined;//Language.t("company_reg_is_seller_attention")
                    } else {
                        this.description = undefined;
                    }
                }
            },
            is_small_business: {
                group: "role_in_the_system/-seller/!right",
                type: 'bool',
                hidden: () => {
                    return !this.is_seller
                }
            },
            free_economic_zone_id: {
                group: "role_in_the_system/-seller/!right",
                type: 'entity',
                dataSource: {
                    valueExp: 'id',
                    //search: true,
                    store: {
                        key: 'id',
                        method: 'ref_free_economic_zone',
                    }
                },
                hidden: () => {
                    return !this.is_seller || Settings._country == 'KG'
                }
            },


            is_buyer: {
                label: "government_corporate_buyer",
                group: "role_in_the_system/-!buyer/!left",
                type: 'bool',
                readonly: () => {
                    return this.context.is_buyer || this.is_comm_buyer || this.context.is_comm_buyer
                },
                onChange: function (value) {
                    if (value) {
                        this.description = undefined;
                    } else {
                        this.description = undefined;
                    }
                }
            },
            soogu_code: {
                group: "role_in_the_system/-buyer/!right",
                type: 'entity',
                dataSource: "ref_soogu",
                hidden: () => {
                    return !this.is_buyer
                },
            },
            is_strategical: {
                group: "role_in_the_system/-buyer/!right",
                type: 'bool',
                //value: false,
                hidden: () => {
                    return !this.is_buyer
                },
            },
            is_comm_buyer: {
                label: "commercial_buyer",
                group: "role_in_the_system/-!comm_buyer/!left",
                type: 'bool',
                readonly: () => {
                    return this.context.is_comm_buyer || this.is_buyer || this.context.is_buyer
                },
                hidden: () => {
                    return !Settings.commercial_buyer
                },
                onChange: function (value) {
                    if (value) {
                        this.description = undefined;
                    } else {
                        this.description = undefined;
                    }
                }
            },
            is_exchange_client: {
                label: "exchange_client",
                group: "role_in_the_system/-!exchanger/!left",
                type: 'bool',
                hidden: () => {
                    if (!Settings.exchange) return true;
                    //if (this.is_broker || !this.id || Settings._country == 'KG') return true;
                },
                readonly: () => {
                    return this.context.is_exchange_client;
                },
                //description: Language.t("company_reg_is_seller_attention"),
                onChange: function (value) {
                    if (value) {
                        this.description = undefined;//Language.t("company_reg_is_seller_attention")
                    } else {
                        this.description = undefined;
                    }
                }
            },

            is_broker: {
                label: "broker",
                group: "role_in_the_system/-!is_broker/!left",
                type: 'bool',
                //description: Language.t("company_reg_is_seller_attention"),
                hidden: () => {
                    if (!Settings.exchange) return true;
                },
                readonly: () => {
                    return true;
                },
                onChange: function (value) {
                    if (value) {
                        this.description = undefined;//Language.t("company_reg_is_seller_attention")
                    } else {
                        this.description = undefined;
                    }
                }
            },
            broker_request: {
                label: "!",
                group: "role_in_the_system/-!is_broker/!right",
                type: "action",
                buttons: true,
                hidden: () => {
                    if (!Settings.exchange) return true;

                    if (!this.id) return true;
                    if (this.context.is_broker) return true;
                    if (this.is_broker_request) return true;
                    if (Context.Access.policy['system_page_edit']) return false;
                    // return true;
                    // if (this.is_exchange_client || !this.id || Settings._country == 'KG') return true;
                    // if (Context.Access.policy['system_page_edit']) return true;
                    // return this.is_broker || this.is_broker_request
                },
                actions: [
                    {
                        label: "send_request",
                        handler: async () => {
                            let { error, data } = await Http.api.rpc("company_ref", {
                                "ref": "permissive_documents",
                                "op": "upload",
                                "type": "pd_broker",
                                "data": { "request_question": true }
                            });
                            if (error && error.code != "AbortError") {
                                Vue.Dialog.MessageBox.Error(error);
                            } else if (data) {
                                this.is_broker_request = true;
                            }
                        }
                    }
                ]
            },
            broker_info: {
                label: "!",
                group: "role_in_the_system/-!is_broker/!right",
                type: 'widget',
                widget: {
                    name: "ui-alert",
                    props: {
                        type: "success",
                        style: "margin: 0"
                    },
                    content: Language.t("send_request_success")
                },
                //value: false,
                hidden: () => {
                    if (!Settings.exchange) return true;
                    if (this.is_broker || !this.id || Settings._country == 'KG') return true;
                    if (Context.Access.policy['system_page_edit']) return true;
                    return this.is_broker || !this.is_broker_request
                },
            },

            is_cargoer: {
                label: "cargoer",
                group: "role_in_the_system/-!is_cargoer/!left",
                type: 'bool',
                hidden: () => {
                    if (!Settings.exchange || !Settings.procedures?.cargo_procedure) return true;
                },
                readonly: () => {
                    return false;
                },
                onChange: function (value) {
                    if (value) {
                        this.description = undefined;
                    } else {
                        this.description = undefined;
                    }
                }
            },
            is_bank_depository: {
                label: "bank_depository",
                group: "role_in_the_system/-!is_bank_depository/!left",
                type: 'bool',
                hidden: () => {
                    return !this.is_bank_depository;
                },
                readonly: () => {
                    return true;
                }
            },

            email: {
                label: '~email',
                group: 'additionally',
                required: true,
            },
            web: {
                label: '~web',
                group: 'additionally',
            },
            phone: {
                label: '~phone',
                group: 'additionally',
                required: true,
            },
            beneficiary: {
                group: 'additionally',
                type: 'text',
            },



            postal_area_id: {
                required: true,
                group: "requisites.address/!postal-",
                type: "entity",
                dataSource: "ref_uz_area_lv4",
                readonly: hidden_edit,
                hidden: function () {
                    if (!Settings.register_with_address) return true;
                }
            },
            postal_index: {
                required: true,
                group: "requisites.address/!postal-",
                readonly: function () {
                    return hidden_edit.call(this, true);
                },
                hidden: function () {
                    if (!Settings.register_with_address) return true;
                }
            },
            postal_address: {
                required: true,
                group: "requisites.address/!postal-",
                readonly: function () {
                    return hidden_edit.call(this, true);
                },
                hidden: function () {
                    if (!Settings.register_with_address) return true;
                }
            },
            is_legal_address_same_as_postal: {
                group: "requisites.address",
                type: "bool",
                readonly: hidden_edit,
                hidden: function () {
                    if (!Settings.register_with_address) return true;
                }
            },
            legal_area_id: {
                required: true,
                group: "requisites.address/!legal-",
                type: "entity",
                dataSource: "ref_uz_area_lv4",
                readonly: hidden_edit,
                hidden: function () {
                    if (!Settings.register_with_address) return true;
                    return this.model.is_legal_address_same_as_postal;
                }
            },
            legal_index: {
                required: true,
                group: "requisites.address/!legal-",
                readonly: hidden_edit,
                hidden: function () {
                    if (!Settings.register_with_address) return true;
                    return this.model.is_legal_address_same_as_postal;
                }
            },
            legal_address: {
                required: true,
                group: "requisites.address/!legal-",
                readonly: hidden_edit,
                hidden: function () {
                    if (!Settings.register_with_address) return true;
                    return this.model.is_legal_address_same_as_postal;
                }
            }

        }
    }

    async save(ecp_key) {
        if (this.validate && this.validate()) {
            return {
                error: {
                    message: Language.t("some_fields_contain_errors")
                }
            };
        } else {
            // Проверка взаимоисключающих ролей
            if (this.is_buyer && this.is_comm_buyer) {
                return {
                    error: {
                        message: Language.t("buyer_comm_buyer_conflict")
                    }
                };
            }
        }
        let params = this.fields.filter((field) => {
            if (field.type === 'static' || field.type === 'widget' || field.type === 'action') {
                return false
            }
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }
            if ((value == '' || value == undefined))
                value = null;

            if (field.type == 'bool' && !value)
                value = false;

            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {});

        if (params.is_seller) {
            params.roles = params.roles || [];
            params.roles.push("seller")
        }
        if (params.is_buyer) {
            params.roles = params.roles || [];
            params.roles.push("buyer")
        }
        if (params.is_exchange_client) {
            params.roles = params.roles || [];
            params.roles.push("exchanger")
        }
        if (params.is_broker) {
            params.roles = params.roles || [];
            params.roles.push("broker")
        }
        if (params.is_cargoer) {
            params.roles = params.roles || [];
            params.roles.push("cargoer")
        }
        if (params.is_comm_buyer) {
            params.roles = params.roles || [];
            params.roles.push("comm_buyer")
        }
        if (params.is_bank_depository) {
           params.roles = params.roles || [];
           params.roles.push("bank_depository")
        }


        params.is_seller = undefined;
        params.is_buyer = undefined;
        params.is_broker = undefined;
        params.is_exchange_client = undefined;
        params.is_cargoer = undefined;
        params.is_bank_depository = undefined;
        params.is_comm_buyer = undefined;
        params.isFilial = undefined;

        if (params.free_economic_zone_id)
            params.free_economic_zone_id = Number(params.free_economic_zone_id);

        if (Develop.new_ecp_develop && ecp_key) {
            let { error, data } = await ecpManger.createPkcs7(JSON.stringify(params), ecp_key)
            if (error)
                return {
                    error: error
                }
            params.pkcs7B64 = data
        } else if (ecp_key) {
            try {
                let keyId = await ecp.loadKey(ecp_key);
                if (!keyId) {
                    throw { message: Language.t("company.key_load_error") }
                }

                let { error, data } = await ecp.createPkcs7(keyId, JSON.stringify(params));
                if (error)
                    throw error
                params.pkcs7B64 = data
            } catch (error) {
                return {
                    error: error
                }
            }
        }

        if (this.id) {
            // сохранить
            params.roles = params.roles || [];
            let { error, data } = await Http.api.rpc('set_company_fields', { ...params, id: this.id })
            if (error) {
                this.setError(error)
            } else {
                Vue.Dialog.MessageBox.Success(data.message);

                this.context.is_seller = this.is_seller
                this.context.is_buyer = this.is_buyer
                this.context.is_broker = this.is_broker
                this.context.is_exchanger = this.is_exchange_client
                this.context.is_exchange_client = this.is_exchange_client
                this.context.is_cargoer = this.is_cargoer
                this.context.is_bank_depository = this.is_bank_depository
                this.context.is_comm_buyer = this.is_comm_buyer

                if (this.id == Context.User.team_id) {
                    Context.User.team_name = params.fulltitle;
                    Context.User.update_face();
                    // Обновить политики если сменилась роль
                    if (this.is_seller != this.context.is_seller || this.is_buyer != this.context.is_buyer) {
                        await Context.Access.updateData()
                    }
                }

                this.context.fulltitle = this.fulltitle;
                this.context.title = this.title;

                this.context.postal_area_id = this.postal_area_id;
                this.context.postal_index = this.postal_index;
                this.context.postal_address = this.postal_address;
                this.context.is_legal_address_same_as_postal = this.is_legal_address_same_as_postal;
                this.context.legal_area_id = this.legal_area_id;
                this.context.legal_index = this.legal_index;
                this.context.legal_address = this.legal_address;


            }
            return {
                error,
                data
            }
        } else {
            // создать
            let { error, data } = await Http.api.rpc('register_face_company', { ...params, external_company: this.external_company })
            this.setError(error)
            return {
                error,
                data
            }
        }
    }

    static async get(id) {
        id = id && Number(id);
        let { data, error } = await Http.api.rpc("get_company_fields", {
            id: id
        });

        if (data) {
            if (data.is_broker_request != false && data.is_broker_request != true) {

                if (data.roles.find(role => role === "broker")) {
                    // data.is_exchange_client = true;
                } else {
                    // Проверяем есть ли у компании запрос на становление клиентом биржи.
                    let response = await Http.api.rpc("company_ref", {
                        "ref": "permissive_documents",
                        "op": "read",
                        "filters": {
                            "type": "pd_broker",
                            "status": ["moderating", "activated"],
                            "company_id": data.id,
                        },
                    })
                    if (Array.isArray(response.data) && response.data.length > 0) data.is_broker_request = true;
                }
            }

            data = Array.isArray(data) ? data[0] : data;
            data = new CompanyModel(data)
        }
        return {
            error,
            data
        }
    }
}
