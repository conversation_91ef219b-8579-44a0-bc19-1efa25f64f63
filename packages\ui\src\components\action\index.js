import Http from "../../../../core/src/http";
import CoreAction from "../../../../core/src/action";

export var Action = {
    name: "ui-action",
    props: {
        actions: {

        },
        icon: {
            type: String,
            default: "toggle"
        },
        buttons: {
            type: Boolean,
            default: false
        },
        handler: {
            type: Function
        },
        readonly: {
            type: Boolean,
            default: false
        },
        top: {

        },
        right: {

        },
        onHandler: {
            type: Function
        },
        margin: {
            type: Boolean,
            default: false
        }
    },
    data: function () {
        return {
            dropdown: false,
            dropdownTop: false,
            dropdownRight: false
        }
    },
    computed: {
        showActionList() {
            let actions = this.actionList;
            if (!actions)
                return false;
            actions = actions.filter((action) => {
                return action.type == 'sep' ? false : true;
            })
            if (actions.length <= 0)
                return false;
            return true;
        },
        actionList() {
            let actions = this.actions;

            if (!actions)
                return;
            if (!Array.isArray(actions)) {
                actions = [actions]
            }

            return actions.filter((action) => {
                if (!action)
                    return false;
                if (typeof action.hidden == 'function')
                    return !action.hidden();
                return !action.hidden;
            })
        },
        classes() {
            return [
                (() => {

                })(),
                {
                    "buttons": this.buttons,
                    "dropdown": this.dropdown,
                    "dropdown-top": this.top!= undefined ? this.top :  this.dropdownTop,
                    "dropdown-right": this.right!= undefined ? this.right : this.dropdownRight,
                }
            ]
        },

    },
    methods: {
        hideContent() {
            this.dropdown = false
        },
        reposition() {
            const elClientRect = this.$el.getBoundingClientRect();
            let clCenter = {
                x: elClientRect.left + elClientRect.width / 2,
                y: elClientRect.top + elClientRect.height / 2
            }
            let htmlSize = {
                width: document.documentElement.clientWidth,
                height: document.documentElement.clientHeight,
            }

            this.dropdownRight = (clCenter.x > htmlSize.width / 2)
            this.dropdownTop = (clCenter.y > htmlSize.height / 2)
        },
        async onCheckItem(item){
            item.on_check();
        },
        async item_handler(item) {
            this.$emit("item_handler",true)
            if(this.onHandler)
                this.onHandler(true);
            if (item.question && await Vue.Dialog.MessageBox.Question(item.question) != Vue.Dialog.MessageBox.Result.Yes) {
                this.$emit("item_handler",false)
                if(this.onHandler)
                    this.onHandler(false);
                return;
            }




            if (item.handler) {
                await item.handler();
                this.dropdown = false;
            } else if (item.type == 'request') {
                let params = {};
                if (item.inject && typeof item.inject == 'function'){
                    let {error,data} = await item.inject() || {};
                    if(error)
                        return;
                        params = {...data}
                }

                let host = item.host ? Http[item.host] : Http.default;
                if (host) {
                    let { error, data } = await host.rpc(item.method, {...item.params,...params},{
                        format: item.download ? 'blob' : undefined
                    });
                    if (error && error.message && error.code != 'AbortError') {
                        Vue.Dialog.MessageBox.Error(error);
                    }
                    
                    if(item.download){
                        const url = URL || webkitURL;
                        const fileUrl = url.createObjectURL(data);
                        const link = document.createElement('a');
                        link.href = fileUrl;
                        link.download = `${item.download}`;
                        link.click();
                        url.revokeObjectURL(data);
                    }

                    if (item.response) {
                        await item.response({data, error});
                    }
                }
            } else if (item.type == 'action'){
                await CoreAction[item.method](item.params);
            }
            this.$emit("item_handler",false)
            if(this.onHandler)
                this.onHandler(false);
        }
    },
    watch: {
        dropdown: {
            immediate: true,
            async handler(val, oldVal) {
                if (val == true) {
                    this.reposition();
                }
            }
        }
    },
    template: `<div v-if='(handler || showActionList) && !readonly' class='ui-action' v-bind:class="classes">
        <ui-btn-group :class='margin ? "margin" : ""' v-if='buttons'>
            <ui-btn v-if='item.type != "sep"' :title='$t(item.label)' :disabled='item.disabled || readonly' :key='index' class='item' v-for='(item,index) in actionList' :type='item.btn_type || "primary"' :active='item.active' @click.native='item_handler && item_handler(item)'>
                <icon v-if='item.icon'>{{item.icon || ''}}</icon>
                <span>{{$t(item.label)}}</span>
            </ui-btn>
        </ui-btn-group>
        <div v-if='!buttons && !handler' class='toggle'><icon v-on:click='dropdown=!dropdown'>{{icon}}</icon></div>
        <template v-if='!buttons && handler'>
            <div v-bind:class='["toggle",{button: showActionList}]'>
                <icon v-on:click='handler()'>{{icon}}</icon>
                <icon v-if='showActionList' style='font-size: 5px' class='arrow' v-on:click='dropdown=!dropdown'>arrow</icon>
            </div>
        </template>
        <div v-if='!buttons && dropdown' class='content' v-on-clickaway="hideContent">
            <div  v-if='showActionList' :key='index' class='item' v-for='(item,index) in actionList'>
                <div v-if='item.type == "sep"'  class='sep content'/>
                <div v-else-if='item.type == "list"' class='content list'>
                    <div class='handler'>
                        <icon>{{item.icon || ''}}</icon> <div class='label'>{{$t(item.label)}}</div>
                    </div>
                    <div>
                        <ui-list :dataSource='item.dataSource'>
                            <div slot='template' slot-scope='props'>
                                <div>{{props.item.data}}</div>
                                <div>{{props.item.name}}</div>
                            </div>
                        </ui-list>
                    </div>
                </div>
                <div v-else class='content action'>
                    <div v-if='!item.on_check' class='handler' v-on:click='item_handler(item)'>
                        <icon>{{item.icon || ''}}</icon>
                        <div class='label'>{{$t(item.label)}}</div>
                    </div>
                    <label v-else class='handler'>
                        <span class='icon'><input :disabled='item.disabled || item.readonly'  type='checkbox' :checked='item.checked' v-on:change='onCheckItem(item)' /></span>
                        <div class='label'>{{$t(item.label)}}</div>
                    </label>
                </div>

            </div>
        </div>
    </div>`
}