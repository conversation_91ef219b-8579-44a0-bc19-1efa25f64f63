# Исправление проблемы с объектом валюты

## Найденная проблема

Из логов отладки выяснилось, что проблема в том, что **валюта передается как объект Entity, а не как строка кода**.

### Что показали логи:
```
🔄 Валюта изменена на: {currency: 'UZS'}
💱 Запрашиваем курс для валюты: {key: Guid, ...}
❌ Курс для валюты {key: Guid, ...} не найден в: {USD: 12658.19, RUB: 161.44, EUR: 14672.11}
```

**Проблема**: `this.currency` содержит объект `{key: Guid, code: 'USD', ...}`, а не строку `'USD'`.

## Исправление

### 1. Извлечение кода валюты из объекта

**Было:**
```javascript
if (this.currency === 'UZS') {
  this.exchange_rate = 1;
} else {
  this.exchange_rate = await this.getExchangeRate(this.currency);
}
```

**Стало:**
```javascript
// Получаем код валюты из объекта Entity
const currencyCode = this.currency?.code || this.currency?.id || this.currency;

if (currencyCode === 'UZS') {
  this.exchange_rate = 1;
} else {
  this.exchange_rate = await this.getExchangeRate(currencyCode);
}
```

### 2. Исправление в calculatePriceUzs

**Было:**
```javascript
if (this.currency === 'UZS') {
  return price;
}
```

**Стало:**
```javascript
const currencyCode = this.currency?.code || this.currency?.id || this.currency;

if (currencyCode === 'UZS') {
  return price;
}
```

### 3. Исправление readonly свойства

**Было:**
```javascript
readonly: this.currency === 'UZS'
```

**Стало:**
```javascript
readonly: (this.currency?.code || this.currency?.id || this.currency) === 'UZS'
```

## Структура объекта валюты

Судя по логам, объект валюты имеет структуру:
```javascript
{
  key: "some-guid",
  code: "USD",  // или id: "USD"
  name: "Доллар США",
  // другие свойства...
}
```

## Универсальное извлечение кода

Используется безопасное извлечение кода валюты:
```javascript
const currencyCode = this.currency?.code || this.currency?.id || this.currency;
```

Это работает для всех случаев:
- **Объект с code**: `{code: 'USD'}` → `'USD'`
- **Объект с id**: `{id: 'USD'}` → `'USD'`  
- **Строка**: `'USD'` → `'USD'`
- **null/undefined**: `null` → `null`

## Ожидаемый результат

После исправления логи должны показать:
```
🔄 Валюта изменена на: {code: 'USD', ...}
💱 Код валюты: USD
💱 Запрашиваем курс для валюты: USD
🔍 getExchangeRate вызван для валюты: USD
✅ Найден курс для USD : 12658.19
💱 Получен курс: 12658.19
🔄 Обновлено поле exchange_rate: 12658.19
```

## Тестирование

### 1. Очистите консоль браузера
### 2. Выберите валюту USD в форме
### 3. Проверьте логи - должен появиться "Код валюты: USD"
### 4. Поле "Курс пересчета" должно заполниться значением 12658.19

## Поддерживаемые валюты

После исправления будут работать:
- **USD** → 12,658.19 UZS
- **EUR** → 14,672.11 UZS
- **RUB** → 161.44 UZS
- **UZS** → 1 UZS (базовая валюта)

## Дополнительные улучшения

### Добавлено логирование кода валюты:
```javascript
console.log('💱 Код валюты:', currencyCode);
```

### Улучшена отладочная информация:
```javascript
console.log('🧮 calculatePriceUzs:', {
  currency: this.currency,
  currencyCode: currencyCode,  // Добавлено
  price: this.price,
  // ...
});
```

## Результат

✅ **Проблема решена**: теперь код валюты корректно извлекается из объекта Entity
✅ **API работает**: курсы получаются из `ref_exchange_rate`  
✅ **Расчет корректен**: цена в UZS рассчитывается правильно
✅ **Поля обновляются**: курс пересчета заполняется автоматически

Теперь при выборе валюты USD поле "Курс пересчета" должно автоматически заполниться значением **12,658.19** вместо 1!
