# Изменения для проверки лимитов БРВ при создании упрощённых договоров

## Проблема
При создании упрощённых договоров не передавался тип заказчика (государственный/корпоративный) на бэкенд, что не позволяло:
- Проверить лимиты БРВ (25 для государственных, 50 для корпоративных заказчиков)
- Показать соответствующие сообщения об ошибках

## Решение

### 1. Добавлен buyer_type_id в Context.User

**Файл:** `kernel/context/user.js`

```javascript
// В конструкторе
this.buyer_type_id = undefined;

// В методе updateData()
this.buyer_type_id = data.company?.buyer_type_id;

// В методе clearData()
this.buyer_type_id = undefined;
```

### 2. Обновлено создание упрощённых договоров

**Файл:** `service/view/workspace/claim/claim_list.js`

#### Добавлена проверка лимитов БРВ на фронтенде:
```javascript
// Проверяем лимиты БРВ перед созданием договора
const totalSum = source.reduce((sum, item) => sum + (item.summa || 0), 0);
const isGosCustomer = Context.User.buyer_type_id == 1;
const brvLimit = isGosCustomer ? 25 : 50;

if (totalSum > brvLimit) {
    const customerType = isGosCustomer ? "бюджетных" : "корпоративных";
    const message = `Вы не можете создать договор упрощенной закупки, так как общая сумма превышает ${brvLimit} БРВ для ${customerType} заказчиков.`;
    Vue.Dialog.MessageBox.Error(message);
    return;
}
```

#### Добавлен buyer_type_id в параметры запроса:
```javascript
let procedure_params = {
    source: source,
    contract_type: 'simplified_contract',
    buyer_type_id: Context.User.buyer_type_id  // ← НОВОЕ
}
```

## Логика проверки

### Типы заказчиков:
- **Государственный заказчик**: `buyer_type_id == 1` → лимит **25 БРВ**
- **Корпоративный заказчик**: `buyer_type_id != 1` → лимит **50 БРВ**

### Сообщения об ошибках:
- **Для бюджетных**: "Вы не можете создать договор упрощенной закупки, так как общая сумма превышает 25 БРВ для бюджетных заказчиков."
- **Для корпоративных**: "Вы не можете создать договор упрощенной закупки, так как общая сумма превышает 50 БРВ для корпоративных заказчиков."

## Данные, передаваемые на бэкенд

### До изменений:
```javascript
{
    source: [...],
    contract_type: 'simplified_contract'
}
```

### После изменений:
```javascript
{
    source: [...],
    contract_type: 'simplified_contract',
    buyer_type_id: 1 // или другое значение
}
```

## Места создания упрощённых договоров

Упрощённые договоры создаются только в одном месте:
- **`service/view/workspace/claim/claim_list.js`** - кнопка "Договор упрощённой закупки"

## Требования к бэкенду

Бэкенд должен:
1. **Принимать параметр `buyer_type_id`** в запросе `contract_create`
2. **Проверять лимиты БРВ** на основе типа заказчика:
   - `buyer_type_id == 1` → максимум 25 БРВ
   - `buyer_type_id != 1` → максимум 50 БРВ
3. **Возвращать ошибку** при превышении лимита с соответствующим сообщением

## Совместимость

Изменения обратно совместимы:
- ✅ Существующие прямые договоры продолжают работать
- ✅ Добавлен новый параметр без нарушения существующего API
- ✅ Проверка на фронтенде дублирует проверку бэкенда для лучшего UX

## Тестирование

Для тестирования нужно:
1. **Создать заявки** с разными суммами
2. **Проверить лимиты** для государственных заказчиков (25 БРВ)
3. **Проверить лимиты** для корпоративных заказчиков (50 БРВ)
4. **Убедиться**, что показываются правильные сообщения об ошибках
