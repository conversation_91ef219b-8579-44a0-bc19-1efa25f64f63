# Изменения для проверки лимитов БРВ при создании упрощённых договоров

## Проблема
При создании упрощённых договоров не передавался тип заказчика (государственный/корпоративный) на бэкенд, что не позволяло:
- Проверить лимиты БРВ (25 для государственных, 50 для корпоративных заказчиков)
- Показать соответствующие сообщения об ошибках

## Решение

### 1. Добавлен buyer_type_id в Context.User

**Файл:** `kernel/context/user.js`

```javascript
// В конструкторе
this.buyer_type_id = undefined;

// В методе updateData()
this.buyer_type_id = data.company?.buyer_type_id;

// В методе clearData()
this.buyer_type_id = undefined;
```

### 2. Обновлено создание упрощённых договоров

#### Файлы:
- **`service/view/workspace/claim/claim_list.js`** (для государственных заказчиков)
- **`service/view/workspace/purchase/details.js`** (для корпоративных заказчиков)
- **`service/view/workspace/purchase/list.js`** (для корпоративных заказчиков)

#### Убрана проверка лимитов БРВ на фронтенде:
Проверка теперь выполняется только на бэкенде для единообразия.

#### Добавлена обработка ошибки БРВ:
```javascript
if (error.code === 'BRV_LIMIT_EXCEEDED') {
    Vue.Dialog.MessageBox.Error(error.message);
} else {
    Vue.Dialog.MessageBox.Error(error);
}
```

#### Добавлен buyer_type_id в параметры запроса:
```javascript
// Для государственных заказчиков (из заявок)
let procedure_params = {
    source: source,
    contract_type: 'simplified_contract',
    buyer_type_id: Context.User.buyer_type_id
}

// Для корпоративных заказчиков (из закупок)
let procedure_params = {
    object: {
        type: "new_purchase",
        id: context.id
    },
    contract_type: 'simplified_contract',
    buyer_type_id: Context.User.buyer_type_id
}
```

## Логика проверки

### Типы заказчиков:
- **Государственный заказчик**: `buyer_type_id == 1` → лимит **25 БРВ**
- **Корпоративный заказчик**: `buyer_type_id != 1` → лимит **50 БРВ**

### Обработка ошибок БРВ:
Бэкенд должен возвращать ошибку с кодом `BRV_LIMIT_EXCEEDED` и соответствующим сообщением:
- **Для бюджетных**: "Вы не можете создать договор упрощенной закупки, так как общая сумма превышает 25 БРВ."
- **Для корпоративных**: "Вы не можете создать договор упрощенной закупки, так как общая сумма превышает 50 БРВ."

## Данные, передаваемые на бэкенд

### До изменений:
```javascript
{
    source: [...],
    contract_type: 'simplified_contract'
}
```

### После изменений:
```javascript
{
    source: [...],
    contract_type: 'simplified_contract',
    buyer_type_id: 1 // или другое значение
}
```

## Места создания упрощённых договоров

Упрощённые договоры теперь создаются в трёх местах:
- **`service/view/workspace/claim/claim_list.js`** - кнопка "Договор упрощённой закупки" (для государственных заказчиков)
- **`service/view/workspace/purchase/details.js`** - кнопка "Договор упрощённой закупки" (для корпоративных заказчиков)
- **`service/view/workspace/purchase/list.js`** - кнопка "Договор упрощённой закупки" (для корпоративных заказчиков)

## Требования к бэкенду

Бэкенд должен:
1. **Принимать параметр `buyer_type_id`** в запросе `contract_create`
2. **Проверять лимиты БРВ** на основе типа заказчика:
   - `buyer_type_id == 1` → максимум 25 БРВ
   - `buyer_type_id != 1` → максимум 50 БРВ
3. **Возвращать ошибку** при превышении лимита:
   ```javascript
   {
     "error": {
       "code": "BRV_LIMIT_EXCEEDED",
       "message": "Вы не можете создать договор упрощенной закупки, так как общая сумма превышает 25 БРВ."
     }
   }
   ```
4. **Поддерживать оба типа источников**:
   - `source: [...]` - для государственных заказчиков (из заявок)
   - `object: { type: "new_purchase", id: "..." }` - для корпоративных заказчиков (из закупок)

## Совместимость

Изменения обратно совместимы:
- ✅ Существующие прямые договоры продолжают работать
- ✅ Добавлен новый параметр без нарушения существующего API
- ✅ Проверка на фронтенде дублирует проверку бэкенда для лучшего UX

## Тестирование

Для тестирования нужно:
1. **Создать заявки** с разными суммами
2. **Проверить лимиты** для государственных заказчиков (25 БРВ)
3. **Проверить лимиты** для корпоративных заказчиков (50 БРВ)
4. **Убедиться**, что показываются правильные сообщения об ошибках
