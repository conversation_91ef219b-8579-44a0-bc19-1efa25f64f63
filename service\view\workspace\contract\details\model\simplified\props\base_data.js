import { Language } from '@iac/core';
import { Config, Context } from '@iac/kernel';
import { validate } from '../utils';

export function getBaseDataProps(contractBody) {
  return {
    base_settings: {
      group: "!base/simplified_contract_gen_data",
      type: "setting",
      attr: {
        style: "max-width: 800px;"
      }
    },
    base_data: {
      type: "model",
      label: "!",
      group: "!base/simplified_contract_gen_data",
      readonly: !contractBody.contract.rights?.set_contract_data,
      fields: {
        contract_status: {
          label: '-status',
          type: "widget",
          widget: {
            name: {
              props: ['status', 'reason_cancellation', 'grounds_cancellation'],
              methods: {
                download(e) {
                  e.preventDefault();
                  let link = `${Config.api_server}/file/${this.grounds_cancellation.id}`
                  this.$wait(async () => {
                    if (Context.User.id) {
                      await Context.User.refreshToken();
                      window.location = `${link}?token=${Context.User.access_token}`
                    } else {
                      window.location = `${link}`
                    }
                  })
                }
              },
              template: `
                  <div>
                    <ui-ref source='simplified_contract_status' :value='status'/>
                    <div style='color: #777' v-if='grounds_cancellation'>{{$t("simplified_contr_reason")}}:
                      <a :href='grounds_cancellation.name' @click.prevent='download'>{{ grounds_cancellation.name }}</a>
                    </div>
                    <div style='color: #777' v-if='reason_cancellation'>Причина: {{reason_cancellation}}</div>
                  </div>
                `
            },
            props: {
              status: contractBody.contract.status,
              reason_cancellation: contractBody.reason_cancellation,
              grounds_cancellation: contractBody.grounds_cancellation
            }
          }
        },
        version_number: {
          label: '-contract.extra_number',
          type: "static",
          hidden: () => {
            return !contractBody.version_number
          }
        },
        ckepik_id: {
          type: "number",
          label: '-contract.ckepik_id',
          readonly: !contractBody.contract.rights?.set_contragent_data,
          required: false,
        },
        contract_name: {
          label: '-contract.contract_name',
          required: true,
          readonly: !contractBody.contract.rights?.set_contragent_data,
        },
        hide_in_public: {
          label: '-contract.hide_in_public',
          type: "bool",
          readonly: contractBody.additional_contract,
        },
        contract_close_at: {
          label: "-contract.contract_close_at",
          type: "date",
          required: true,
          validate: () => {
            let value = contractBody.base_data.contract_close_at;
            if (!value || !contractBody.base_data.execution_date) { return }

            return (
              new Date(contractBody.base_data.execution_date) < new Date(value)
                ? Language.t("contract.must_be_before_expiration")
                : null
            );
          }
        },
        execution_date: {
          label: "-contract.execution_date",
          type: "date",
          required: true,
          validate: () => {
            let value = contractBody.base_data.execution_date
            if (!value || !contractBody.base_data.contract_close_at) { return }

            return (
              new Date(value) < new Date(contractBody.base_data.contract_close_at)
                ? Language.t("contract.must_be_after_signing")
                : null
            );
          }
        },
        spec_totalcost_amount: {
          label: "-contract.contract_totalcost",
          type: "static",
          attr: {
            style: "margin-bottom: 0;"
          },
        },
        ///сумаа
        vat_nds: {
          type: "bool",
          label: "-contract.vat",
        },
        contract_type: {
          label: '-contract_type',
          type: "entity",
          readonly: !contractBody.contract.rights?.set_contragent_data,
          has_del: true,
          required: true,
          dataSource: "ref_contract_type",
        },
        contract_reason: {
          label: '-simplified_contr_reason',
          type: "entity",
          has_del: true,
          readonly: !contractBody.contract.rights?.set_contragent_data,
          required: true,
          dataSource: {
            limit: 99,
            store: {
              method: "ref",
              ref: "ref_contract_reasons",
              injectQuery: (params) => {
                params.filters = params.filters || {};
                params.filters.proc_type = "simplified_contract";
                return params;
              }
            }
          },
        },
        reason: {
          label: "-Загрузить файл сравнения цен",
          type: "file",
          multiple: true,
          required: false,
        },
        delivery_days: {
          label: "-contract.delivery_days",
          type: "integer",
          required: true,
          validate: validate,
        },
        payment_days: {
          label: "-contract.payment_days",
          type: "integer",
          required: true,
          validate: () => {
            let value = contractBody.base_data.advance_payment_days;
            if (!value || !contractBody.base_data.payment_days) { return }

            return (
              value > contractBody.base_data.payment_days
                ? Language.t("payment_term_not_less_than_preliminary")
                : null
            );
          },
        },
        advance_payment_days: {
          label: "-contract.advance_payment_days",
          type: "integer",
          required: false,
          validate: () => {
            let value = contractBody.base_data.advance_payment_days;
            if (!value || !contractBody.base_data.payment_days) { return }

            return (
              value > contractBody.base_data.payment_days
                ? Language.t("payment_term_not_exceed")
                : null
            );
          }
        },
        special_conditions: {
          group: "!base/!special_terms",
          label: "-special_terms",
          required: true,
          type: "text",
        },
      }
    }
  };
}
