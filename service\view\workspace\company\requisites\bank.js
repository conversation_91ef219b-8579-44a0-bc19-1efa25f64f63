import { Entity, DataSource, Query, RemoteStore, ArrayStore, RefStore } from '@iac/data'
import { Http } from '@iac/core';
import { Ref } from '@iac/kernel'
import { Context, <PERSON><PERSON><PERSON>, Settings } from '@iac/kernel'
import { Language } from '@iac/core'

let hidden_edit = () => {
    return !Context.Access.policy['company_edit'];
}

class SharingBackAccount extends Entity {
    constructor(context = {}) {
        super(context)
        let { model } = context;
        this.context = context
        this.id = model.id
        this.info = [
            { title: "bank_name", value: model.bank_name },
            { title: "bank_account", value: model.bank_account },
            { title: Language.t([Settings._country + ".bank_mfo_code", "bank_mfo_code"]), value: model.bank_mfo_code }
        ]
        this.currency = model.currency;
        this.is_main = model.meta?.is_main || false;
    }
    props() {
        return {
            info: {
                type: "layout-static",
                label: "!"
            },
            account: {
                type: "data-grid",
                label: "!",
                dataSource: {
                    store: {
                        method: "company_ref",
                        ref: "shared_accounts",
                        context: (context) => {
                            context.actions = [
                                {
                                    icon: "delete",
                                    btn_type: "danger",
                                    question: Language.t("question_delete_this_record"),
                                    handler: async () => {

                                        let { error, data } = await Http.api.rpc("company_ref", {
                                            "ref": "shared_accounts",
                                            "op": "delete",
                                            "filters": {
                                                "id": context.id,
                                            }
                                        })
                                        if (error)
                                            return Vue.Dialog.MessageBox.Error(error);


                                        this.context.model.share_count--;
                                        this.properties.account.dataSource.reload();
                                    }
                                }
                            ]
                            return context
                        },
                        injectQuery: (params) => {
                            params.filters.account_id = this.id
                            return params
                        }
                    },
                    actions: [
                        {
                            label: "add",
                            handler: async () => {
                                let company = await Vue.Dialog.MessageBox.Form({
                                    fields: [
                                        {
                                            name: "company",
                                            type: "entity",
                                            value: undefined,
                                            required: true,
                                            status: undefined,
                                            dataSource: new DataSource({
                                                template: "template-company",
                                                displayExp: "title",
                                                store: {
                                                    ref: "companies",
                                                    method: "company_ref",
                                                    injectQuery: (params) => {
                                                        params.op = "my_subs"
                                                        return params;
                                                    }
                                                }
                                            })
                                        }
                                    ],
                                    onClose: async (params) => {
                                        return await Http.api.rpc("company_ref", {
                                            "ref": "shared_accounts",
                                            "op": "share_account",
                                            "data": {
                                                "account_id": this.id,
                                                "company_id": params.company
                                            }
                                        })
                                    }
                                })

                                if (company && company != 2) {
                                    this.context.model.share_count++;
                                    this.properties.account.dataSource.reload();
                                }
                            },
                        },
                    ]
                },
                attr: {
                    //action_name: "Действия",
                    buttons: true,
                    columns: [
                        { label: "company", field: "company_fulltitle", style: "text-align: left; width: 100%" }
                    ]
                }
            }
        }
    }
}

class BackAccount extends Entity {
    constructor(context = {}, company = {}) {
        super(context)

        this.name = context.name;
        this.bank_name = context.bank_name;
        this.bank_account_type_id = company.external_company ? 2 : context.bank_account_type_id || 2;
        this.bank_account = context.bank_account;
        this.bank_mfo_code = context.bank_mfo_code;
        this.actions = context.actions || [];
        this.share_count = context.share_count || 0;
        this.is_linked = context.is_linked || false;
        this.bank_country = context.meta?.bank_country
        this.currency = context.currency;
        this.is_main = context.meta?.is_main || false;
        this.meta = context.meta;
        this.is_basic = company?.bank_requisites?.account == context.bank_account  //Основной банковский счет у нерезидента
        this.external_company = company.external_company;
        this.bank_requisites = company.bank_requisites;
    }
    props(company) {
        //09.04.2025 Andrey
        //для нерезидента просто текст
        const bank_mfo_code = Settings._country == 'KG' || this.external_company ? {
            required: true,
            type: 'string'
        } : {
            required: true,
            type: 'entity',
            dataSource: "ref_bank_mfo"
        }

        return {
            name: {
                label: "bank_account.name",
                required: true
            },
            bank_name: {
                required: true
            },
            bank_account_type_id: {
                type: 'entity',
                dataSource: "ref_bank_account_type",
                readonly: () => {
                    return this.external_company ? true : false
                }
            },
            bank_account: {
                required: true
            },
            bank_mfo_code: {
                ...bank_mfo_code,
                //09.04.2025 Andrey
                //Если нерезидент то поле "Банк МФО" переименовываем "SWIFT" 
                label: !this.external_company ? [Settings._country + ".bank_mfo_code", "bank_mfo_code"] : ["SWIFT"],
            },
            bank_country: {
                label: '~Страна банка',
                type: "entity",
                dataSource: "ref_country",
                required: true,
                hidden: () => {
                    return !this.external_company
                }
            },
            currency: {
                label: "Валюта счета",
                type: "entity",
                dataSource: {
                    store: {
                        key: 'id',
                        ref: 'ref_currency_full',
                    }
                },
                required: true,
            },
        }
    }
    async save() {
        let params = this.fields.filter((field) => {
            if (field.hidden && typeof field.hidden == 'function') {
                return !field.hidden();
            }
            return !field.hidden;
        }).map((field) => {
            let value = field.value;
            if (field.value && field.value.exp && field.value.exp.value != undefined) {
                value = field.value.exp.value
            }
            return {
                name: field.name,
                value: value
            }
        }).reduce((prev, curr) => {
            prev[curr.name] = curr.value
            return prev;
        }, {})


        let method = this.id ? "update_company_bank_account" : "create_company_bank_account";
        let { error, data } = await Http.api.rpc(method, {
            id: this.id,
            ...params
        })
        if (error) {
            if (!this.setError(error)) {
                await Vue.Dialog.MessageBox.Error(error);
            }
        } else if (!this.id) {
            this.id = data.id
        }
        return { error, data };
    }

    async delete() {

        if (await Vue.Dialog.MessageBox.Question(Language.t('question_delete_this_record')) != Vue.Dialog.MessageBox.Result.Yes) {
            return
        }
        let { error, data } = await Http.api.rpc('delete_company_bank_account', {
            id: this.id
        });
        if (error) {
            await Vue.Dialog.MessageBox.Error(error);
        } else {
            await Vue.Dialog.MessageBox.Success(data.message);
        }

        return {
            error, data
        }
    }
}


export default {
    props: ['company'],
    data: function () {
        return {
            dataSource: new DataSource({
                template: 'template-bank_account',
                store: new RemoteStore({
                    method: 'get_company_bank_accounts',
                    context: (context) => {
                        let model = new BackAccount(context, this.company);
                        model.actions = [
                            {
                                label: 'common_access',
                                handler: async () => {
                                    new Vue.Dialog({
                                        props: ["model"],
                                        data() {
                                            return {
                                                account: new SharingBackAccount({
                                                    model: model
                                                })
                                            }
                                        },
                                        template: `
                                        <div>
                                            <header>{{model.name}}</header>
                                            <main>
                                                <ui-layout :fields='account.fields'/>
                                            </main>
                                            <footer>
                                                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>    
                                            </footer>
                                        </div>
                                    `
                                    }).Modal({
                                        size: "right",
                                        model: model,
                                    })
                                }, hidden: () => {
                                    if (hidden_edit())
                                        return true;
                                    return context.is_linked
                                }
                            },
                            {
                                label: 'set_main',
                                question: Language.t("account_set_main_question"),
                                handler: async () => {
                                    let { error, data } = await Http.api.rpc('set_main_account', {
                                        id: model.id
                                    });

                                    if (error) {
                                        return Vue.Dialog.MessageBox.Error(error);
                                    }
                                    let mainIds = data || [];
                                    let items = this.dataSource.items || [];

                                    items.forEach(item => {
                                        item.is_main = mainIds.includes(item.id);
                                    })
                                },
                                hidden: () => {
                                    return model.is_main || !Context.Access.policy['company_edit'];
                                },

                            },
                            { type: "sep" },
                            {
                                label: 'delete',
                                icon: 'trash',
                                handler: async () => {
                                    if (await Vue.Dialog.MessageBox.Question(Language.t("account_delete_question"), "") == Vue.Dialog.MessageBox.Result.Yes) {
                                        const { error, data } = await Http.api.rpc("delete_company_bank_account", { id: context.id })
                                        if (error) {
                                            await Vue.Dialog.MessageBox.Error(error);
                                        } else {
                                            this.dataSource.reload()
                                        }
                                    }
                                }, hidden: () => {
                                    //10.04.2025 Andrey
                                    if(this.company.external_company && (this.company?.bank_requisites?.account == context.bank_account)) //если неризедент и основной счет
                                        return true
                                    if (hidden_edit())
                                        return true;
                                    if (context.meta && context.meta.gnk_status === 0) // активный счет - запрет на удаление
                                        return true;
                                    return context.is_linked 
                                }
                            },
                            {
                                label: 'edit',
                                icon: 'edit',
                                handler: async () => {
                                    await this.edit(new BackAccount(context, this.company));
                                },
                                hidden: () => {
                                    if(this.company.external_company && (this.company?.bank_requisites?.account == context.bank_account)) //если нерезидент и основной счет
                                        return true
                                    if (context.meta.gnk_status === 0) // активный счет - запрет
                                        return true;
                                    if (context.meta.gnk_status) // неактивный счёт - запрет
                                        return true;
                                    return !Context.Access.policy['company_edit'];
                                }
                            },
                        ]
                        /* model.actions = [{
                             label: 'edit',
                             hidden: hidden_edit,
                             handler: async () => {
                                 await this.edit(new BackAccount(context));
                             }
                         },{
                             label: 'delete',
                             hidden: hidden_edit,
                             handler: async () => {
                                 let reuslt = await model.delete();
                                 if(reuslt && !reuslt.error)
                                     this.dataSource.reload();
                             }
                         }]*/

                        return model;
                    },
                    inject: (items) => {
                        if (!items)
                            return;
                        let bank_mfo_codes = {};
                        let bank_country_codes = {};
                        items.forEach((item) => {
                            bank_mfo_codes[item.bank_mfo_code] = bank_mfo_codes[item.bank_mfo_code] || {
                                items: []
                            };
                            bank_mfo_codes[item.bank_mfo_code].items.push(item)

                            if(item.bank_country){
                                bank_country_codes[item.bank_country] = bank_country_codes[item.bank_country] || {
                                    items: []
                                };
                                bank_country_codes[item.bank_country].items.push(item)
                            }
                        })

                        DataSource.get("ref_bank_mfo").byKeys(Object.keys(bank_mfo_codes)).then((codes) => {
                            codes.forEach((code_item) => {
                                let items = bank_mfo_codes[code_item.code] && bank_mfo_codes[code_item.code].items;
                                if (items) {
                                    items.forEach((item) => {
                                        item.bank_mfo_code = `${code_item.code}: ${code_item.name}`;
                                    })
                                }
                            })
                        })
                        DataSource.get("ref_country").byKeys(Object.keys(bank_country_codes)).then((codes) => {
                            codes.forEach((country) => {
                                let items = bank_country_codes[country.code]?.items;
                                if (items) {
                                    items.forEach((item) => {
                                        item.bank_country = `${country.name}`;
                                    })
                                }
                            });
                        });

                        return items;
                    }
                })
            }),
            actions: [

            ]
        }
    },
    computed: {
        has_edit() {
            return !hidden_edit();
        }
    },
    methods: {
        async edit(model) {
            if (!model) {
                model = new BackAccount(undefined, this.company);
            }

            if (await Vue.Dialog({
                props: ['model'],
                methods: {
                    async save() {
                        let { error, data } = await model.save();
                        if (!error)
                            this.Close(true);
                    },
                    async deleteAccount() {
                        let result = await model.delete();
                        if (result && !result.error) {
                            this.Close(true);
                        }
                    }
                },
                template: `
                    <div>
                        <main>
                            <ui-layout :fields='model.fields'/>
                        </main>
                        <footer>
                            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                            <ui-btn type='primary' v-on:click.native='save'>{{$t('save')}}</ui-btn>
                            <ui-btn v-if='model.id' type='danger' v-on:click.native='deleteAccount'>{{$t('delete')}}</ui-btn>
                        </footer>
                    </div>
                `
            }).Modal({
                model: model
            })) {
                this.dataSource.reload();
            }
        },
        refreshBalance() {
            this.$wait(async () => {
                const { error, data } = await Http.api.rpc('force_accounts_update');
                if (error) {
                    Vue.Dialog.MessageBox.Error(error);
                } else {
                    const message = data?.message || Language.t('balance_refresh_success_default');

                    Vue.Dialog.MessageBox.Success({
                        message: message,
                    });
                }
            })
        },
    },
    template: `
        <div>
            <ui-layout-group label='company_bank' :actions='actions'> 
                <ui-data-view type='tile' :search='false' :toolbar='false' :dataSource='dataSource'/>
            </ui-layout-group>
            <p>
            <ui-btn type='primary' v-if='$settings.bank_account && $settings.bank_account._create && has_edit' v-on:click.native='edit()'>{{$t("create")}}</ui-btn>
            <ui-btn type='primary' v-if='$settings.bank_account && $settings.bank_account._create && has_edit'  v-on:click.native='refreshBalance'>{{$t("balance.refresh")}}</ui-btn>
        </div>
    `
}



