# Исправление загрузки файлов в упрощенных договорах

## 🐛 **Проблема:**
Файлы не загружались на сервер перед сохранением договора. В логах было видно:
```javascript
"reason": [
    {
        "file": {},           // ❌ Пустой объект файла
        "name": "6145880984.jpg"
    }
]
```

Вместо ожидаемого:
```javascript
"reason": [
    {
        "id": "uuid",         // ✅ ID файла на сервере
        "name": "file.jpg",   // ✅ Имя файла
        "meta": {...}         // ✅ Метаданные файла
    }
]
```

## 🔍 **Причина:**
Метод `build_action_params()` не обрабатывал файлы перед отправкой на сервер. Файлы оставались в исходном состоянии `{file: File, name: string}` вместо загрузки на файловый сервер.

## 🔧 **Исправление:**

### **1. Модифицировали `build_action_params()` для асинхронной обработки файлов:**

```javascript
async build_action_params(fields) {
  // Функция для загрузки файла на сервер
  const uploadFile = async (file) => {
    let formData = new FormData();
    formData.append('data', file, file.name);
    let { data, error } = await Http.upload.form('tender/attach', formData);
    
    if (error) throw error;

    return {
      id: data.uuid,
      name: data.meta.name,
      meta: {
        "type": data.meta.type,
        "content_type": data.meta.content_type,
        "type_group": data.meta.group,
        "size": data.meta.size
      }
    };
  };

  // Обработка каждого поля
  for (const field of fields) {
    // ... фильтрация полей ...

    // Обработка файлов
    if (field.type === 'file' && value) {
      if (Array.isArray(value)) {
        // Множественные файлы
        const processedFiles = [];
        for (const item of value) {
          if (item && item.file && item.file.name) {
            // Файл нужно загрузить
            const uploadedFile = await uploadFile(item.file);
            processedFiles.push(uploadedFile);
          } else if (item && item.id) {
            // Файл уже загружен
            processedFiles.push(item);
          }
        }
        value = processedFiles;
      } else {
        // Одиночный файл
        if (value && value.file && value.file.name) {
          value = await uploadFile(value.file);
        }
      }
    }
  }

  return params;
}
```

### **2. Обновили вызовы метода для поддержки async/await:**

```javascript
// Было:
params: this.build_action_params(this.properties.base_data.fields),

// Стало:
params: await this.build_action_params(this.properties.base_data.fields),
```

### **3. Добавили подробное логирование:**

```javascript
console.log('📁 Обработка поля файла "reason":', value);
console.log('📤 Загрузка файла на сервер:', {fileName, fileSize, fileType});
console.log('✅ Файл успешно загружен:', {id, name, meta});
console.log('💾 Обработанное поле файла "reason":', value);
console.log('📤 Параметры для отправки на сервер:', params);
```

## ✅ **Результат:**

### **Теперь файлы корректно обрабатываются:**

1. **При выборе файла** → файл временно сохраняется в поле как `{file: File, name: string}`
2. **При сохранении договора** → `build_action_params()` автоматически загружает файлы на сервер
3. **Файлы загружаются** → `Http.upload.form('tender/attach')` → получаем `{id, name, meta}`
4. **Данные файлов отправляются** → сервер получает ссылки на файлы вместо самих файлов
5. **Файлы сохраняются** → связываются с договором в базе данных

### **Поддерживается:**
- ✅ **Одиночные файлы** (`multiple: false`)
- ✅ **Множественные файлы** (`multiple: true`)
- ✅ **Уже загруженные файлы** (не загружаются повторно)
- ✅ **Новые файлы** (автоматически загружаются)
- ✅ **Обработка ошибок** загрузки

## 🧪 **Для тестирования:**

1. **Откройте упрощенный договор**
2. **Перейдите в "Основные данные"**
3. **Загрузите файл(ы) в поле "Загрузить файл сравнения цен"**
4. **Откройте консоль браузера**
5. **Нажмите "Сохранить"**
6. **Проверьте логи** - должны появиться сообщения:
   ```
   📁 Обработка поля файла "reason": [...]
   📤 Загрузка файла на сервер: {...}
   ✅ Файл успешно загружен: {id: "uuid", ...}
   💾 Обработанное поле файла "reason": [{id: "uuid", ...}]
   📤 Параметры для отправки на сервер: {reason: [{id: "uuid", ...}]}
   ```

## 🎉 **Проблема решена!**

Теперь файлы корректно загружаются на файловый сервер и их данные сохраняются вместе с договором.
