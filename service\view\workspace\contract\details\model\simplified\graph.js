import { Entity } from '@iac/data';
import { Settings } from '@iac/kernel';
import { validate, validate_advance } from './utils';

export default class SimplifiedContractGraph extends Entity {
  constructor(context, contract) {
    super(context);

    this.source = context.source;
    this.summa = context.summa;
    this.advance_payment = context.advance_payment || 0;
    this.currency = context.currency || Settings._default_currency;
  }

  props() {
    return {
      source: {
        label: "comm_src",
        required: true,
      },
      summa: {
        group: "<summa>",
        type: "float",
        required: true,
        attr: { react: true },
        validate: validate
      },
      currency: {
        group: "<summa>",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true,
        required: true,
      },
      advance_payment: {
        type: "float",
        label: "advance_payment_amount",
        required: false,
        attr: { react: true },
        validate: validate_advance
      },
    }
  }
}
