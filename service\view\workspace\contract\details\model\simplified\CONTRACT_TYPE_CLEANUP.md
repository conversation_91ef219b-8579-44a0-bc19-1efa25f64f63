# Удаление логики типов контракта из упрощенных договоров

## 🎯 **Цель:**
Удалить всю логику, основанную на типах контракта (`contract_type`), которая попала из прямых договоров в упрощенные, но не нужна для упрощенных договоров.

## 🔍 **Проблема:**
В упрощенных договорах не должно быть поля "тип контракта", но логика из прямых договоров включала проверки на `contract_type`, что создавало ненужную сложность.

## 🗑️ **Что было удалено:**

### **1. Поле `contract_type` из `base_data.js`:**
```javascript
// ❌ УДАЛЕНО:
contract_type: {
  type: "hidden",
  value: 2, // Для упрощенных договоров всегда тип 2 (корпоративный)
},
```

### **2. Инициализация `contract_type` из `simplified_body.js`:**
```javascript
// ❌ УДАЛЕНО:
contract_type: context.contract_type || 2, // По умолчанию тип 2 для упрощенных договоров
```

### **3. Условия скрытия в `participants.js`:**

#### **Поле `contragent`:**
```javascript
// ❌ УДАЛЕНО:
hidden: () => {
  return !contractBody.base_data.contract_type
},

// ✅ СТАЛО: поле всегда видимо
```

#### **Поле `custom_company`:**
```javascript
// ❌ БЫЛО:
hidden: () => {
  return contractBody.readonly || !contractBody.base_data.contract_type
},

// ✅ СТАЛО:
hidden: () => {
  return contractBody.readonly
},
```

#### **Поле `company` (выбор из списка):**
```javascript
// ❌ БЫЛО:
hidden: () => {
  return (
    !contractBody.base_data.contract_type
    || contractBody.participants.contragent?.custom_company
  )
},

// ✅ СТАЛО:
hidden: () => {
  return contractBody.participants.contragent?.custom_company
},
```

#### **Поле `custom` (ручной ввод):**
```javascript
// ❌ БЫЛО:
hidden: () => {
  return (
    !contractBody.base_data.contract_type
    || !contractBody.participants.contragent?.custom_company
  )
},

// ✅ СТАЛО:
hidden: () => {
  return !contractBody.participants.contragent?.custom_company
},
```

### **4. Отладочная информация:**
```javascript
// ❌ УДАЛЕНО:
console.log('🔍 Отладка participants.js:', {
  'contractBody.base_data.contract_type': contractBody.base_data?.contract_type,
  // ...
});
```

## ✅ **Результат:**

### **Упрощенная логика:**
- ✅ **Нет поля "тип контракта"** - не нужно для упрощенных договоров
- ✅ **Нет проверок на `contract_type`** - логика стала проще
- ✅ **Блок "Участники" всегда видим** - не зависит от типа контракта
- ✅ **Переключатель "Добавить компанию вручную" работает** без лишних условий

### **Логика работы участников:**

1. **По умолчанию** показывается поле выбора компании из списка
2. **Переключатель** "Добавить компанию вручную" всегда доступен (кроме readonly режима)
3. **При включении переключателя:**
   - Скрывается поле выбора из списка
   - Показываются поля ручного ввода
4. **При выключении переключателя:**
   - Показывается поле выбора из списка
   - Скрываются поля ручного ввода

### **Преимущества:**
- ✅ **Простота** - нет лишней логики типов контракта
- ✅ **Понятность** - логика основана только на реальных потребностях
- ✅ **Соответствие концепции** - упрощенные договоры действительно упрощены
- ✅ **Меньше условий** - код стал чище и понятнее

## 🧪 **Для тестирования:**

1. **Откройте упрощенный договор**
2. **Убедитесь, что блок "Участники" всегда видим**
3. **Проверьте переключатель "Добавить компанию вручную":**
   - По умолчанию выключен → показывается выбор из списка
   - При включении → показываются поля ручного ввода
   - При выключении → снова показывается выбор из списка
4. **Заполните любые поля в "Основных данных"**
5. **Убедитесь, что блок "Участники" остается видимым**

## 🎯 **Техническая суть изменений:**

**Было (сложно):**
```javascript
// Поле contract_type определено, но не используется пользователем
contract_type: { type: "hidden", value: 2 }

// Множество проверок на contract_type
hidden: () => !contractBody.base_data.contract_type
hidden: () => contractBody.readonly || !contractBody.base_data.contract_type
hidden: () => !contractBody.base_data.contract_type || condition
```

**Стало (просто):**
```javascript
// Поле contract_type удалено полностью

// Простые условия без лишних проверок
// (поле всегда видимо)
hidden: () => contractBody.readonly
hidden: () => condition
```

## 🎉 **Упрощенные договоры стали действительно упрощенными!**

Теперь логика основана только на реальных потребностях пользователей, без лишних абстракций из прямых договоров.
