import { Config } from '@iac/kernel'

const Component = {
  props: ["model"],
  methods: {
    getFileUrl(id) {
      return `${Config.api_server}/file/${id}`              
    }
  },
  template: `
        <ui-data-view-item :model='model'>            
            <template slot='header'>
                <div>
                  №{{model.id}}
                </div>
                <div>
                  <span><iac-date :date='model.date_begin' withoutTime /></span>  <span><iac-date :date='model.date_end' withoutTime /></span>
                </div>
            </template>
            
            <template slot='title'>
                <div>{{model.name}}</div>
            </template>

            <template slot='description'>
            <div>
                <label style="text-transform: capitalize">{{$t('product')}}:</label>
                <span>{{model.product_name}}</span>
            </div>
            <div>
              <label>{{$t('company')}}:</label>
              <span> {{model.meta.company_title}} </span>
            </div>
           
             <div v-if='model.comment'>
              <label>{{$t('comment')}}:</label>
              <span>{{model.comment}}</span>
              </div>
            </template>
            <template slot='props'>
            <div>
              <label>{{$t('file')}}:</label>
              <div>
                  <div v-for="(file,index) in model.files">
                      <a :href='getFileUrl(file.id)'>{{ file.name }}</a>
                  </div>
              </div>
            </div>
            </template>
        
        </ui-data-view-item>
    `
}

Vue.component("template-issuing_schedule_new", Component)
