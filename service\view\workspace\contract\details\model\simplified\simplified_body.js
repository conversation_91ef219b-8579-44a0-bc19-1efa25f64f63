import { Entity, DataSource } from '@iac/data';
import { Http, Language, Util } from '@iac/core';
import { Config, Context, Settings } from '@iac/kernel';

import SimplifiedContractProduct from './product';
import SimplifiedContractViewProduct from './view_product';
import SimplifiedContractGraph from './graph';
import SimplifiedContractGraphFinance from './graph_finance';
import SimplifiedContractCompetitiveList from './competitive_list';
import SimplifiedContractBodyProperty from './property';
import { SearchInput, validate, validate_advance } from './utils';
import { getAllProps } from './props';

export default class SimplifiedContractBody extends Entity {

  constructor(context, contract) {
    super(context);

    this.context = context;
    this.contract = contract;
    this.wait = false;

    // Определяем тип заказчика (государственный или корпоративный)
    if (this.context.position_src) {
      this.is_gos = this.context.position_src == 'claim'
    } else {
      this.is_gos = this.contract.graphic?.filter((item) => {
        return item.details || item.base
      }).length > 0;
    }

    // Тип процедуры - упрощённая закупка
    this.proc_type = 'simplified_contract';

    this.claim_data = context.claim_data || [];

    this.claim_accounts = this.claim_data.map((item) => { return { id: item.kls, name: item.kls }; });

    this.claim_data = this.claim_data.reduce((acc, item) => {
      acc[item.id] = {
        account: item.kls,
        expense_item_code: item.expense
      };

      return acc;
    }, {});

    this.currency = context.currency || Settings._default_currency;

    this.base_data = {
      ckepik_id: context.ckepik_id,
      version_number: context.version_number,
      contract_name: context.contract_name,
      hide_in_public: context.hide_in_public,
      contract_close_at: context.contract_close_at,
      execution_date: context.execution_date,
      spec_totalcost_amount: `${Util.Number(context.spec_totalcost_amount, ' ', 2)} ${this.currency}`,
      vat_nds: context.vat_nds,
      contract_type: context.contract_type,
      contract_reason: context.contract_reason,
      reason: context.reason,
      addons: context.addons,
      footing: context.footing,
      beneficiary: context.beneficiary,
      delivery_days: context.delivery_days,
      payment_days: context.payment_days,
      advance_payment_days: context.advance_payment_days,
      special_conditions: context.special_conditions,
    }

    this.reason_cancellation = context.reason_cancellation;
    this.grounds_cancellation = context.grounds_cancellation;

    this.additional_contract = context.additional_contract;

    context.contragent = context.contragent || {};

    if (context.contragent.company_details) {
      let source = context.contragent.company_details.source || "company";

      context.contragent[source] = { ...context.contragent.company_details, source: source }
      if (context.contragent.custom) {
        context.contragent.custom_company = true
      }
    }

    this.participants = {
      initiator: contract.initiator,
      contragent: context.contragent,
    }

    this.requisites = {
      org_banking_details: this.participants.initiator.banking_details,
      part_banking_details: this.participants.contragent.banking_details,
    };

    this._graph = context.graph || []
    this._graph_finance = context.graph_finance || []
    this._competitive_list = context.competitive_list || []
    this._spec = context.spec || []
  }

  get propertyModel() {
    return SimplifiedContractBodyProperty;
  }

  async edit_product(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new SimplifiedContractProduct({ is_gos: this.is_gos, proc_type: this.proc_type, ...item })).fields
    });
  }

  async edit_graph(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new SimplifiedContractGraph({ currency: this.currency, ...item })).fields
    })
  }

  async edit_graph_finance(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new SimplifiedContractGraphFinance({ currency: this.currency, is_gos: this.is_gos, proc_type: this.proc_type, claim_accounts: this.claim_accounts, ...item })).fields
    })
  }

  async edit_competitive_list(item = {}) {
    return Vue.Dialog.MessageBox.Form({
      fields: (new SimplifiedContractCompetitiveList({ currency: this.currency, ...item })).fields
    })
  }

  validate(show_error = true) {
    // Вызываем базовую валидацию
    super.validate(show_error);

    // Дополнительная валидация конкурентного листа
    if (this.contract.rights?.set_graph_finance_data) {
      let items = this.properties.competitive_list.dataSource.items || [];
      let valid_items = items.filter((item) => {
        return !((item.status & 4) != 0)
      });

      // if (valid_items.length < 3) {
      //   if (show_error) {
      //     Vue.Dialog.MessageBox.Error("Для публикации договора необходимо добавить не менее трех поставщиков в конкурентный лист");
      //   }
      //   return false;
      // }
    }

    return true;
  }

  validate_fields(fields) {
    for (let field of fields) {
      if (field.hidden && typeof field.hidden != "function") {
        continue;
      }

      if (field.hidden && field.hidden()) {
        continue;
      }

      if (field.status) {
        Vue.Dialog.MessageBox.Error(Language.t("error_check_data_validity"));

        return false;
      }

      if (field.type == 'model' && field.fields && !this.validate_fields(field.fields)) {
        return false;
      }
    }

    return true;
  }

  build_action_params(fields) {
    return fields.filter((field) => {
      if (field.type == 'static') return false;

      if (field.hidden && typeof field.hidden == 'function') return !field.hidden();

      return !field.hidden;
    }).map((field) => {
      let value = field.value;

      if (value && value.exp && value.exp.value != undefined) value = value.exp.value

      if (Array.isArray(value) && value.length <= 0) value = undefined;

      if (value && !Array.isArray(value) && typeof value == 'object') value = { ...value }

      return { name: field.name, value: value }
    }).filter((field) => {
      return field.value != undefined
    }).reduce((acc, field) => {
      acc[field.name] = field.value;

      return acc;
    }, {});
  }

  async update_graph() {
    let { error, data } = await this.contract.action('get_graph', {});

    if (!error && data) {
      this.properties.graph.dataSource.set_items(data);
    }
  }

  async save(handle_actions = null, notify = true) {
    if (handle_actions != null && typeof (handle_actions) != "object") {
      handle_actions = [handle_actions];
    }

    this.validate();

    let actions = [];

    if (this.contract.rights?.set_contract_data && (!handle_actions || handle_actions.indexOf("base_data") != -1)) {
      if (!this.validate_fields(this.properties.base_data.fields)) { return null; }

      actions.push({
        name: 'set_contract_data',
        params: this.build_action_params(this.properties.base_data.fields),
      });
    }

    if (this.contract.rights.set_contragent_data && (!handle_actions || handle_actions.indexOf("participants") != -1)) {
      if (!this.validate_fields(this.properties.participants.fields)) { return null; }

      actions.push({
        name: 'set_contragent_data',
        params: this.build_action_params([this.properties.participants.properties.contragent]),
      });
    }

    if (this.contract.rights.set_org_requisites && (!handle_actions || handle_actions.indexOf("org_requisites") != -1)) {
      if (!this.validate_fields([this.properties.requisites.properties.org_banking_details])) { return null; }

      actions.push({
        name: 'set_org_requisites',
        params: { banking_details_id: this.requisites.org_banking_details.id },
      });
    }

    if (this.contract.rights.set_part_requisites && (!handle_actions || handle_actions.indexOf("part_requisites") != -1)) {
      if (!this.validate_fields([this.properties.requisites.properties.part_banking_details])) { return null; }

      actions.push({
        name: 'set_part_requisites',
        params: { banking_details_id: this.requisites.part_banking_details.id },
      });
    }

    if (this.contract.rights.set_graph_data && (!handle_actions || handle_actions.indexOf("graph") != -1)) {
      let items = this.properties.graph.dataSource.items || [];

      items = items.filter((item) => {
        return !((item.status & 4) != 0)
      }).map((item) => {
        return {
          source: item.source,
          summa: item.summa,
          advance_payment: item.advance_payment || 0,
          currency: item.currency,
        }
      })

      let currency = items.reduce((prev, item) => {
        prev[item.currency] = prev[item.currency] || 0
        prev[item.currency] += item.summa
        return prev
      }, {});

      var graph_totalcost = Object.keys(currency).map(function (key) {
        return { currency: key, amount: currency[key] };
      });

      actions.push({
        name: 'set_graph_data',
        params: { graph: items, graph_totalcost: graph_totalcost }
      });
    }

    if (this.contract.rights.set_graph_finance_data && (!handle_actions || handle_actions.indexOf("graph_finance") != -1)) {
      let items = this.properties.graph_finance.dataSource.items || [];

      items = items.filter((item) => {
        return !((item.status & 4) != 0)
      }).map((item) => {
        return {
          date: item.date,
          summa: item.summa,
          advance_payment: item.advance_payment || 0,
          currency: item.currency,
          kls: item.kls,
          expense_item_code: item.expense_item_code,
        }
      })

      let currency = items.reduce((prev, item) => {
        prev[item.currency] = prev[item.currency] || 0
        prev[item.currency] += item.summa
        return prev
      }, {});

      var graph_finance_totalcost = Object.keys(currency).map(function (key) {
        return { currency: key, amount: currency[key] };
      });

      actions.push({
        name: 'set_graph_finance_data',
        params: { graph_finance: items, graph_finance_totalcost: graph_finance_totalcost }
      });
    }

    if (this.contract.rights.set_graph_finance_data && (!handle_actions || handle_actions.indexOf("competitive_list") != -1)) {
      let items = this.properties.competitive_list.dataSource.items || [];

      // Проверяем минимальное количество поставщиков (не менее 3)
      let valid_items = items.filter((item) => {
        return !((item.status & 4) != 0)
      });

      if (valid_items.length < 3) {
        await Vue.Dialog.MessageBox.Error("Необходимо добавить не менее трех поставщиков в конкурентный лист");
        return null;
      }

      items = valid_items.map((item) => {
        return {
          supplier_name: item.supplier_name,
          supplier_inn: item.supplier_inn,
          price: item.price,
          currency: item.currency,
          exchange_rate: item.exchange_rate,
          price_uzs: item.price_uzs
        }
      })

      actions.push({
        name: 'set_competitive_list_data',
        params: { competitive_list: items }
      });
    }

    if (this.contract.rights.set_specification && (!handle_actions || handle_actions.indexOf("spec") != -1)) {
      let items = this.properties.spec.dataSource.items || [];

      let invalid_items = items.filter((item) => {
        return (
          !((item.status & 4) != 0)
          && (
            (this.is_gos && !item.price)
            || (this.is_gos && !item.delivery_month)
            || (this.is_gos && !item.delivery_year)
            || !item.country
            || !item.conditions
            || (!this.is_gos && !item.delivery_address)
          )
        )
      });

      if (invalid_items.length > 0) {
        invalid_items.forEach((items) => { items.status |= 8; });
        await Vue.Dialog.MessageBox.Error(Language.t("error_check_data_validity"));
        return null;
      }

      items = items.filter((item) => {
        return !((item.status & 4) != 0)
      }).map((item) => {
        return {
          id: item.id,
          product: item.product,
          account: item.account,
          expense_item_code: item.expense_item_code,
          currency: item.currency,
          amount: item.amount,
          unit: item.unit,
          price: item.price,
          conditions: item.conditions,
          delivery_address: item.delivery_address,
          country: item.country,
          delivery_month: item.delivery_month,
          delivery_year: item.delivery_year,
        }
      })

      let currency = items.reduce((prev, item) => {
        prev[item.currency] = prev[item.currency] || 0
        prev[item.currency] += item.amount * item.price
        return prev
      }, {});

      var spec_totalcost = Object.keys(currency).map(function (key) {
        return { currency: key, amount: currency[key] };
      });

      actions.push({
        name: 'set_specification',
        params: { spec: items, spec_totalcost: spec_totalcost }
      });
    }

    this.wait = true;

    let data = {}
    for (let action of actions) {
      let params = action.params

      let { error, data } = await this.contract.action(action.name, { data: params });

      if (error && error.code != 'AbortError') {
        await Vue.Dialog.MessageBox.Error(error);

        this.wait = false;

        return null;
      }

      data = { ...data, ...params };

      if (params.graph) {
        this._graph = params.graph.map((item) => {
          return { ...item, status: 0 }
        });
        this.properties.graph.dataSource.set_items(this._graph);
      }

      if (params.graph_finance && this.is_gos) {
        this.update_graph();
      }

      if (params.graph_finance) {
        this._graph_finance = params.graph_finance.map((item) => {
          return { ...item, status: 0 }
        });
        this.properties.graph_finance.dataSource.set_items(this._graph_finance);
      }

      if (params.competitive_list) {
        this._competitive_list = params.competitive_list.map((item) => {
          return { ...item, status: 0 }
        });
        this.properties.competitive_list.dataSource.set_items(this._competitive_list);
      }

      if (params.spec) {
        await this.contract.update_context();

        this._spec = params.spec.map((item) => {
          return { ...item, status: 0 }
        });
        this.properties.spec.dataSource.set_items(this._spec);
      }
    }

    this.wait = false;

    if (notify) {
      await Vue.Dialog.MessageBox.Success(Language.t("data_updated"));
    }

    return data;
  }

  async onChangeProperty(field) {

    if (!field || field.property.type != 'file')
      return;

    var send = async (file) => {
      let formData = new FormData();
      formData.append('data', file, file.name);
      let { data, error } = await Http.upload.form('tender/attach', formData);
      if (error) {
        return;
      }

      return {
        id: data.uuid,
        name: data.meta.name,
        meta: {
          "type": data.meta.type,
          "content_type": data.meta.content_type,
          "type_group": data.meta.group,
          "size": data.meta.size
        }
      }
    }


    let value = field.value;
    this.properties.setting_general.wait = true;
    if (Array.isArray(value)) {
      for (let key in value) {
        let item = value[key];
        if (item && item.file && item.file.name) {
          let _value = await send(item.file);
          value[key] = _value;
        }
      }
      field.property._value = [...value]

    } else {
      let item = value;
      if (item && item.file && item.file.name) {
        let _value = await send(item.file);
        value = field.property._value = _value

      }
    }
    this.properties.setting_general.wait = false;
  }

  props() {
    return getAllProps(this);
  }
}
