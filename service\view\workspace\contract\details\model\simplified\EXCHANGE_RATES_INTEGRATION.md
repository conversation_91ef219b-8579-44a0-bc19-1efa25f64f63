# Интеграция курсов валют в конкурентный лист

## Описание

Добавлена интеграция с API курсов валют `ref_exchange_rate` для автоматического получения актуальных курсов валют при заполнении конкурентного листа.

## API курсов валют

### Запрос
```json
{
  "method": "rpc",
  "params": {
    "ref": "ref_exchange_rate",
    "op": "read", 
    "limit": 1
  }
}
```

### Ответ
```json
{
  "data": [
    {
      "id": 3421,
      "rates": {
        "EUR": 14820.11,
        "RUB": 163.34,
        "USD": 12758.36
      },
      "sync_time": "2025-07-17T02:00:00.000000Z",
      "inserted_at": "2025-07-17T02:00:00.253849Z"
    }
  ]
}
```

## Реализация в коде

### Импорты
```javascript
import { Entity } from '@iac/data';
import { Http } from '@iac/core';
import { Settings } from '@iac/kernel';
```

### Кэширование курсов
```javascript
export default class SimplifiedContractCompetitiveList extends Entity {
  // Кэш курсов валют
  static _exchangeRatesCache = null;
  static _cacheTimestamp = null;
  static _cacheTimeout = 5 * 60 * 1000; // 5 минут
```

### Метод получения курса валюты
```javascript
async getExchangeRate(currency) {
  // Проверяем кэш
  const now = Date.now();
  if (SimplifiedContractCompetitiveList._exchangeRatesCache && 
      SimplifiedContractCompetitiveList._cacheTimestamp &&
      (now - SimplifiedContractCompetitiveList._cacheTimestamp) < SimplifiedContractCompetitiveList._cacheTimeout) {
    
    const cachedRate = SimplifiedContractCompetitiveList._exchangeRatesCache[currency];
    if (cachedRate) {
      return cachedRate;
    }
  }

  try {
    // Запрос к API курсов валют
    const { error, data } = await Http.api.rpc("ref", {
      ref: "ref_exchange_rate",
      op: "read",
      limit: 1
    });

    if (!error && data && data.length > 0) {
      const exchangeData = data[0];
      const rates = exchangeData.rates || {};
      
      // Сохраняем в кэш
      SimplifiedContractCompetitiveList._exchangeRatesCache = rates;
      SimplifiedContractCompetitiveList._cacheTimestamp = now;
      
      // Возвращаем курс для указанной валюты
      if (rates[currency]) {
        return rates[currency];
      }
    }
  } catch (err) {
    console.warn('Ошибка получения курса валют:', err);
  }

  // Fallback: фиксированные курсы, если API недоступен
  const fallbackRates = {
    'USD': 12758.36,
    'EUR': 14820.11,
    'RUB': 163.34
  };
  
  return fallbackRates[currency] || 1;
}
```

## Дополнительные методы

### Принудительное обновление курсов
```javascript
static async refreshExchangeRates() {
  try {
    const { error, data } = await Http.api.rpc("ref", {
      ref: "ref_exchange_rate",
      op: "read",
      limit: 1
    });

    if (!error && data && data.length > 0) {
      const exchangeData = data[0];
      const rates = exchangeData.rates || {};
      
      // Обновляем кэш
      SimplifiedContractCompetitiveList._exchangeRatesCache = rates;
      SimplifiedContractCompetitiveList._cacheTimestamp = Date.now();
      
      return rates;
    }
  } catch (err) {
    console.warn('Ошибка обновления курсов валют:', err);
  }
  
  return null;
}
```

### Получение кэшированных курсов
```javascript
static getExchangeRatesCache() {
  return SimplifiedContractCompetitiveList._exchangeRatesCache;
}
```

## Как работает

### 1. При выборе валюты
- Автоматически вызывается `getExchangeRate(currency)`
- Проверяется кэш (действителен 5 минут)
- Если кэш устарел, делается запрос к API
- Полученные курсы сохраняются в кэш
- Возвращается курс для выбранной валюты

### 2. Кэширование
- **Время жизни кэша**: 5 минут
- **Хранение**: статические свойства класса
- **Обновление**: автоматическое при истечении времени

### 3. Fallback курсы
Если API недоступен, используются фиксированные курсы:
- **USD**: 12,758.36 UZS
- **EUR**: 14,820.11 UZS  
- **RUB**: 163.34 UZS

## Пример использования

### В форме добавления поставщика:
1. Пользователь выбирает валюту "USD"
2. Автоматически запрашивается курс USD
3. Поле "Курс пересчета" заполняется значением 12,758.36
4. При вводе цены автоматически рассчитывается цена в UZS

### Программное использование:
```javascript
// Получить курс конкретной валюты
const usdRate = await competitiveListItem.getExchangeRate('USD');

// Принудительно обновить все курсы
const rates = await SimplifiedContractCompetitiveList.refreshExchangeRates();

// Получить кэшированные курсы
const cachedRates = SimplifiedContractCompetitiveList.getExchangeRatesCache();
```

## Преимущества

### ✅ Актуальные курсы
- Курсы получаются из центральной системы
- Автоматическое обновление каждые 5 минут
- Синхронизация с официальными курсами

### ✅ Производительность
- Кэширование запросов на 5 минут
- Минимальная нагрузка на API
- Быстрый отклик интерфейса

### ✅ Надежность
- Fallback курсы при недоступности API
- Обработка ошибок сети
- Логирование проблем

### ✅ Простота использования
- Автоматическое получение курсов
- Прозрачная интеграция в форму
- Не требует дополнительных действий от пользователя

## Мониторинг

### Логи в консоли
При проблемах с API в консоли браузера появятся предупреждения:
```
Ошибка получения курса валют: [детали ошибки]
Ошибка обновления курсов валют: [детали ошибки]
```

### Проверка работы
1. Откройте форму добавления поставщика
2. Выберите валюту USD/EUR/RUB
3. Проверьте, что курс автоматически заполнился
4. В консоли не должно быть ошибок

## Совместимость

- ✅ Работает с существующим API `ref_exchange_rate`
- ✅ Совместимо с fallback курсами
- ✅ Не влияет на другие части системы
- ✅ Обратная совместимость с фиксированными курсами
