import { DataSource, RefStore } from '@iac/data'
import { Http, Language } from '@iac/core'
import { Context, Config } from '@iac/kernel'
import "@iac/service/templates/company"

const $t = Language.t;

const reject_dialog = Vue.Dialog({
    data() { return { comment: null } },
    template: `
        <div>
            <header>{{$t('reject_reason')}}</header>
            <main>
                <ui-text label='comment' v-model='comment'></ui-text>
            </main>
            <footer>
                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t("close")}}</ui-btn>
                <ui-btn type='primary' v-on:click.native='Close(comment)'>{{$t('accepted')}}</ui-btn>
            </footer>
        </div>
    `
})

const props_gen = (ctx) => (label, fetch_field_func, text_func) => {
    let r = null;

    if (typeof fetch_field_func === 'function') r = fetch_field_func(ctx)
    else r = fetch_field_func

    let text = null;
    text_func = text_func || (id => id)

    if (r) {
        text = text_func(r)
    } else {
        return null
    }

    return {
        label: $t(label),
        text: $t(text)
    }
}

export default {
    props: {
        access: {
            default: 'moderate'
        }
    },
    data() {
        return {
            system_access: Context.Access,
            error: undefined,
            type_source: undefined,
            permissiveDocumentsDataSource: undefined
        }
    },
    watch: {
        access: {
            immediate: true,
            async handler(value, oldValue) {
                
                this.$wait(async ()=>{
                    this.error = undefined;
                    let {error,data} = await Http.api.rpc("company_ref",{
                        ref: 'permissive_documents',
                        op: 'pd_types'
                    })
                    if(error)
                        this.error = error;
                    else if(data){
                        this.type_source = new DataSource({
                            ignore_listeners: true,
                            store: {
                                data: data
                            }
                        })
                        this.type_source.load();
                    }
                    this.permissiveDocumentsDataSource = this.createDataSource()
                })

            }
        }
    },
    methods: {
        createDataSource() {
            return new DataSource({
                store: new RefStore({
                    method: "company_ref",
                    ref: "permissive_documents",
                    inject: async items => {
                        if (!items) return;
                        if (this.access === 'private') return items;

                        const uniq_ids = [...new Set(items.map(it => it.company_id))];

                        let { error, data } = await Http.api.rpc("company_ref", {
                            ref: "companies",
                            op: "read",
                            filters: { id: uniq_ids },
                            limit: 51
                        });

                        if (!data) return items;

                        const kv = data.reduce((acc, it) => {
                            acc[it.id] = it;
                            return acc;
                        }, {});

                        return items.map(it => {
                            it['footer'] = [{
                                computed: {
                                    model() {
                                        const inn = kv[it.company_id]["inn"]
                                        return { ...kv[it.company_id], title_text: `${$t('company')}: ${inn}` };
                                    }
                                },
                                template: `
                                    <template-company :model='model' />
                                `
                            }];

                            return it;
                        });
                    },
                    context: context => {
                        const prop = props_gen(context);
                        return {
                            id: context?.id,
                            // TODO: Решить что делать с этим кастылём.
                            // Нужно добавить текст комментария и company_id, может быть ещё и user_id
                            header: [
                                // context?.meta?.document_name || context?.type == "pd_broker" && Language.t("request_for_becoming_exchange_company"), 
                                Language.t([`${context.type}.document_name`,context.type]),
                                {
                                component: 'ui-ref',
                                props: {
                                    source: "status_permissive_documents",
                                    value: context.status
                                }

                            }
                            ],
                            title: context?.meta?.full_name,
                            props: [
                                // TODO: Костыль
                                //prop('comment',
                                //    c => c?.meta?.comment),
                                // TODO: Костыль
                                prop('fio',
                                    c => {

                                        return c.face && `${c.face.surname || ""} ${c.face.name || ""} ${c.face.patronymic || ""}`}),
                                {
                                    props: ["model"],
                                    methods: {
                                        company_link(company_id) {
                                          return `/company/${company_id}`;
                                        },
                                    },
                                    template: `
                                    <div>
                                        <label>{{ $t('company') }}</label>
                                        <div>
                                            <router-link v-if='$develop.content_debug' :to='company_link(model.company_id)'>{{ model.company_id }}</router-link>
                                            <template v-else>{{ model.company_id }}</template>
                                        </div>
                                    </div>
                                    `
                                },
                                prop('pd_number',
                                    c => c?.meta?.document_number),
                                prop('visibility',
                                    c => c?.visibility),
                                context.meta.purchase_start_date && {
                                    props: ["model"],
                                    template: `<div v-if='model.meta'>
                                        <label>{{$t('purchase_start_date')}}</label>
                                        <div><iac-date :date="model.meta.purchase_start_date" withoutTime withMonthName/></div>
                                    </div>`
                                },
                                context.meta.document_date && {
                                    props: ["model"],
                                    template: `<div v-if='model.meta'>
                                            <label>{{$t('document_date')}}</label>
                                            <div><iac-date :date="model.meta.document_date" withoutTime withMonthName/></div>
                                        </div>`
                                },
                                context.meta.document_expiry_date && {
                                    props: ["model"],
                                    template: `<div v-if='model.meta'>
                                            <label>{{$t('document_expire')}}</label>
                                            <div v-if='model.meta.document_expiry_date && model.meta.document_expiry_date.date'><iac-date :date="model.meta.document_expiry_date.date" withoutTime withMonthName/></div>
                                            <div :title='$t("unlimited")' v-else>{{$t('unlimited')}}</div>
                                        </div>`
                                },
                                // prop('reject_reason',
                                //     c => context?.meta?.reason),
                                prop('tags',
                                    c => context?.meta?.tags,
                                    it => it.map(it => $t(it.name)).join(', ')),
                            ].filter(x => x),
                            description: [
                                ...context?.attachments.map(it => {
                                return {
                                    label: null,
                                    text: it.name,
                                    link: `${Config.api_server}/file/${it.id}`
                                }
                            }),
                            {
                                // label: "comment",
                                text: context?.meta?.comment
                            },
                            ...(context?.meta?.reason ? [{
                                props: ["model"], 
                                template: `
                                    <div class="clamp_5">
                                        <label>{{ $t('reject_reason') }}:</label>
                                        <span :title="model.meta.reason">{{ model.meta.reason }}</span>
                                    </div>
                                `,
                                
                            }] : []),
                            ],
                            
                            ...context,
                            actions: context.actions.map(action => {
                                if (action.is_reload) {
                                    action.response = ({ data }) => {
                                        data && this.permissiveDocumentsDataSource.reload();
                                    };
                                }

                                return action;
                            }),
                        };
                    },
                }),
                actions: [{
                    label: "add",
                    handler: async () => {

                        let get_type = async ()=>{
                            //let {type} = this.permissiveDocumentsDataSource.query || {};
                            
                            let type = undefined;
                            let type_items = this.type_source.items?.filter((item)=>{
                                return item.can_add
                            })    

                            if(!type){
                                // Если нет типа но есть в списке доступных только один
                                if(type_items && type_items.length == 1){
                                    type = type_items[0]
                                }                                
                            }

                            if(!type){
                                let {type:default_type} = this.permissiveDocumentsDataSource.query || {};
                                // Если нет типа то пытаемся получить из диалогового окна
                                type = await Vue.Dialog({
                                    props: ['source','default'],
                                    data: function(){
                                        return {
                                            field: {
                                                type: "entity",
                                                value: this.default || this.source[0],
                                                label: "type",
                                                dataSource: DataSource.get(this.source),
                                            },
                                            value: this.source[0].id
                                        }
                                    },
                                    template: `
                                        <div>
                                            <header>{{$t('permissive_document_header')}}</header>
                                            <main>
                                                <ui-field :model='field' />
                                            </main>
                                            <footer>
                                                <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('close')}}</ui-btn>
                                                <ui-btn type='primary' v-on:click.native='Close(field.value)'>{{$t('next')}}</ui-btn>
                                            </footer>
                                        </div>
                                    `
                                }).Modal({
                                    source: type_items,
                                    default: default_type
                                })
                            }

                            type = (type && type.id) || type
                            return type;
                        }

                        let type = await get_type();
                        if(!type){
                            return;
                        }

                        let { error, data } = await Http.api.rpc("company_ref", {
                            ref: "permissive_documents",
                            op: this.access === 'private' && 'upload' || 'add',
                            type: type,
                            data: {request_question: true}
                        });

                        (error && error.code != 'AbortError') && Vue.Dialog.MessageBox.Error(error);
                        !error && this.permissiveDocumentsDataSource.reload();
                    },
                    hidden: ()=>{

                        if(!this.type_source || !this.type_source.items)
                            return true;

                        let {type} = this.permissiveDocumentsDataSource.query || {};
                        type = (type && type.id)

                        let items = this.type_source.items.filter((item)=>{
                            return item.can_add && (!type || item.id == type)
                        })
                        return items.length <= 0
                    },
                }],
                query: {
                    type: {
                        type: "entity",
                        has_del: true,
                        dataSource: this.type_source
                    },
                    status: {
                        group: "status",
                        label: "!status",
                        type: "enum",
                        value: undefined,
                        dataSource: "status_permissive_documents",
                    },
                }
            })
        },
        async rejectPermissiveDocument(id) {
            const reason = await reject_dialog.Modal({
                size: "sm",
            });

            if (!reason) return;

            const { error, data } = await Http.api.rpc("company_ref", {
                ref: "permissive_documents",
                op: 'reject',
                data: {
                    id: id,
                    reason: reason
                }
            });

            (error && error.code != 'AbortError') && Vue.Dialog.MessageBox.Error(error);
            !error && this.permissiveDocumentsDataSource.reload();
        },
    },
    computed: {
        access_policy(){
            if(this.access == 'private')
                return this.system_access.policy.company_permissive_documents_edit
                       || this.system_access.policy.company_admin_workspace_pd_show
                       || this.system_access.policy.company_trader_pd_goods_list
                       || this.system_access.policy.company_client_counterterrorist_pd_goods_list
            if(this.access == 'moderate')
                return this.system_access.policy.company_permissive_documents_moderate;
            return false
        }
    },
    template: `
    <iac-access :access='access_policy' class='iac-drivers-list' :key='access'>
        <iac-section type='header'>
            <ol class='breadcrumb'>
                <li><router-link to='/'>{{$t('home')}}</router-link></li>
                <li>{{$t('nav.permissive_documents')}}</li>
            </ol>
            <div class='title'>
                <h1>{{$t('nav.permissive_documents')}}</h1>
            </div>
        </iac-section>

        <iac-section>
            <ui-error v-if='error' :code='error.status' :message='error.message'  :details='error.details' :number='error.number'/>
            <ui-data-view v-else-if='permissiveDocumentsDataSource' :dataSource='permissiveDocumentsDataSource'/>
        </iac-section>
    </iac-access>
    `
}
