import { DataSource } from '@iac/data';
import { Language, Util } from '@iac/core';

export function getGraphFinanceProps(contractBody) {
  return {
    graph_finance: {
      group: "contract.graphic",
      type: "data-grid",
      readonly: !contractBody.contract.rights || !contractBody.contract.rights.set_graph_finance_data,
      label: "!grid",
      dataSource: new DataSource({
        actions: [
          {
            label: "add",
            btn_type: "success",
            handler: async () => {
              let item = await contractBody.edit_graph_finance();
              if (!item || item == 2)
                return;

              contractBody.properties.graph_finance.dataSource.push_item({
                status: 1,

                date: item.date,
                summa: item.summa,
                advance_payment: item.advance_payment || 0,
                currency: item.currency,
                kls: item.kls,
                expense_item_code: item.expense_item_code,

              })
            }
          },
          {
            label: "save_changes",
            hidden: () => {
              let items = contractBody.properties.graph_finance.dataSource.items;
              if (!items)
                return true;
              return items.filter((item) => {
                if ((item.status & 7) != 0)
                  return true
              }).length <= 0;
            },
            handler: async () => {
              await contractBody.save(["graph_finance"]);
            }
          }
        ],
        store: {
          data: contractBody._graph_finance.map((item) => {
            return { ...item, status: 0 }
          }),
          context: (context) => {

            Object.defineProperty(context, "bindClass", {
              configurable: true,
              enumerable: true,
              get: () => {

                if ((context.status & 4) != 0)
                  return "ui-alert ui-alert-danger";

                if ((context.status & 1) != 0)
                  return "ui-alert ui-alert-success";

                if ((context.status & 2) != 0)
                  return "ui-alert ui-alert-warning";

              },
            });

            context.actions = [
              {
                btn_type: "warning",
                icon: "edit",
                hidden: () => {
                  return ((context.status & 4) != 0)
                },
                handler: async () => {
                  let item = await contractBody.edit_graph_finance(context);
                  if (!item || item == 2)
                    return;

                  context.date = item.date;
                  context.summa = item.summa;
                  context.advance_payment = item.advance_payment || 0;
                  context.currency = item.currency;
                  context.kls = item.kls;
                  context.expense_item_code = item.expense_item_code;

                  context.status |= 2;
                }
              },
              {
                btn_type: "danger",
                icon: "trash",
                hidden: () => {
                  return ((context.status & 4) != 0)
                },
                handler: () => {
                  context.status |= 4;
                }
              },
              {
                label: "Восстановить",
                btn_type: "danger",
                hidden: () => {
                  return !((context.status & 4) != 0)
                },
                handler: () => {
                  context.status &= ~4;
                }
              }
            ]

            return context
          }
        }
      }), attr: {
        action_name: Language.t("actions"),
        action_style: "min-width: 110px;text-align:left;",
        buttons: true,
        summary: true,
        not_found: "",
        columns: [{
          field: "date",
          display: (value) => {
            return `${Language.t('month_' + value.month)}, ${value.year}`;
          },
          style: "white-space: nowrap; text-align: right;"
        },
        {
          field: "kls", 
          style: "text-align: left;",
          hidden: true // Скрываем для упрощённых договоров
        },
        {
          field: "expense_item_code", 
          style: "width: 100%; text-align: left;",
          hidden: true // Скрываем для упрощённых договоров
        },
        {
          field: "summa", style: "white-space: nowrap; text-align: right;",
          display: (value, item) => {
            return `${Util.Number(value, ' ', 2)} ${item.currency}`;
          },
          summary: (items) => {
            if (!items)
              return;

            let currency = items.reduce((prev, item) => {
              if ((item.status & 4) != 0)
                return prev
              prev[item.currency] = prev[item.currency] || 0
              prev[item.currency] += item.summa
              return prev
            }, {});

            return Object.keys(currency).map((key) => {
              return `${Util.Number(currency[key], ' ', 2)} ${key}`
            }).join('<br/>')
          }
        },
        {
          field: "advance_payment", label: "advance_payment_amount", style: "white-space: nowrap; text-align: right;",
          display: (value, item) => {
            return `${Util.Number(value || 0, ' ', 2)} ${item.currency}`;
          },
          summary: (items) => {
            if (!items)
              return;

            let currency = items.reduce((prev, item) => {
              if ((item.status & 4) != 0)
                return prev
              prev[item.currency] = prev[item.currency] || 0
              prev[item.currency] += item.advance_payment || 0
              return prev
            }, {});

            return Object.keys(currency).map((key) => {
              return `${Util.Number(currency[key], ' ', 2)} ${key}`
            }).join('<br/>')
          }
        },
        ]
      }
    }
  };
}
