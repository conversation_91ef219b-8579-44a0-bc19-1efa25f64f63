import { Context } from '@iac/kernel';
import { Action } from '@iac/core';
import { DataSource, RefStore, Query } from '@iac/data';


export default {
  data() {
    return {
      user: Context.User,
      ad: new DataSource({
        limit: 9,
        query: new Query({
          status: {
            group: "status",
            label: "!",
            type: 'enum',
            has_del: true,
            dataSource: 'ref_ad_status_private',
          },
          product_id: {
            group: "choose_product",
            type: 'entity',
            label: '!choose_product',
            has_del: true,
            dataSource: new DataSource({
              valueExp: 'product_id',
              displayExp: "product_name",
              search: true,
              store: new RefStore({
                ref: "ref_online_shop_products_private",
                key:"product_id"
              })
            }),
            multiple: true,
          },
          is_gos_shop: {
            label: "gos_shop",
            group: "spaces_filter",
            type: "bool",
            hidden: ()=>!(this.$settings?.business || this.$settings?.ecosystem)
          },
          is_comm_shop: {
            label: "comm_shop",
            group: "spaces_filter",
            type: "bool",
            hidden: ()=>!this.$settings?.business
          },
          ecosystem_producer: {
            label: "eco_system",
            group: "spaces_filter",
            type: "bool",
            hidden: ()=>!this.$settings?.ecosystem
          },
          text: {
            icon: 'search',
            label: "!Search",
            order: -1,
            hidden: true,
            has_del: true
          },
          green: {
            type: "entity",
            label: "!green_procedures",
            group: "green_procedures",
            has_del: true,
            dataSource: [{id: true,name: "yes"},{id: false,name: "no"}],
            hidden: ()=>!this.$settings?.procedures?._green
          }
        }),
        store: new RefStore({
          ref: 'ref_online_shop_private',
          injectQuery: (params) => {
            params.fields = ["green","product","unit","id","publicated_at","status","name","price","close_at","totalcost","currency", "amount","min_amount","images","owner_legal_area_id","product_name","remain_time"]
            if (this.$develop.content_debug) {
              params.fields.push("debug_info");
            }

            params.query = params.filters.text;
            params.filters.text = undefined;

            params.filters.delivery_regions = params.filters.area_path;
            params.filters.area_path = undefined;

            params.filters.is_gos_shop = params.filters.is_gos_shop ? true : undefined;
            params.filters.is_comm_shop = (params.filters.is_comm_shop && this.$settings.business) ? true : undefined;
            params.filters.ecosystem_producer = (params.filters.ecosystem_producer&& this.$settings.ecosystem) ? true : undefined;
            return params;
          }
        }),
      }),
    };
  },
  methods: {
    async create() {
      const response = await Action['procedure.create']({
        type: 'ad',
      });

      if (!response) {
        return;
      }
      const { error, data } = response;
      if (!error) {
        this.$router.push({ path: `/procedure/${data[0].proc_id}/core` });
      }
    },
  },
  template: `
    <iac-access :access='$policy.ad_click || $policy.ad_create || $policy.ad_sign || $policy.comm_ad_click || $policy.comm_ad_create || $policy.comm_ad_sign'>
      <iac-section type='header'>
        <ol class='breadcrumb'>
          <li><router-link to='/'>{{ $t('home') }}</router-link></li>
          <li>{{ $t('nav.my_ad') }}</li>
        </ol>
        <div class='title'>
          <h1>{{ $t('nav.my_ad') }}</h1>
          <div v-if='$policy.ad_create || $policy.comm_ad_create'>
            <ui-btn type='primary' @click.native='create'>{{ $t('create_ad') }}</ui-btn>
          </div>
        </div>
      </iac-section>
      <iac-section>
        <ui-data-list search='text' :dataSource='ad'>
          <template slot='items' slot-scope='props'>
            <ui-list :dataSource='ad'>
              <div slot='items' slot-scope='props' class='iac-grid'>
                <widget-shop v-for='item in props.items' :key='item.id' :item='item' />
              </div>
            </ui-list>
          </template>
        </ui-data-list>
      </iac-section>
    </iac-access>
  `,
};
