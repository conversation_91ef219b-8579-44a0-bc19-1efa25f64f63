import { Entity, DataSource } from '@iac/data';
import { Settings } from '@iac/kernel';

export default class SimplifiedContractProduct extends Entity {
  constructor(context, contract) {
    super(context);

    this.is_simplified = true; // Флаг упрощённой закупки

    this.claim_id = context.claim_id;
    this.product = context.product;
    this.start_price = context.start_price || context.price;
    this.start_currency = context.currency || Settings._default_currency;
    this.price = context.price;
    this.currency = context.currency || Settings._default_currency;

    if (context.product?.unit) {
      this.amount = `${context.amount} ${context.product.unit}`;
    } else {
      this.amount = context.amount;
    }

    this.delivery_month = context.delivery_month;
    this.delivery_year = context.delivery_year;

    this.conditions = context.conditions;
    this.delivery_address = context.delivery_address;
    this.country = context.country;
  }

  props() {
    return {
      product: {
        type: "product",
        readonly: true,
        attr: {
          eye: true,
          short: true
        },
      },
      start_price: {
        group: "<start_price>",
        type: "float",
        label: "Стартовая цена",
        readonly: true,
        attr: { react: true },
      },
      start_currency: {
        group: "<start_price>",
        label: "currency",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true
      },
      price: {
        group: "<price>",
        type: "float",
        required: true,
        attr: { react: true },
        validate: () => {
          if (this.price <= 0) {
            return "Укажите значение больше 0";
          }

          if (this.price > this.start_price) {
            return "Значение не может превышать стартовую цену";
          }
        }
      },
      currency: {
        group: "<price>",
        type: "entity",
        dataSource: 'ref_currency',
        readonly: true,
      },
      delivery_month: {
        group: '<delivery>',
        type: "entity",
        label: "contract.delivery_month",
        dataSource: DataSource.get("ref_months"),
        required: true,
      },
      delivery_year: {
        group: "<delivery>",
        label: "contract.delivery_year",
        max: 2100,
        type: "number",
        required: true,
      },
      country: {
        type: "entity",
        label: "country",
        dataSource: 'ref_country_',
        required: true,
      },
      conditions: {
        type: "text",
        label: "conditions_characteristics",
        required: true,
      },
      delivery_address: {
        type: "text",
        required: true,
      },
    }
  }
}
