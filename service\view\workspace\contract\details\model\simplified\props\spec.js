import { DataSource } from '@iac/data';
import { Language, Util } from '@iac/core';
import SimplifiedContractViewProduct from '../view_product';
import { SearchInput } from '../utils';

export function getSpecProps(contractBody) {
  return {
    spec: {
      group: "contract.spec",
      readonly: !contractBody.contract.rights || !contractBody.contract.rights.set_specification,
      type: "data-grid",
      label: "!spec",
      dataSource: {
        actions: [
          {
            label: "save_changes",
            hidden: () => {
              if (!contractBody.contract.rights || !contractBody.contract.rights.set_specification) {
                return true;
              }

              let items = contractBody.properties.spec.dataSource.items;
              if (!items)
                return true;
              return items.filter((item) => {
                if ((item.status & 7) != 0)
                  return true
              }).length <= 0;
            },
            handler: async () => {
              await contractBody.save(["spec"]);
            }
          }
        ],
        store: {
          data: contractBody._spec.map((item) => {
            return { ...item, status: 0, country: item.country, is_gos: contractBody.is_gos, proc_type: contractBody.proc_type }
          }),
          context: (context) => {

            Object.defineProperty(context, "readonly", {
              configurable: true,
              enumerable: true,
              get: () => {
                return !contractBody.contract.rights || !contractBody.contract.rights.set_specification;
              }
            });

            context.setCountry = (country) => {
              context.country = country;
              context.status &= ~8
              contractBody.save(["spec"])
            }

            context.checkbox = true;
            Object.defineProperty(context, "bindClass", {
              configurable: true,
              enumerable: true,
              get: () => {

                if ((context.status & 4) != 0)
                  return "ui-alert ui-alert-danger";

                if ((context.status & 1) != 0)
                  return "ui-alert ui-alert-success";

                if ((context.status & 2) != 0)
                  return "ui-alert ui-alert-warning";

                if ((context.status & 8) != 0)
                  return "ui-alert ui-alert-danger";

              },
            });

            context.actions = [
              {
                btn_type: "warning",
                icon: "edit",
                hidden: () => {
                  return (
                    !context.id
                    || !contractBody.contract.rights
                    || !contractBody.contract.rights.set_specification
                    || ((context.status & 4) != 0)
                  );
                },
                handler: async () => {
                  let item = await contractBody.edit_product(context);
                  if (!item || item == 2)
                    return;

                  context.claim_id = item.claim_id || context.claim_id;
                  context.product = item.product || context.product;
                  context.amount = item.amount || context.amount;
                  context.unit = item.unit || context.unit;
                  context.start_price = item.start_price || context.start_price || context.price;
                  context.price = item.price || context.price;
                  context.currency = item.currency || context.currency;
                  context.country = item.country || context.country;
                  context.delivery_address = item.delivery_address || context.delivery_address;
                  context.conditions = item.conditions || context.conditions;
                  context.delivery_month = item.delivery_month || context.delivery_month;
                  context.delivery_year = item.delivery_year || context.delivery_year;

                  context.status |= 2;
                }
              }
            ]
            return context;
          }
        }
      },
      attr: {
        action_name: Language.t("actions"),
        action_style: "min-width: 110px;text-align:left;",
        buttons: true,
        summary: true,
        not_found: "",
        columns: [
          {
            field: "product", label: "product_name", style: "width: 100%; text-align: left;",
            display: (product) => {
              return product.product_name || product.name
            },
            component: {
              props: ["item"],
              methods: {
                show_properties(item) {
                  Vue.Dialog({
                    props: ['position', 'product'],
                    template: `
                          <div>
                            <header>{{product.product_name || product.name}}</header>
                            <main>
                              <ui-layout :fields='position.fields' />
                              <ui-layout-group label="Свойства">
                                <iac-layout-static :value='product.product_properties || product.properties' />
                              </ui-layout-group>
                            </main>
                            <footer>
                              <ui-btn type='secondary' v-on:click.native='Close'>{{$t('Close')}}</ui-btn>
                            </footer>
                          </div>
                        `
                  }).Modal({
                    position: new SimplifiedContractViewProduct(item),
                    product: item.product,
                  })
                }
              },
              template: `
                  <div>
                    <a href='#' v-on:click.prevent='show_properties(item)'>{{item.product.product_name || item.product.name}}</a>
                  </div>
                `
            },
            summary: (items) => {
              if (!items)
                return;
              let count = items.filter((item) => {
                return !((item.status & 4) != 0);
              }).length;
              return `${count} ${Language.t("product", { count: count })}`
            }
          },
          contractBody.is_gos && {
            field: "account",
            display: (_, item) => {
              let data = contractBody.claim_data[item.claim_id];
              return data && data.account;
            }
          },
          contractBody.is_gos && {
            field: "expense_item_code",
            display: (_, item) => {
              let data = contractBody.claim_data[item.claim_id];
              return data && data.expense_item_code;
            }
          },
          {
            field: "country", label: "country", display: (value) => {
              return (value && value.name) ? value.name : value
            },
            component: {
              props: ["item"],
              readonly: true,
              data: function () {
                return {
                  dataSource: DataSource.get('ref_country_')
                }
              },
              methods: {
                async select_country() {
                  let country = await Vue.Dialog({
                    data: function () {
                      return {
                        search_text: undefined,
                        dataSource: DataSource.get('ref_country_')
                      }
                    },
                    methods: {
                      onItem(item) {
                        this.Close(item);
                      },
                      search(text) {
                        if (this.search_text == text)
                          return;
                        this.search_text = text
                        this.dataSource.query.search(text);
                      },
                    },
                    components: {
                      SearchInput: SearchInput
                    },
                    template: `
                        <div>
                          <main style='flex: 0 0 auto; padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid #ccc; z-index: 1; top: 0; position: sticky; background: #fff'>
                            <SearchInput :text='search_text' v-on:search="search" />
                          </main>
                          <main>
                            <ui-list :dataSource='dataSource'  v-on:item='onItem'/>
                          </main>
                          <footer></footer>
                        </div>
                      `
                  }).Modal()

                  if (country && country.exp) {
                    this.item.setCountry(country.exp.value)
                  }
                }
              },
              template: `
                  <div style='white-space: nowrap'>
                    <span v-if='item.country'>{{item.country.name}}</span>
                    <span v-else>Не указана</span>
                  </div>
                `
            }
          },
          {
            field: "amount",
            style: "white-space: nowrap; text-align: right;",
            component: {
              props: ["item"],
              template: `
                  <div><iac-number :value='item.amount' delimiter=' ' /> {{item.unit}}</div>
                `
            }
          },
          {
            field: "price", label: "price_unit", style: "white-space: nowrap; text-align: right;",
            display: (value, item) => {
              return `${Util.Number(value, ' ', 2)} ${item.currency}`;
            }
          },
          {
            field: "summary", label: "total", style: "white-space: nowrap; text-align: right;",
            display: (_, item) => {
              return `${Util.Number(item.amount * item.price, ' ', 2)} ${item.currency}`;
            },

            summary: (items) => {
              if (!items)
                return;

              let currency = items.reduce((prev, item) => {
                if ((item.status & 4) != 0)
                  return prev
                prev[item.currency] = prev[item.currency] || 0
                prev[item.currency] += item.amount * item.price
                return prev
              }, {});

              var total = Object.keys(currency).map(function (key) {
                return { currency: key, amount: currency[key] };
              });

              return total.map((item) => {
                return `${Util.Number(item.amount, ' ', 2)} ${item.currency}`
              }).join('<br/>')
            },

          },

        ]
      }
    }
  };
}
