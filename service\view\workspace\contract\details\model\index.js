import { Entity, DataSource, Query, Property } from '@iac/data';
import { Http, Event, Language, Action, Util } from '@iac/core';
import { Config, Context, Settings, ecp } from '@iac/kernel';
import Dialog from './../_dlg_add_extra';

import ExchangeContractBody from './exchange_body'
import CustomContractBody from './custom_body'
import SimplifiedContractBody from './simplified_body'

export default class Contract extends Entity {
  @Event onReload;
  constructor(context = {}) {
    super(context);
    this.rights = undefined;
    this.update_rights_key = 0;
    this.actions = undefined;
    this.update_context(context);
  }

  async update_contract_detailed(fields = []) {
    let { data, error } = await Http.api.rpc('contract_detailed', {
      number: this.number,
      fields: (fields && fields.length > 0) ? fields : undefined
    });

    if (error) {
      return Vue.Dialog.MessageBox.Error(error)
    }

    await this.update_context({ ...this.context, ...data, rights: undefined })

  }

  update_shipment_docs(shipment_docs) {

    this.shipment_docs = DataSource.get({
      actions: [
        {
          label: `reserve_create_documents`,
          type: "request",
          method: "contract_action",
          params: {
            action: "create_documents",
            number: this.number,
            //params,
          },
          hidden: () => {
            return !this.rights.create_documents;
          },
          response: ({ data }) => {
            data && this.update_context()
          }
        },
        ...[
          ...["accept", "remark", "complaint_buyer", "complaint_seller", "edit"].map((action) => {
            return {
              label: `reserve_${action}`,
              type: "request",
              btn_type: action == "accept" ? "success" : "warning",
              method: "contract_action",
              params: {
                action: `reserve_${action}`,
                number: this.number,
                params: {
                  id: (shipment_docs && shipment_docs.length > 0) ? (shipment_docs.length - 1) : undefined
                }
              },
              hidden: () => {
                return !this.rights[`reserve_${action}`];
              },
              response: async ({ data }) => {
                data && await this.update_context()
              }
            }
          })
        ]
      ],
      store: {
        data: shipment_docs || [],
        context: (context) => {

          Object.defineProperty(context, "bindClass", {
            configurable: true,
            enumerable: true,
            get: () => {

              if (context.status == 'wait' || context.status == "edited")
                return "ui-alert ui-alert-success";

              if (context.status == "remark")
                return "ui-alert ui-alert-danger";
              if (context.status == 'accepted')
                return;

              return "ui-alert ui-alert-warning";

            },
          });

          return context;
        },
        inject: (items) => {
          return items.sort((a, b) => {
            return a.date > b.date ? -1 : 1
          });
        },
        inject1: (items) => {
          if (!items || items.length <= 0)
            return;

          items[items.length - 1].actions = [
            ...["accept", "remark", "complaint_buyer", "complaint_seller", "edit"].map((action) => {
              return {
                label: `reserve_${action}`,
                type: "request",
                method: "contract_action",
                params: {
                  action: `reserve_${action}`,
                  number: this.number,
                  //params,
                },
                hidden: () => {
                  return !this.rights[`reserve_${action}`];
                },
                response: ({ data }) => {
                  data && this.update_context()
                }
              }
            })
          ]

          return items
        }
      }
    });
  }

  async update_context(context) {

    if (!context && this.number) {
      let { data } = await Http.api.rpc('contract_detailed', {
        number: this.number,
      });
      context = data;
    }

    if (!context) {
      return;
    }

    this.context = context;

    context.data = context.data || {};

    let isugf_reply_positive = context["isguf_reply_positive?"] != undefined ? context["isguf_reply_positive?"] : context["isugf_reply_positive?"];

    this.isugf_reply_positive = isugf_reply_positive == undefined ? true : isugf_reply_positive;
    this.isugf_reply = context.isugf_reply || {};

    this.lot_id = context.lot_id;
    this.ckepik_id = context.ckepik_id;

    this.confirm_income_date = context.confirm_income_date;
    this.delivery_date = context.real_end_date;

    this.ready_for_delivery = context.ready_for_delivery || {};
    this.number = context.number;
    this.title = context.title;
    this.debug = context.debug;
    this.status = context.status;
    this.update_shipment_docs(context.shipment_docs);
    this.end_wait_offer_date = context.end_wait_offer_date
    this.unblock_deposit_status = context.unblock_deposit_status;
    this.product = context.product;
    this.request_info = {
      title: context.product && context.product.name,
      brand: context.brand,
      producer: context.producer,
      best_before: context.best_before,
      price: context.contragent && context.contragent.price,
      country: context.country,
      delivery: (context.delivery && typeof context.delivery == 'object') ? (`${context.delivery.amount} ${Language.t(context.delivery.unit, {
        count: context.delivery.amount
      })}`) : context.delivery,
      desc: context.desc,
      year: context.year,
      guarantee: context.guarantee,
      license: context.license,
      amount: context.amount,
      unit: context.unit,
    }
    this.reserve_contragent = context.reserve_contragent;  
    this.is_reserve = context.is_reserve;
    this.winner_reject_accept_at = context.winner_reject_accept_at;
    this.contract_close_at = context.contract_close_at;

    this.contract_close_at_formatted = context.contract_close_at ? new Date(context.contract_close_at).toLocaleDateString() : null;
    this.winner_reject_accept_at_formatted = context.winner_reject_accept_at ? new Date(context.winner_reject_accept_at).toLocaleDateString() : null;

    this.contract_name = context.contract_name;
    this.org_company = context.org_company || {};
    this.initiator = context.initiator || {
      company_details: {}
    };
    this.initiator_broker = context.initiator_broker || {};
    this.org_company_address = context.org_company_address || {};
    this.finances = context.finances || [];
    this.proc_type = context.proc_type;
    this.proc_id = context.proc_id;

    this.end_wait_delete_date = context.data.end_wait_delete_date;
    this.end_wait_additional_contract_date = context.data.end_wait_additional_contract_date;

    this.execution = {
      base: {
        status: undefined,
        value: context.execution ? context.execution.base : undefined,
      },
      termination_reason: {
        status: undefined,
        value: context.execution ? context.execution.termination_reason : undefined,
      },
      who_pays: {
        status: undefined,
        value: context.execution ? context.execution.who_pays : undefined,
      },
      who: context.execution ? context.execution.who : null,
      fine_reason: {
        status: undefined,
        value: context.execution ? context.execution.fine_reason : undefined,
      },
      fine_amount: {
        status: undefined,
        value: context.execution ? context.execution.fine_amount : undefined,
      },
      fine_currency: {
        status: undefined,
        value: context.execution ? context.execution.fine_currency : undefined,
      },
    };

    //let ref_unit = DataSource.get('ref_unit');
    let unit_of_good;// = await ref_unit.byKey(context.unit_of_good)

    if (this.proc_type == "schedule_exchange_contract") {
      this.exchangeBody = new ExchangeContractBody({
        signed_at: context.signed_at,
        partial_paid_at: context.partial_paid_at,

        good: context.good,
        complaint_number: context.complaint_number,
        country_of_production: context.country_of_production,
        type_of_packing: context.type_of_packing,
        unit_of_packing: context.unit_of_packing,
        type_of_packing_description: context.type_of_packing_description,
        parent_id: context.parent_id,
        proc_id: context.proc_id,
        timers: context.timers,
        execution: context.execution,
        rkp_fine: context.rkp_fine,

        period_of_delivery: context.period_of_delivery,
        period_of_paying: context.period_of_paying,
        incoterms: context.incoterms,
        type_of_vehicle: context.type_of_vehicle,
        stock_address: context.stock_address,
        procent_of_nedogruz: context.procent_of_nedogruz,

        base_for_create_additional_contract: context?.base_for_create_additional_contract,

        price: context.price,
        amount_of_bought_lots: context.amount_of_bought_lots,
        amount_of_good_in_lot: context.amount_of_good_in_lot,
        vat: context.vat,
        unit_of_good: unit_of_good?.name || context.unit_of_good,

      }, this);
    }

    // Инициализируем права доступа перед созданием body
    await this.update_rights(context.rights);

    if (this.proc_type == "custom_contract") {
      this.customBody = new CustomContractBody({
        spec_totalcost_amount: context.data.spec_totalcost_amount,
        lot_id: context.data.lot_id,
        ckepik_id: context.data.ckepik_id,
        vat_nds: context.data.vat_nds,

        claim_data: context.claim_data,
        currency: context.currency,

        version_number: context.data.version_number,
        contract_name: context.data.contract_name,
        hide_in_public: context.data.hide_in_public,
        contract_type: context.data.contract_type,
        contract_reason: context.data.contract_reason,
        addons: context.data.addons,
        reason: context.data.reason,
        footing: context.data.footing,
        reason_cancellation: context.data.reason_cancellation,
        grounds_cancellation: context.data.grounds_cancellation,
        execution_date: context.data.execution_date,
        contract_close_at: context.data.contract_close_at,

        contragent: context.data.contragent,

        beneficiary: context.data.beneficiary,
        delivery_days: context.data.delivery_days,
        payment_days: context.data.payment_days,
        advance_payment_days: context.data.advance_payment_days,
        special_conditions: context.data.special_conditions,

        spec: context.data.spec || [],
        graph: context.data.graph || [],
        graph_finance: context.data.graph_finance || [],
        
        additional_contract: context['additional_contract?'],

      }, this)
    } else if (this.proc_type == "simplified_contract") {
      this.simplifiedBody = new SimplifiedContractBody({
        spec_totalcost_amount: context.data.spec_totalcost_amount,
        lot_id: context.data.lot_id,
        ckepik_id: context.data.ckepik_id,
        vat_nds: context.data.vat_nds,

        claim_data: context.claim_data,
        currency: context.currency,

        version_number: context.data.version_number,
        contract_name: context.data.contract_name,
        hide_in_public: context.data.hide_in_public,
        contract_type: context.data.contract_type,
        contract_reason: context.data.contract_reason,
        addons: context.data.addons,
        reason: context.data.reason,
        footing: context.data.footing,
        reason_cancellation: context.data.reason_cancellation,
        grounds_cancellation: context.data.grounds_cancellation,
        execution_date: context.data.execution_date,
        contract_close_at: context.data.contract_close_at,

        contragent: context.data.contragent,

        beneficiary: context.data.beneficiary,
        delivery_days: context.data.delivery_days,
        payment_days: context.data.payment_days,
        advance_payment_days: context.data.advance_payment_days,
        special_conditions: context.data.special_conditions,

        spec: context.data.spec || [],
        graph: context.data.graph || [],
        graph_finance: context.data.graph_finance || [],
        
        additional_contract: context['additional_contract?'],

    //   }, this)
    // }

    // // Сохраняем тип договора для использования в компонентах
    // this.contract_type = context.data?.contract_type;
    
    // // ВРЕМЕННО: для тестирования упрощённых договоров
    // if (context.data?.contract_type === 'simplified_contract' && this.proc_type === 'custom_contract') {
    //   console.log(' Переключаем на упрощённый договор:', context.data.contract_type);
    //   this.proc_type = 'simplified_contract';
    //   // Пересоздаём модель с правильным типом
    //   this.simplifiedBody = new SimplifiedContractBody({
    //     spec_totalcost_amount: context.data.spec_totalcost_amount,
    //     lot_id: context.data.lot_id,
    //     ckepik_id: context.data.ckepik_id,
    //     vat_nds: context.data.vat_nds,
    //     claim_data: context.claim_data,
    //     currency: context.currency,
    //     version_number: context.data.version_number,
    //     contract_name: context.data.contract_name,
    //     hide_in_public: context.data.hide_in_public,
    //     contract_type: context.data.contract_type,
    //     contract_reason: context.data.contract_reason,
    //     delivery_days: context.data.delivery_days,
    //     payment_days: context.data.payment_days,
    //     special_conditions: context.data.special_conditions,
    //     spec: context.data.spec || [],
    //     contragent: context.data.contragent,
    //     additional_contract: context['additional_contract?'],
        
      }, this)
    }


    this.tender_close_at = context.tender_close_at;
    this.protocol = context.protocol || {};
    this.contract_close_at = context.contract_close_at;
    this.inserted_at = context.inserted_at;
    this.contract_totalcost = context.contract_totalcost;
    this.currency = context.currency;
    this.execution_date = context.execution_date;
    this.execution_days = {
      value: context.execution_days ?? undefined,
      status: undefined,
    };
    this.base = context.base;
    this.contract_body = context.contract_body;
    this.org_reject_reason = context.org_reject_reason;
    this.winner_reject_reason = context.winner_reject_reason;
    this.additional_contract = context['additional_contract?'];

    this.other_requests = context.other_requests || [];

    this.spec = context.spec || [];
    this.country_ids = {};
    this.spec = this.spec.map((el) => {
      el.product = el.product || {
        id: el.id,
        name: el.name
      }

      let suggested_price = parseFloat(el.suggested_price);
      suggested_price = !isNaN(suggested_price) ? parseFloat(el.suggested_price.toFixed(2)) : undefined;
      let ret = {
        ...el,
        suggested_price,
      };

      if (el.country) {
        this.country_ids[el.country] = el.country;
      }

      return ret;
    });

    let country_ids_array = Object.keys(this.country_ids) || [];
    if (country_ids_array && country_ids_array.length > 0)
      DataSource.get('ref_country').byKeys(country_ids_array).then((data) => {
        data.forEach((county) => {
          this.country_ids[county.code] = county.name
        })
      })

    this.graphic = (Array.isArray(context.graphic) && context.graphic) || [];

    if (this.context.position_src) {
      this.is_gos = this.context.position_src == 'claim'
    } else {
      this.is_gos = this.graphic.filter((item) => {
        return item.details || item.base
      }).length > 0;
    }

    let expense_codes = {};
    this.graphic.forEach((item) => {
      if (item.base && !item.base.code) {
        item.base = {
          code: item.base,
          name: undefined
        }
        expense_codes[item.base.code] = expense_codes[item.base.code] || []
        expense_codes[item.base.code].push(item.base)
      }
    })

    let expense_codes_array = Object.keys(expense_codes) || []
    if (expense_codes_array && expense_codes_array.length > 0)
      DataSource.get("ref_expense_item").byKeys(expense_codes_array).then((expenseList) => {
        expenseList.forEach((expense) => {
          let items = expense_codes[expense.code];
          if (items) {
            items.forEach((item) => {
              item.name = expense.name
            })
          }
        })
      })




    this.contragent = context.contragent || {
      company_details: {}
    };
    this.contragent_broker = context.contragent_broker || {};

    this.org_banking_details = context.org_banking_details || (context.initiator && context.initiator.banking_details) || undefined;//{};
    this.contragent_banking_details = context.contragent_banking_details || (context.contragent && context.contragent.banking_details) || undefined;//{};

    this.spec_totalcost = context.spec_totalcost || (context.data && context.data.spec_totalcost);
    this.version_number = context.version_number || (context.data && context.data.version_number);
    this.timers = context.timers;
    this.spec_suggested_totalcost = context.spec_suggested_totalcost;

    this.isugf = context.isugf;
    const states = { "2": "completed", "3": "deleted", "4": "cancelled", "not_availabled": "not_availabled" }
    if (this.isugf) {
      this.isugf.invoices = Object.keys(this.isugf?.invoices || {})
        .map(key => {
          const invoice = this.isugf.invoices[key]
          invoice.DATEDOC = invoice.DATEDOC ? new Date(invoice.DATEDOC) : ''
          invoice.DATEINVOICE = invoice.DATEINVOICE ? new Date(invoice.DATEINVOICE) : ''
          invoice.STATE = states[invoice.STATE || "not_availabled"] || states["not_availabled"]
          return invoice
        })
        .sort((a, b) => a.INVOICEID - b.INVOICEID)
    }

    this.deposit_sum = context.deposit_sum;

    this.base_currency = this.currency || (this.base && this.base.currency) || "";
    this.contract_currency = this.contragent.currency || this.base_currency

    if (this.base_currency != this.contract_currency) { //${Util.Number(exchange_rates, ' ', 2)}
      let exchange_rates = this.context.exchange_rates[this.contract_currency.toLocaleLowerCase()];

      this.exchange_rates = `1 ${this.contract_currency} = ${Util.Number(exchange_rates, ' ', 2)} ${this.base_currency}`
    } else {
      this.exchange_rates = undefined;
    }

    this.exchange_rates = context.exchange_rates || {}

    this.max_fine_sum = context.max_fine_sum;
  }

  setError(context, errors) {
    errors.forEach(({ name, message }) => {
      let field = context[name];
      if (!field) {
        return;
      }

      field.status = {
        type: 'error',
        message,
      };
    });
  }


  async request_action(name, {params = {},question, success, subscribe, update_context}) {
    if (question && (await Vue.Dialog.MessageBox.Question(Language.t(question)) != Vue.Dialog.MessageBox.Result.Yes)) {
      return {
        error: {
          code: "AbortError",
          message: "AbortRequest"
        }
      }
    }
    let sign = undefined;
    if (subscribe) {
      let response = await this.action('get_digest_hash');
      if (response?.data) {
        response = await ecp.subscribe(response.data) || {
          error: {
            code: "AbortError",
            message: "AbortRequest"
          }
        };
      }

      if (response.error) {
        if (response.error.code != 'AbortError')
          await Vue.Dialog.MessageBox.Error(response.error);
        return { error: response.error };
      }
      sign = response.data;
    }
    params.sign = sign;
    let { error, data } = await Contract.Action(name, this.number, params);
    if (error && error.code != 'AbortError')
      await Vue.Dialog.MessageBox.Error(error);

    if(!error && success){
      await Vue.Dialog.MessageBox.Success(Language.t(success));
    }

    if(update_context){
      await this.update_context()
    }

    return { error, data };
  }

  // #region методы которые используют request_action
  async sign_action(action, question = 'contract.question_sign') {
    return await this.request_action(action, {
      question: question,
      subscribe: true,
      update_context: true
    })
  }

  async return_rkp_pays() {
    return await this.request_action(`return_rkp_pays`,{
      question: 'return_rkp_pays_question',
      update_context: true
    })
  }

  async buyers_blocked() {
    return await this.request_action(`buyers_blocked`,{
      question: 'buyers_blocked_question',
      subscribe: true,
      update_context: true
    })
  }

  async cancel_contract(part) {
    return await this.request_action(`cancel_contract_${part}`,{
      update_context: true,
      success: 'cancel_contract_success'
    })
  }

  async complaint(part) {
    return await this.request_action(`${part}_complaint`,{
      update_context: true,
    })
  }

  async cancel_org_return() {
    return await this.request_action("cancel_org_return", {
      update_context: true,
    });
  }

  async winner_join_contract() {
    return await this.request_action("winner_join_contract", {
      update_context: true,
    });
  }

  async winner_sign() {
    return await this.request_action("winner_sign", {
      update_context: true,
    });
  }

  async cancel_winner_sign() {
    return await this.request_action("cancel_winner_sign", {
      update_context: true,
    });
  }

  async cancel_winner_return() {
    return await this.request_action("cancel_winner_return", {
      update_context: true,
    });
  }

  async org_sign() {
    return await this.request_action("org_sign", {
      update_context: true,
    });
  }

  async cancel_org_sign() {
    return await this.request_action("cancel_org_sign", {
      update_context: true,
    });
  }

  async finalize_contract_org() {
    return await this.request_action("finalize_contract_org", {
      update_context: true,
    });
  }

  async finalize_contract_winner() {
    return await this.request_action("finalize_contract_winner", {
      update_context: true,
    });
  }

  async finalize_winner_return() {
    return await this.request_action("finalize_winner_return", {
      update_context: true,
    });
  }

  async finalize_winner_sign() {
    return await this.request_action("finalize_winner_sign", {
      update_context: true,
    });
  }

  async finalize_org_return() {
    return await this.request_action("finalize_org_return", {
      update_context: true,
    });
  }

  async finalize_org_sign() {
    return await this.request_action("finalize_org_sign", {
      update_context: true,
    });
  }

  async user_repeat_isugf_call() {
    return await this.request_action("user_repeat_isugf_call", {
      update_context: true,
    });
  }


  async reserve_complaint_seller() {
    return await this.request_action("reserve_complaint_seller", {
      update_context: true,
    });
  }


  // #endregion


  async edit_information() {
    const { contract_name, execution_days } = this;
    const { error } = await this.action('edit_information', {
      contract_name,
      execution_days: execution_days.value,
    });
    if (error !== undefined) {
      if (Array.isArray(error.data)) {
        this.setError(this, error.data);
      }
      Vue.Dialog.MessageBox.Error(error);
    } else {
      execution_days.status = undefined;
    }
  }

  async edit_specification() {
    const params = this.spec.map((item) => {
      const price = parseFloat(item.suggested_price);
      item.suggested_price = price;
      return {
        id: item.id,
        price,
      };
    });

    const { error, data } = await this.action('edit_specification', params);
    if (!error) {
      this.spec_suggested_totalcost = data;
      await this.update_context();
    } else {
      Vue.Dialog.MessageBox.Error(error);
    }
  }

  get is_edit_execution() {
    const { edit_execution_org, edit_execution_winner } = this.rights;
    return edit_execution_org || edit_execution_winner;
  }

  get is_show_termination() {
    const { edit_execution_org, edit_execution_winner } = this.rights;

    let execution_base = this.execution && this.execution.base && this.execution.base.value;
    execution_base = execution_base?.id || execution_base;

    if (edit_execution_winner && execution_base == 2 && (this.proc_type == 'request' || this.proc_type == 'reduction')) {
      return false;
    }

    if (this.unblock_deposit_status != "for_execution" && this.execution.base?.value && (this.is_edit_execution || this.execution.who_pays?.value))
      return true
    /*
    
        if (!edit_execution_org && execution_base == 2 && (this.proc_type == 'request' || this.proc_type == 'reduction')) {
          return false;
        }
    
        return true*/
  }
  async edit_execution(part) {
    if(!part)
      part = this.rights.edit_execution_org ? 'execution_org' : 'execution_winner';
    
    let { base, termination_reason, who_pays, fine_reason, fine_amount, } = this.execution;

    fine_reason = fine_reason.value == "" ? undefined : fine_reason.value;
    fine_amount = (fine_amount.value && fine_amount.value == "") ? undefined : fine_amount.value;
    who_pays = (who_pays.value && who_pays.value.exp ? who_pays.value.exp.value : who_pays.value);
    let fine_currency = ((fine_amount != undefined && fine_amount != null) ? this.currency : undefined) || null;
    if (!who_pays) {
      who_pays = fine_reason = fine_amount = fine_currency = null;
    }

    base = base && base.value && base.value.exp ? base.value.exp.value : base.value;
    termination_reason = termination_reason && termination_reason.value && termination_reason.value.exp ? termination_reason.value.exp.value : termination_reason.value;

    if (!this.is_edit_execution || !this.is_show_termination || !base) {
      who_pays = fine_reason = fine_amount = fine_currency = null;
    }

    if (!base || base != 1) {
      termination_reason = null;
    }



    let params = {
      base: base,
      termination_reason: termination_reason,

      fine_currency: fine_currency,
      who_pays: who_pays,
      fine_reason: fine_reason,
      fine_amount: fine_amount,
    };


    let { error } = await this.action(`edit_${part}`, params);

    Object.values(this.execution).forEach((item) => {
      if (item && item.status) {
        item.status = undefined;
      }
    });
    if (error !== undefined) {
      if (Array.isArray(error.data)) {
        this.setError(this.execution, error.data);
      }
      Vue.Dialog.MessageBox.Error(error);
      return false;
    }
    await this.update_context();
    return true;
  }

  async put_contract_body(data) {
    const { error } = await this.action('put_contract_body', data);
    if (!error) {
      this.contract_body = data;
    } else {
      Vue.Dialog.MessageBox.Error(error);
    }
    return {error}
  }

  async edit_banking_details(type, value) {
    if (value)
      value = {
        bank_account: value.bank_account,
        bank_account_type_id: value.bank_account_type_id,
        bank_mfo_code: value.bank_mfo_code,
        bank_name: value.bank_name,
        id: value.id,
        name: value.name,
      };

    let method = type ? `edit_${type}_banking_details` : 'edit_banking_details'

    const { error } = await this.action(method, value);
    if (error) {
      Vue.Dialog.MessageBox.Error(error);
      return;
    }
    if (!type)
      return;

    if (!this[`${type}_banking_details`]) {
      await this.update_rights();
    }
    this[`${type}_banking_details`] = value;
  }

  get public_contract() {
    if (this.customBody.validate(false))
      return;

    return async () => {
      if (!this.contract_body) {
        return await Vue.Dialog.MessageBox.Error(Language.t("contract.attach_file"));
      }

      let spec_items = this.customBody.properties.spec.dataSource.items || [];

      let country_empty = spec_items.filter((item) => {
        return !((item.status & 4) != 0) && !item.country
      })
      if (country_empty.length > 0) {

        country_empty.forEach((items) => {
          items.status |= 8
        })
        return await Vue.Dialog.MessageBox.Error(Language.t("error_check_country"));
      }

      if (!!await this.customBody.save(null, false)) {
        await this.publish()
      }
    }
  }

  get org_sign_contract() {
    if (this.customBody.validate(false))
      return;

    return async () => {
      if (!this.contract_body) {
        return await Vue.Dialog.MessageBox.Error(Language.t("contract.attach_file"));
      }

      let spec_items = this.customBody.properties.spec.dataSource.items || [];

      let country_empty = spec_items.filter((item) => {
        return !((item.status & 4) != 0) && !item.country
      })
      if (country_empty.length > 0) {

        country_empty.forEach((items) => {
          items.status |= 8
        })
        return await Vue.Dialog.MessageBox.Error(Language.t("error_check_country"));
      }

      if (!!await this.customBody.save(null, false)) {
        await this.org_sign()
      }
    }
  }

  async remove_contract() {
    await this.delete()
  }

  async remove_additional_contract(self) {
    let sign;
    if ((this.proc_type == "schedule_exchange_contract") && !(sign = await this.subscribe('contract.question_remove_additional'))) {
      return;
    }

    const { error, data } = await this.action('remove_additional_contract', { sign: sign });
    if (error !== undefined) {
      return Vue.Dialog.MessageBox.Error(error);
    }
    const { number } = this;
    await this.update_context();
    let contractNumber = number.split('.').slice(0, -1);
    contractNumber.push('1');
    contractNumber = data;
    const constractUrl = self.$route.fullPath.split(number).join(contractNumber);
    self.$router.push({
      path: constractUrl,
    });
  }

  async update_rights(rights) {
    if (!rights) {
      const { error, data } = await this.action('get_rights');
      if (!error && data) {
        rights = data
      }
    }
    if (rights) {
      for (let name in rights) {
        if (rights[name] == 'grant' || rights[name] == true) {
          rights[name] = true;
        } else {
          rights[name] = false;
        }
      }
    }
    this.rights = rights || {};

    this.actions = Object.keys(this.rights).filter((key) => {
      return this.rights[key] && key.indexOf('__') == 0
    }).map((key) => {
      return {
        name: key.replace(/^__/gi, ''),
        method: key
      }
    })
    this.update_rights_key++;
  }


  async org_confirm_income() {
    if (await Vue.Dialog.MessageBox.Question(Language.t("org_confirm_income_question")) != Vue.Dialog.MessageBox.Result.Yes) {
      return;
    }
    let result = await this.action('org_confirm_income');
    if (result.error) {
      return Vue.Dialog.MessageBox.Error(result.error);
    }
    await this.update_context();
  }


  async accept_delivery_timeout() {
    let question = "accept_delivery_timeout_question_full"
    if (this.is_gos && this.proc_type == "reduction") {
      question = "accept_delivery_timeout_question"
    }

    let sign;
    if (!(sign = await this.subscribe(question)))
      return;

    let { error, data } = await this.action('accept_delivery_timeout', {
      sign: sign
    });
    if (error) {
      return Vue.Dialog.MessageBox.Error(error);
    }
    await this.update_context();
  }


  async confirm_income_timeout_expired() {
    if (await Vue.Dialog.MessageBox.Question(Language.t("confirm_income_timeout_expired_question")) != Vue.Dialog.MessageBox.Result.Yes) return;
    let result = await this.action('confirm_income_timeout_expired', {}, "with_ecp");
    if (!result) return;
    if (result.error) return Vue.Dialog.MessageBox.Error(result.error);
    await this.update_context();
  }

  async reject_without_fine_from_org() {
    if (await Vue.Dialog.MessageBox.Question(Language.t("contract.question_reject_without_fine_from_org")) != Vue.Dialog.MessageBox.Result.Yes) return;
    let result = await this.action('reject_without_fine_from_org', {}, "with_ecp");
    if (!result) return;
    if (result.error) return Vue.Dialog.MessageBox.Error(result.error);
    await this.update_context();
  }

  async return_on_execution() {
    if (await Vue.Dialog.MessageBox.Question(Language.t("contract.question_return_on_execution")) != Vue.Dialog.MessageBox.Result.Yes) return;
    let result = await this.action('return_on_execution', {}, "with_ecp");
    if (!result) return;
    if (result.error) return Vue.Dialog.MessageBox.Error(result.error);
    await this.update_context();
  }

  async pause_contract() {
    if (await Vue.Dialog.MessageBox.Question(Language.t("contract.question_pause_contract")) != Vue.Dialog.MessageBox.Result.Yes) return;
    let result = await this.action('pause_contract', {}, "with_ecp");
    if (!result) return;
    if (result.error) return Vue.Dialog.MessageBox.Error(result.error);
    await this.update_context();
  }

  async reject_without_fine() {
    let question = this.rights.confirm_income_timeout_expired ? "confirm_income_timeout_expired_question" : "confirm_reject_without_fine_question"
    if (await Vue.Dialog.MessageBox.Question(Language.t(question)) != Vue.Dialog.MessageBox.Result.Yes) return;
    let result = await this.action('reject_without_fine', {}, "with_ecp");
    if (!result) return;
    if (result.error) return Vue.Dialog.MessageBox.Error(result.error);
    await this.update_context();
  }

  async rejected_timeout(part) {
    let action = part == 'org' ? 'org_rejected_winner_timeout' : 'winner_rejected_org_timeout'

    let sign;
    if (!(sign = await this.subscribe('contract.question_rejected_timeout')))
      return;

    let { error, data } = await this.action(action, {
      sign: sign
    });

    if (error) {
      return Vue.Dialog.MessageBox.Error(error);
    }
    await this.update_context();
  }

  async create_new_version(part) {
    let { error, data } = await this.action(`create_new_version_${part}`);

    if (error) {
      Vue.Dialog.MessageBox.Error(error);
      return
    }
    return data?.number || data;
  }

  async reassign_trader() {
    let { error, data } = await this.action(`reassign_trader`);

    if (error) {
      Vue.Dialog.MessageBox.Error(error);
      return
    }
    return data?.number || data;
  }

  // part не используется, в будущем может пригодиться
  async ac_reject(part) {
    let { error, data } = await this.action(`ac_reject`);

    if (error) {
      Vue.Dialog.MessageBox.Error(error);
      return
    }
    return data?.number || data;
  }

  async cancellation() {
    let result = await this.action(`cancellation`);

    if (result.error && result.error.code != 'AbortError') {
      return Vue.Dialog.MessageBox.Error(result.error);
    }
    await this.update_context();
  }

  async publish(question = 'contract.question_sign') {

    let sign;
    if (!(sign = await this.subscribe(question)))
      return;

    let { error, data } = await this.action(`publish`, {
      sign: sign
    });

    if (error) {
      return Vue.Dialog.MessageBox.Error(error);
    }
    await this.update_context();
  }

  async delete(question = Language.t('question_delete_proc')) {

    if (await Vue.Dialog.MessageBox.Question(question) != Vue.Dialog.MessageBox.Result.Yes) {
      return;
    }

    let result = await this.action(`delete`, {

    });

    if (result.error) {
      Vue.Dialog.MessageBox.Error(result.error);
      return;
    }
    Vue.Dialog.MessageBox.Success("Договор удален");
    return true;
    //await this.update_context();
  }



  async sign(part, question) {
    return this.sign_action(`${part}_sign`, question);
  }

  async ac_sign(part, question = 'question.call.action') {
    return this.sign_action(`ac_sign_${part}`, question);
  }


  async execution_reject() {

    let sign;
    if (!(sign = await this.subscribe('contract.execution_reject')))
      return;

    let { error } = await this.action(`execution_reject`, {
      sign: sign
    });
    if (error) {
      return Vue.Dialog.MessageBox.Error(error);
    }
    await this.update_context();
  }

  async reject_sign(part) {
    if (await Vue.Dialog.MessageBox.Question(Language.t('contract.question_reject_sign')) != Vue.Dialog.MessageBox.Result.Yes) {
      return;
    }

    let { error } = await this.action(`${part}_reject_sign`);
    if (error) {
      return Vue.Dialog.MessageBox.Error(error);
    }
    await this.update_context();
  }

  async reject(part) {
    const response = await Dialog.Modal({
      model: this,
      title: Language.t('contract.reject_reason'),
      method: `${part}_reject`,
      fields: [{
        name: 'reason',
        type: 'text',
        required: true,
        label: 'comment',
        status: undefined,
        value: undefined,
      }],
      button: Language.t('contract.reject_dlg_button'),
      close: Language.t('contract.return'),
    });
    if (!response) {
      return;
    }

    //const { error } = await this.action(`${part}_reject`, params);
    //if (error) {
    //  return Vue.Dialog.MessageBox.Error(error);
    //}
    await this.update_context();
  }

  async coorp_publish(method = 'coorp_publish') {
    let { error } = await this.action(method);
    if (error) {
      return Vue.Dialog.MessageBox.Error(error);
    }
    await this.update_context();
  }

  async develop(method, show_dlg = false) {
    let params = show_dlg ? await Vue.Dialog({
      props: ["title"],
      data: function () {
        return {
          json: undefined
        }
      },
      methods: {
        send() {
          try {
            let result = this.json;
            if (result)
              result = JSON.parse(this.json)
            else
              result = {}
            this.Close(result)
          } catch (e) {
            Vue.Dialog.MessageBox.Error(e)
          }

        }
      },
      template: `
        <div>
          <header>{{title}}</header>
          <main>
            <ui-text label='Введите json' v-model='json'/>
          </main>
          <footer>
            <ui-btn type='secondary' v-on:click.native='Close()'>{{$t('cancel')}}</ui-btn>
            <ui-btn type='primary' v-on:click.native='send'>{{$t("send")}}</ui-btn>
          </footer>
        </div>
      `
    }).Modal({
      title: method
    }) : {}

    if (!params)
      return;

    let { error } = await this.action(method, params);
    if (error) {
      if (error.code != 'AbortError')
        Vue.Dialog.MessageBox.Error(error);
      return
    }
    if (method == 'delete_contract') {
      Vue.Dialog.MessageBox.Success("Договор удален");
      return true;
    }
    await this.update_context();
  }

  async subscribe(question) {

    if (question && (await Vue.Dialog.MessageBox.Question(Language.t(question)) != Vue.Dialog.MessageBox.Result.Yes)) {
      return;
    }

    let result = await this.action('get_digest_hash');
    if (result && result.data) {
      result = await ecp.subscribe(result.data);
    }
    if (result && result.error)
      Vue.Dialog.MessageBox.Error(result.error);

    return result?.data;
  }

  async action(name, params, with_ecp) {
    let sign = undefined;
    if (with_ecp) {
      let result = await this.action('get_digest_hash');
      if (result.error) return result;

      result = await ecp.subscribe(result.data);

      if (!result) return;
      if (result.error) return result;

      sign = result.data;
    }

    if (sign) {
      return Contract.Action(name, this.number, { ...params, sign: sign })
    } else {
      return Contract.Action(name, this.number, params)
    }
  }

  static async Action(name, number, params) {
    return await Http.api.rpc('contract_action', {
      action: name,
      number,
      params,
    });
  }

  @Action("contract.get")
  static async get(id) {
    let { error, data } = await Http.api.rpc('contract_detailed', {
      number: id,
    });
    if (error) {
      return {
        error,
      };
    }

    const response = await Contract.Action('get_rights', id);
    if (response.error) {
      return {
        error: response.error,
      };
    }

    data.rights = response.data;
    data = new Contract(data);

    return {
      data,
    };
  }

  static async develop() {
    if (!this._DevelopPromise) {
      /*this._DevelopPromise = new Promise(async (resolve, reject) => {
        const { error, data } = await Http.api.rpc('contract_ref', {
          ref: '__contract_schema',
          op: 'read',
        });
        resolve({ error, data });
      })*/
      this._DevelopPromise = Http.api.rpc('contract_ref', {
        ref: '__contract_schema',
        op: 'read',
      });
    }
    return this._DevelopPromise;
  }

  @Action("contract.create")
  static async create(params = {}) {
    return await Http.api.rpc("contract_create", params)
  }

}
