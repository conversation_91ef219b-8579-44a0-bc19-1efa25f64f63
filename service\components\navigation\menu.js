import { Context, Config, <PERSON><PERSON><PERSON>, Settings } from '@iac/kernel'
import ExplorerWorkspace from './explorer'

class ServiceMenu {

    constructor() {
        this._items = undefined;

        Context.Access.onUpdate.bind(() => {
            this._items = undefined;
        })

        Settings.onUpdate.bind(() => {
            this._items = undefined;
        })

        Develop.onChangeProperty.bind(() => {
            this._items = undefined;
        })

        this._custom_items_fn = [];

    }

    addItems(fn) {
        this._custom_items_fn.push(fn)
    }

    get source() {
        let profile_group = ''
        let processes_group = ''
        let broker_group = ''

        let sysop_group = ''
        if (Develop.nav_menu_develop) {
            profile_group = 'company/'
            processes_group = 'goszakupki/'
            sysop_group = 'admin/'
            broker_group = 'birja/'
        }

        return [
            {
                group: `${profile_group}`,
                label: 'nav.notification',
                path: '/workspace/notification',
                hidden: () => !Context.User.id,
            },
            {
                group: `${profile_group}`,
                label: 'nav.complaints',
                path: '/workspace/complaints',
                hidden: () => {
                    return (!Context.User.id || !Settings.complaints || !Settings.ecp)
                },
            },
            {
                group: `${profile_group}nav.company`,
                label: 'nav.company_requisites',
                path: '/workspace/company_requisites',
                policy: [
                    "company_edit",
                    "company_view_own_requisites"
                ]
            },
            {
                group: `${profile_group}nav.company`,
                label: 'nav.members',
                path: '/workspace/user',
                policy: [
                    "user_accept_participation_request",
                    "user_change_policy",
                    "user_change_role",
                    "user_clear_role",
                    "user_list",
                    "company_invite_user"
                ]
            },
            {
                group: `${profile_group}nav.company`,
                label: 'nav.affiliation',
                path: '/workspace/affiliation',
                hidden: ()=>{
                    return Settings._country == 'KG'
                },
                policy: [
                    "affiliation_crud"
                ]
            },
            {
                group: `${profile_group}nav.company`,
                label: 'nav.billing',
                path: '/workspace/billing',
                hidden: ()=>{
                    return Settings._country == 'KG'
                },
                policy: [
                    "balance_view",
                    "balance_view_exchange"
                ]
            },
            {
                group: `${profile_group}nav.company`,
                label: 'nav.drivers',
                path: '/workspace/drivers',
                hidden: ()=>{
                    return !Settings.exchange || Settings.exchange._blocked
                },
                policy: [
                    "company_edit",
                    "company_drivers_edit",
                    "company_vehicles_edit",
                    "logistic_stocks_crud"
                ]
            },
            {
                group: `${profile_group}nav.company`,
                label: 'nav.permissive_documents',
                path: '/workspace/permissive_documents',
                hidden: ()=>{
                    return !Settings.exchange
                },
                policy: [
                    "company_admin_workspace_pd_show",
                    "company_permissive_documents_edit",
                    "company_trader_pd_goods_list",
                    "company_client_counterterrorist_pd_goods_list",
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.quotation_list',
                path: '/workspace/exchange/quotation_list',
                hidden: ()=>{
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    "exchange_exchange_contract_my_crud",
                    "exchange_exchange_contract__crud",
                    "exchange_exchange_contract_company_crud",
                    "exchange_exchange_contract_vote",
                    "exchange_exchange_contract_moderate",
                    "exchange_exchange_exposition_my_crud"
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.exposition_list',
                path: '/workspace/exchange/exposition_list',
                hidden: ()=>{
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    "exchange_exchange_exposition_my_crud",
                    "exchange_exchange_exposition_observe",
                    "exchange_scheduled_exchange_contract_bet"
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.commission_list',
                path: '/workspace/exchange/commission_list',
                hidden: ()=>{
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    "exchange_exchange_contract_moderate"
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.exchange.admin',
                path: '/workspace/exchange/admin',
                hidden: ()=>{
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    "exchange_broker_admin_panel",
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'contract.reassign_trader',
                path: '/workspace/exchange/reassign',
                hidden: ()=>{
                    return !Settings.exchange  || Settings.exchange._blocked || !Settings.exchange._reassign
                },
                policy: [
                    "exchange_contract_reassign_trader"
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.brokers_list',
                path: '/workspace/exchange/brokers_list',
                hidden: ()=>{
                    // Не показывать эту страницу тем кто не является админом БИРЖЕВОЙ компании и следовательно
                    // не имеет пермит `exchange.broker.broker_list_panel`
                    return !Settings.exchange || Settings.exchange._blocked;
                },
                policy: [
                    "exchange_broker_broker_list_panel",
                    "exchange_client_broker_list_panel",
                    // TODO
                    // Подумать как привязаться на политику "член биржи"
                    // делать такую политику не планируется, членство опредеделяется
                    // по наличию разрешительного документами.
                    // Но возможно прийдется все таки завести... Пока не уверен как поступить
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.broker_requests_list',
                path: '/workspace/exchange/broker_requests_list',
                hidden: ()=>{
                    return !Settings.exchange || Settings.exchange._blocked;
                },
                policy: [
                    "exchange_client_broker_list_panel",
                    "exchange_broker_broker_list_panel",
                    // TODO
                    // Подумать как привязаться на роль "брокер"
                ]
            },
            {
                group: `${profile_group}nav.company`,
                label: 'nav.settings',
                path: '/workspace/company_settings',
                hidden: ()=>{
                    return Settings._country == 'KG'
                },
                policy: [
                    "company_settings_edit",
                    "company_settings_read",
                ]
            },
            {
                group: `${broker_group}`,
                label: 'terminal',
                path: '/workspace/terminal',
                hidden: ()=>{
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    "exchange_trader_view_terminal",
                ]

            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.burse_product_request',
                path: '/workspace/exchange/burse_product_request',
                hidden: ()=>{
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    "exchange_burse_product_request",
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.issuing_schedule',
                path: '/workspace/exchange/issuing_schedule',
                hidden: ()=>{
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    "system_pgv_create",
                    ...["read"].map((item)=>{
                        return `exc_pgv_${item}`
                    })
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.shipment_schedule',
                path: '/workspace/exchange/shipment_schedule',
                hidden: ()=>{
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    ...["read"].map((item)=>{
                        return `exc_pgo_${item}`
                    })
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'arbitration_complaints',
                path: '/workspace/exchange/arbitration_complaint_list',
                hidden: () => {
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    ...["list", "create","delete","edit","moderate","postpone","reject","return","review","revoke","schedule_meeting","vote"].map((item)=>{
                        return `exchange_arbitration_complaint_${item}`
                    })
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.arbitration_commission',
                path: '/workspace/exchange/commission_arbitration',
                hidden: () => {
                    return !Settings.exchange  || Settings.exchange._blocked
                },
                policy: [
                    "exchange_arbitration_commission_moderate"
                ]
            },
            {
                group: `${profile_group}nav.exchange`,
                label: 'nav.black_list',
                path: '/workspace/exchange/black_list',
                hidden: ()=>{
                    return !Settings.exchange || !Context.User.id  || Settings.exchange._blocked
                },
                policy: [
                    "blacklist_read",
                ]
            },
            {
                group: `${processes_group}`,
                label: 'nav.reports',
                path: '/workspace/report',
                policy: [
                    "report_list"
                ]
            },
            {
                group: `${processes_group}`,
                label: 'nav.purchase',
                path: '/workspace/purchase',
                policy: [
                    "schedule_create",
                    "schedule_sign",
                    "schedule_list"
                ]
            },
            {
                group: `${processes_group}`,
                label: 'nav.claim',
                path: '/workspace/claim',
                hidden: ()=>{
                    return Settings._country == 'KG'
                },
                policy: [
                    "isugf_schedule_list",
                    "isugf_schedule_read",
                    "isugf_schedule_create",
                    "isugf_schedule_sign"
                ]
            },
            {
                group: `${processes_group}nav.agreements`,
                label: 'nav.agreements_procedures',
                path: '/workspace/agreement/procedure',
                policy: [
                    "agreement_list_procedures",
                ]
            },
            {
                group: `${processes_group}nav.agreements`,
                label: 'nav.templates',
                path: '/workspace/agreement/template',
                policy: [
                    "agreement_list_templates",
                    "agreement_create_template"
                ]
            },
            {
                group: `${processes_group}`,
                label: 'nav.contracts',
                path: '/workspace/contract',
                policy: [
                    "contract_list",
                    "contract_create"
                ]
            },
            {
                group: `${processes_group}master_agreements`,
                label: 'my_procedures',
                path: '/workspace/master_agreement',
                hidden: () => {
                    return !(Settings.procedures?.master_agreement)
                },
                policy: [
                    "master_agreement_list_own",
                    "master_agreement_list_participant",
                ]
            },
            {
                group: `${processes_group}master_agreements`,
                label: 'requests',
                path: '/workspace/ma_request',
                hidden: () => {
                    return !(Settings.procedures?.master_agreement)
                },
                policy: [
                    "master_agreement_list_own",
                    "master_agreement_list_participant",
                ]
            },
            {
                group: `${processes_group}nav.procedures`,
                label: 'Закупочная документация',
                path: '/workspace/documentation',
                hidden: () => {
                    return !(Settings.procedures?._documentation || Develop.procedures_documentation_develop)
                },
                policy: [
                    "company_buyer_admin_cm_dp_list_types",
                    "company_buyer_admin_cm_dp_add",
                    "company_buyer_admin_cm_dp_create",
                    "company_buyer_admin_cm_dp_update",
                    "company_buyer_admin_cm_dp_public"

                ]
            },
            {
                group: `${processes_group}nav.procedures`,
                label: 'nav.tenders',
                path: '/workspace/tender',
                hidden: () => !Settings.procedures?.tender,
                policy: [
                    "tender_list_own",
                    "tender_list_participant",
                    "tender_create",
                    "tender_offer_create"
                ]
            },
            {
                group: `${processes_group}nav.procedures`,
                label: 'selections',
                path: '/workspace/selection',
                policy: [
                    "contest_list_own",
                    "contest_list_participant",
                    "contest_create",
                    "contest_offer_create",

                    "procedures_selections_view",
                ]
            },
            {
                group: `${processes_group}nav.procedures`,
                label: 'cargo_procedures',
                path: '/workspace/cargo_procedures',
                hidden: ()=>{
                    return !Settings.exchange || !Settings.procedures?.cargo_procedure
                },
                policy: [
                    "cargo_procedure_my_crud",
                    "cargo_procedure_participate"
                ]
            },
            {
                group: `${processes_group}nav.procedures`,
                label: 'reductions',
                path: '/workspace/reduction',
                hidden: () => !Settings.procedures?.auction,
                policy: [
                    "reduction_list_own",
                    "reduction_list_participant",
                    "reduction_create",
                    "reduction_offer_create"
                ]
            },
            {
                group: `${processes_group}nav.procedures`,
                label: 'nav.contests',
                path: '/workspace/contest',
                hidden: () => !Settings.procedures?.contest,
                policy: [
                    "contest_list_own",
                    "contest_list_participant",
                    "contest_create",
                    "contest_offer_create"
                ]
            },
            {
                group: `${processes_group}nav.electronic_shop`,
                label: 'nav.my_ad',
                path: '/workspace/ad',
                hidden: () => !Settings.procedures?.e_shop,
                policy: [
                    "ad_click",
                    "ad_create",
                    "ad_sign",
                    "comm_ad_click",
                    "comm_ad_create",
                    "comm_ad_sign"
                ]
            },
            {
                group: `${processes_group}nav.electronic_shop`,
                label: 'nav.request',
                path: '/workspace/request',
                hidden: () => !Settings.procedures?.e_shop,
                policy: [
                    "request_list_own",
                    "request_list_participant",
                    "comm_request_list_own",
                    "comm_request_list_participant"
                ]
            },
            {
                group: `${processes_group}nav.electronic_shop`,
                label: 'nav.nad_request',
                path: '/workspace/nad_request',
                hidden: () => {
                    let show = Settings.procedures
                        && Settings.procedures.e_shop
                        && Settings.procedures.e_shop.national_shop
                        && (Context.User.id && Context.User.team_id || Context.Access.policy.system_national_shop);

                    return !show;
                },
                policy: [
                    "request_list_own",
                    "request_list_participant",
                    "comm_request_list_own",
                    "comm_request_list_participant",
                ]
            },
            {
                group: `${processes_group}nav.electronic_shop`,
                label: 'nav.cart',
                path: '/workspace/cart',
                hidden: () => !Context.Access.develop["cart_develop"],
                policy: [
                    "request_list_own",
                    "comm_request_list_own",
                ]
            },
            {
                group: `${sysop_group}nav.moderate`,
                label: 'nav.electronic_shop',
                path: '/workspace/moderate/electronic_shop',
                hidden: () => !Settings.procedures?.e_shop,
                policy: [
                    'ad_moderate',
                    'comm_ad_moderate',
                ],
            },
            {
                group: `${sysop_group}nav.moderate`,
                label: 'nav.auction',
                path: '/workspace/moderate/auction',
                hidden: () => !Settings.procedures?.auction,
                policy: [
                    'reduction_moderate',
                ],
            },
            {
                group: `${sysop_group}nav.moderate`,
                label: 'nav.permissive_documents',
                path: '/workspace/moderate/permissive_documents',
                hidden: ()=>{
                    return !Settings.exchange
                },
                policy: [
                    "company_permissive_documents_moderate",
                ]
            },
            {
                group: `${sysop_group}nav.moderate`,
                label: 'debt_registry',
                path: '/workspace/moderate/debt_registry',
                hidden: ()=>{
                   // return !Settings.exchange
                },
                policy: [
                    "debt_registry_moderate",
                ]
            },
            {
                group: `${sysop_group}`,
                label: 'system_errors',
                path: '/error',
                policy: [
                    "error_view"
                ]
            },
            {
                group: `${sysop_group}`,
                label: 'nav.errors',
                path: '/workspace/errors',
                policy: [
                    "system_errors"
                ]
            },
            {
                group: `${sysop_group}`,
                label: 'nav.user_list',
                path: '/workspace/user_list',
                policy: [
                    "system_user_list"
                ]
            },
            {
                group: `${sysop_group}`,
                label: 'nav.company_list',
                path: '/workspace/company_list',
                policy: [
                    "system_company_list",
                    "company_foreign_list"
                ]
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'nav.settings.edit',
                path: '/workspace/settings/edit',
                policy: [
                    'system_cm_settings_view',
                ],
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'nav.settings.content',
                path: '/workspace/settings/content',
                policy: [
                    'system_cm_content_view',
                ],
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'nav.settings.events',
                path: '/workspace/settings/events',
                policy: [
                    'system_page_edit',
                ],
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'nav.settings.calendar',
                path: '/workspace/settings/calendar',
                policy: [
                    'system_page_edit',
                ],
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'nav.bcv',
                path: '/workspace/settings/bcv',
                policy: [
                    "common_bcv_list",
                    "common_bcv_create"
                ]
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'nav.references',
                path: '/workspace/settings/reference',
                hidden: () => {
                    return !Settings.reference_edit
                },
                policy: [
                    "system_dict_managment"
                ]
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'nav.faq',
                path: '/workspace/settings/faq',
                policy: [
                    "system_cm_faq_view"
                ]
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'nav.banners',
                path: '/workspace/settings/banners',
                policy: [
                    "system_cm_banners_view"
                ]
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'user_local_ip_permissions',
                path: '/workspace/settings/local_permissions',
                policy: [
                    "system_user_local_ip_permissions_edit"
                ]
            },
            {
                group: `${sysop_group}nav.settings`,
                label: 'documentation',
                path: '/workspace/settings/doc/procurement',
                policy: [
                    "system_cm_documentation_view",
                ]
            },
            ...this._custom_items_fn.map((fn) => {
                return fn(Develop.nav_menu_develop)
            }).reduce((arr, cur) => {
                arr.push(...cur)
                return arr
            }, []),
            {
                group: `vscode/test`,
                label: 'search',
                component: ExplorerWorkspace,
                hidden: () => {
                    return true;
                },
                policy: [
                    "system_page_edit"
                ]
            }

        ].sort((a, b) => {
            return a.order - b.order;
        })
    }

    get items() {
        if (!this._items) {
            let _items = undefined;

            let view_count = 0;
            _items = this.source.map((item) => {
                item = { ...item }
                item.view = false;

                item.hidden = (typeof item.hidden == 'function') ? item.hidden() : item.hidden;
                if (item.hidden)
                    return item;

                if (!item.policy) {
                    item.view = true;
                    item.disabled = false;
                    view_count += 1;
                    return item;
                }

                for (let index in item.policy) {
                    let policy = item.policy[index];
                    if (Context.Access.policy[policy]) {
                        item.view = true;
                        item.disabled = false;
                        view_count += 1;
                        break;
                    }
                }
                return item;
            })

            if (view_count <= 0) {
                _items = [];
            } else {
                _items = _items.filter((item) => {
                    if (item.hidden)
                        return false;
                    return item.view;
                })
            }
            this._items = _items;
        }
        return this._items;
    }
}

export default new ServiceMenu();
