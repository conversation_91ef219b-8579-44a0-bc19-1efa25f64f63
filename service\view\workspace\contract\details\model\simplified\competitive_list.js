import { Entity } from '@iac/data';
import { Settings } from '@iac/kernel';

export default class SimplifiedContractCompetitiveList extends Entity {
  constructor(context) {
    super(context);

    this.supplier_name = context.supplier_name;
    this.supplier_inn = context.supplier_inn;
    this.price = context.price;
    this.currency = context.currency || Settings._default_currency;
    this.exchange_rate = context.exchange_rate || 1;
    this.price_uzs = context.price_uzs || this.calculatePriceUzs();
  }

  calculatePriceUzs() {
    if (this.currency === 'UZS') {
      return this.price;
    }
    return this.price * this.exchange_rate;
  }

  props() {
    return {
      supplier_name: {
        type: "text",
        label: "Наименование поставщика",
        required: true,
        attr: { react: true }
      },
      supplier_inn: {
        type: "text",
        label: "ИНН организации поставщика",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.supplier_inn) {
            return "Укажите ИНН поставщика";
          }
          // Простая валидация ИНН (9 цифр)
          if (!/^\d{9}$/.test(this.supplier_inn)) {
            return "ИНН должен содержать 9 цифр";
          }
        }
      },
      price: {
        type: "float",
        label: "Цена",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.price || this.price <= 0) {
            return "Укажите цену больше 0";
          }
        },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      currency: {
        type: "entity",
        label: "Валюта",
        dataSource: 'ref_currency',
        required: true,
        attr: { react: true },
        onChange: async () => {
          if (this.currency === 'UZS') {
            this.exchange_rate = 1;
          } else {
            // Здесь можно добавить запрос курса валют
            // Пока используем фиксированный курс
            this.exchange_rate = await this.getExchangeRate(this.currency);
          }
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      exchange_rate: {
        type: "float",
        label: "Курс пересчета",
        readonly: this.currency === 'UZS',
        attr: { react: true },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      price_uzs: {
        type: "float",
        label: "Цена в UZS",
        readonly: true,
        attr: { react: true }
      }
    }
  }

  async getExchangeRate(currency) {
    // Здесь должен быть запрос к API курсов валют
    // Пока возвращаем фиксированные значения
    const rates = {
      'USD': 12500,
      'EUR': 13500,
      'RUB': 135
    };
    return rates[currency] || 1;
  }
}
