import { Entity } from '@iac/data';
import { Http } from '@iac/core';
import { Settings } from '@iac/kernel';

export default class SimplifiedContractCompetitiveList extends Entity {
  // Кэш курсов валют
  static _exchangeRatesCache = null;
  static _cacheTimestamp = null;
  static _cacheTimeout = 5 * 60 * 1000; // 5 минут

  constructor(context) {
    super(context);

    this.supplier_name = context.supplier_name;
    this.supplier_inn = context.supplier_inn;
    this.price = context.price;
    this.currency = context.currency || Settings._default_currency;
    this.exchange_rate = context.exchange_rate || 1;
    this.price_uzs = context.price_uzs || this.calculatePriceUzs();
  }

  calculatePriceUzs() {
    if (this.currency === 'UZS') {
      return this.price;
    }
    return this.price * this.exchange_rate;
  }

  props() {
    return {
      supplier_name: {
        type: "text",
        label: "Наименование поставщика",
        required: true,
        attr: { react: true }
      },
      supplier_inn: {
        type: "text",
        label: "ИНН организации поставщика",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.supplier_inn) {
            return "Укажите ИНН поставщика";
          }
          // Простая валидация ИНН (9 цифр)
          if (!/^\d{9}$/.test(this.supplier_inn)) {
            return "ИНН должен содержать 9 цифр";
          }
        }
      },
      price: {
        type: "float",
        label: "Цена",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.price || this.price <= 0) {
            return "Укажите цену больше 0";
          }
        },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      currency: {
        type: "entity",
        label: "Валюта",
        dataSource: 'ref_currency',
        required: true,
        attr: { react: true },
        onChange: async () => {
          if (this.currency === 'UZS') {
            this.exchange_rate = 1;
          } else {
            // Здесь можно добавить запрос курса валют
            // Пока используем фиксированный курс
            this.exchange_rate = await this.getExchangeRate(this.currency);
          }
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      exchange_rate: {
        type: "float",
        label: "Курс пересчета",
        readonly: this.currency === 'UZS',
        attr: { react: true },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      price_uzs: {
        type: "float",
        label: "Цена в UZS",
        readonly: true,
        attr: { react: true }
      }
    }
  }

  async getExchangeRate(currency) {
    // Проверяем кэш
    const now = Date.now();
    if (SimplifiedContractCompetitiveList._exchangeRatesCache &&
        SimplifiedContractCompetitiveList._cacheTimestamp &&
        (now - SimplifiedContractCompetitiveList._cacheTimestamp) < SimplifiedContractCompetitiveList._cacheTimeout) {

      const cachedRate = SimplifiedContractCompetitiveList._exchangeRatesCache[currency];
      if (cachedRate) {
        return cachedRate;
      }
    }

    try {
      // Запрос к API курсов валют
      const { error, data } = await Http.api.rpc("ref", {
        ref: "ref_exchange_rate",
        op: "read",
        limit: 1
      });

      if (!error && data && data.length > 0) {
        const exchangeData = data[0];
        const rates = exchangeData.rates || {};

        // Сохраняем в кэш
        SimplifiedContractCompetitiveList._exchangeRatesCache = rates;
        SimplifiedContractCompetitiveList._cacheTimestamp = now;

        // Возвращаем курс для указанной валюты
        if (rates[currency]) {
          return rates[currency];
        }
      }
    } catch (err) {
      console.warn('Ошибка получения курса валют:', err);
    }

    // Fallback: фиксированные курсы, если API недоступен
    const fallbackRates = {
      'USD': 12758.36,
      'EUR': 14820.11,
      'RUB': 163.34
    };

    return fallbackRates[currency] || 1;
  }

  // Статический метод для принудительного обновления курсов валют
  static async refreshExchangeRates() {
    try {
      const { error, data } = await Http.api.rpc("ref", {
        ref: "ref_exchange_rate",
        op: "read",
        limit: 1
      });

      if (!error && data && data.length > 0) {
        const exchangeData = data[0];
        const rates = exchangeData.rates || {};

        // Обновляем кэш
        SimplifiedContractCompetitiveList._exchangeRatesCache = rates;
        SimplifiedContractCompetitiveList._cacheTimestamp = Date.now();

        return rates;
      }
    } catch (err) {
      console.warn('Ошибка обновления курсов валют:', err);
    }

    return null;
  }

  // Статический метод для получения всех курсов валют
  static getExchangeRatesCache() {
    return SimplifiedContractCompetitiveList._exchangeRatesCache;
  }
}
