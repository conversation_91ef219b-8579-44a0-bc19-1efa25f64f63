import { Entity } from '@iac/data';
import { Http } from '@iac/core';
import { Settings } from '@iac/kernel';

export default class SimplifiedContractCompetitiveList extends Entity {
  // Кэш курсов валют
  static _exchangeRatesCache = null;
  static _cacheTimestamp = null;
  static _cacheTimeout = 5 * 60 * 1000; // 5 минут

  // Маппинг числовых кодов валют в строковые
  static _currencyCodeMap = {
    '840': 'USD',  // Доллар США
    '978': 'EUR',  // Евро
    '643': 'RUB',  // Российский рубль
    '860': 'UZS',  // Узбекский сум
    '417': 'KGS',  // Киргизский сом
    // Можно добавить другие валюты по необходимости
  };

  constructor(context) {
    super(context);

    console.log('🏗️ Создание SimplifiedContractCompetitiveList с контекстом:', context);

    this.supplier_name = context.supplier_name;
    this.supplier_inn = context.supplier_inn;
    this.price = context.price;
    this.currency = context.currency || Settings._default_currency;
    this.exchange_rate = context.exchange_rate || 1;
    this.price_uzs = context.price_uzs || this.calculatePriceUzs();

    console.log('🏗️ Инициализированы значения:', {
      supplier_name: this.supplier_name,
      supplier_inn: this.supplier_inn,
      price: this.price,
      currency: this.currency,
      exchange_rate: this.exchange_rate,
      price_uzs: this.price_uzs
    });
  }

  // Метод для получения строкового кода валюты
  getCurrencyCode() {
    const rawCode = this.currency?.code || this.currency?.id || this.currency;

    // Если это числовой код, преобразуем в строковый
    const stringCode = SimplifiedContractCompetitiveList._currencyCodeMap[rawCode] || rawCode;

    console.log('🔄 Преобразование кода валюты:', {
      raw: rawCode,
      mapped: stringCode,
      currency: this.currency
    });

    return stringCode;
  }

  calculatePriceUzs() {
    const price = parseFloat(this.price) || 0;
    const rate = parseFloat(this.exchange_rate) || 1;

    // Получаем код валюты из объекта Entity
    const currencyCode = this.getCurrencyCode();

    console.log('🧮 calculatePriceUzs:', {
      currency: this.currency,
      currencyCode: currencyCode,
      price: this.price,
      parsedPrice: price,
      exchange_rate: this.exchange_rate,
      parsedRate: rate
    });

    if (currencyCode === 'UZS') {
      console.log('💱 UZS валюта, возвращаем цену как есть:', price);
      return price;
    }

    const result = price * rate;
    console.log('💱 Расчет:', price, '*', rate, '=', result);
    return result;
  }

  props() {
    return {
      supplier_name: {
        type: "text",
        label: "Наименование поставщика",
        required: true,
        attr: { react: true }
      },
      supplier_inn: {
        type: "text",
        label: "ИНН организации поставщика",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.supplier_inn) {
            return "Укажите ИНН поставщика";
          }
          // Простая валидация ИНН (9 цифр)
          if (!/^\d{9}$/.test(this.supplier_inn)) {
            return "ИНН должен содержать 9 цифр";
          }
        }
      },
      price: {
        type: "float",
        label: "Цена",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.price || this.price <= 0) {
            return "Укажите цену больше 0";
          }
        },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      currency: {
        type: "entity",
        label: "Валюта",
        dataSource: 'ref_currency',
        required: true,
        attr: { react: true },
        onChange: async () => {
          console.log('🔄 Валюта изменена на:', this.currency);

          // Получаем код валюты с преобразованием
          const currencyCode = this.getCurrencyCode();
          console.log('💱 Код валюты:', currencyCode);

          if (currencyCode === 'UZS') {
            console.log('💱 UZS выбран, устанавливаем курс = 1');
            this.exchange_rate = 1;
          } else {
            console.log('💱 Запрашиваем курс для валюты:', currencyCode);
            this.exchange_rate = await this.getExchangeRate(currencyCode);
            console.log('💱 Получен курс:', this.exchange_rate);
          }

          this.price_uzs = this.calculatePriceUzs();
          console.log('💰 Пересчитана цена в UZS:', this.price_uzs);

          // Принудительно обновляем поля формы
          if (this.properties) {
            if (this.properties.exchange_rate) {
              this.properties.exchange_rate._value = this.exchange_rate;
              console.log('🔄 Обновлено поле exchange_rate:', this.exchange_rate);
            }
            if (this.properties.price_uzs) {
              this.properties.price_uzs._value = this.price_uzs;
              console.log('🔄 Обновлено поле price_uzs:', this.price_uzs);
            }
          }
        }
      },
      exchange_rate: {
        type: "float",
        label: "Курс пересчета",
        readonly: (this.currency?.code || this.currency?.id || this.currency) === 'UZS',
        attr: { react: true },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      price_uzs: {
        type: "float",
        label: "Цена в UZS",
        readonly: true,
        attr: { react: true }
      }
    }
  }

  async getExchangeRate(currency) {
    console.log('🔍 getExchangeRate вызван для валюты:', currency);

    // Проверяем кэш
    const now = Date.now();
    if (SimplifiedContractCompetitiveList._exchangeRatesCache &&
        SimplifiedContractCompetitiveList._cacheTimestamp &&
        (now - SimplifiedContractCompetitiveList._cacheTimestamp) < SimplifiedContractCompetitiveList._cacheTimeout) {

      const cachedRate = SimplifiedContractCompetitiveList._exchangeRatesCache[currency];
      console.log('📦 Используем кэшированный курс:', cachedRate, 'для валюты:', currency);
      if (cachedRate) {
        return cachedRate;
      }
    }

    console.log('🌐 Запрашиваем курсы валют через API...');
    try {
      // Запрос к API курсов валют
      const response = await Http.api.rpc("ref", {
        ref: "ref_exchange_rate",
        op: "read",
        limit: 1
      });

      console.log('📡 Ответ API:', response);

      const { error, data, result } = response;

      // Проверяем разные варианты структуры ответа
      // Согласно примеру API, данные в поле result
      const actualData = result || data;

      if (!error && actualData && actualData.length > 0) {
        const exchangeData = actualData[0];
        console.log('💱 Данные курсов:', exchangeData);

        const rates = exchangeData.rates || {};
        console.log('💰 Курсы валют:', rates);

        // Сохраняем в кэш
        SimplifiedContractCompetitiveList._exchangeRatesCache = rates;
        SimplifiedContractCompetitiveList._cacheTimestamp = now;

        // Возвращаем курс для указанной валюты
        if (rates[currency]) {
          console.log('✅ Найден курс для', currency, ':', rates[currency]);
          return rates[currency];
        } else {
          console.log('❌ Курс для валюты', currency, 'не найден в:', rates);
        }
      } else {
        console.log('❌ Некорректный ответ API:', { error, data: actualData });
      }
    } catch (err) {
      console.warn('❌ Ошибка получения курса валют:', err);
    }

    // Fallback: фиксированные курсы, если API недоступен
    const fallbackRates = {
      'USD': 12658.19,
      'EUR': 14672.11,
      'RUB': 161.44
    };

    const fallbackRate = fallbackRates[currency] || 1;
    console.log('🔄 Используем fallback курс:', fallbackRate, 'для валюты:', currency);

    return fallbackRate;
  }

  // Статический метод для принудительного обновления курсов валют
  static async refreshExchangeRates() {
    try {
      const response = await Http.api.rpc("ref", {
        ref: "ref_exchange_rate",
        op: "read",
        limit: 1
      });

      const { error, data, result } = response;
      const actualData = result || data;

      if (!error && actualData && actualData.length > 0) {
        const exchangeData = actualData[0];
        const rates = exchangeData.rates || {};

        // Обновляем кэш
        SimplifiedContractCompetitiveList._exchangeRatesCache = rates;
        SimplifiedContractCompetitiveList._cacheTimestamp = Date.now();

        return rates;
      }
    } catch (err) {
      console.warn('Ошибка обновления курсов валют:', err);
    }

    return null;
  }

  // Статический метод для получения всех курсов валют
  static getExchangeRatesCache() {
    return SimplifiedContractCompetitiveList._exchangeRatesCache;
  }
}
