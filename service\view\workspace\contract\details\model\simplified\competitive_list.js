import { Entity } from '@iac/data';
import { Http } from '@iac/core';
import { Settings } from '@iac/kernel';

export default class SimplifiedContractCompetitiveList extends Entity {


  _exchangeRates = null;

  constructor(context) {
    super(context);

    this.supplier_name = context.supplier_name;
    this.supplier_inn = context.supplier_inn;
    this.price = context.price;
    this.currency = context.currency || Settings._default_currency;
    this.exchange_rate = context.exchange_rate || 1;
    this.price_uzs = context.price_uzs || this.calculatePriceUzs();
  }

  getCurrencyCode() {
    const currencyCode = this.currency?.id || this.currency;
    return currencyCode;
  }

  calculatePriceUzs() {
    const price = parseFloat(this.price) || 0;
    const rate = parseFloat(this.exchange_rate) || 1;
    const currencyCode = this.getCurrencyCode();

    if (currencyCode === 'UZS') {
      return price;
    }
    return price * rate;
  }

  props() {
    return {
      supplier_name: {
        type: "text",
        label: "Наименование поставщика",
        required: true,
        attr: { react: true }
      },
      supplier_inn: {
        type: "text",
        label: "ИНН организации поставщика",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.supplier_inn) {
            return "Укажите ИНН поставщика";
          }
          if (!/^\d{9}$/.test(this.supplier_inn)) {
            return "ИНН должен содержать 9 цифр";
          }
        }
      },
      price: {
        type: "float",
        label: "Цена",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.price || this.price <= 0) {
            return "Укажите цену больше 0";
          }
        },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      currency: {
        type: "entity",
        label: "Валюта",
        dataSource: 'ref_currency',
        required: true,
        attr: { react: true },
        onChange: async () => {
          const currencyCode = this.getCurrencyCode();

          if (currencyCode === 'UZS') {
            this.exchange_rate = 1;
          } else {
            // Теперь метод getExchangeRate работает с кэшем экземпляра
            this.exchange_rate = await this.getExchangeRate(currencyCode);
          }

          this.price_uzs = this.calculatePriceUzs();

          if (this.properties) {
            if (this.properties.exchange_rate) {
              this.properties.exchange_rate._value = this.exchange_rate;
            }
            if (this.properties.price_uzs) {
              this.properties.price_uzs._value = this.price_uzs;
            }
          }
        }
      },
      exchange_rate: {
        type: "float",
        label: "Курс пересчета",
        readonly: this.getCurrencyCode() === 'UZS',
        attr: { react: true },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      price_uzs: {
        type: "float",
        label: "Цена в UZS",
        readonly: true,
        attr: { react: true }
      }
    };
  }

  // ИЗМЕНЕНО: Метод теперь работает с кэшем экземпляра `this._exchangeRates`
  async getExchangeRate(currency) {
    // 1. Проверяем кэш ТЕКУЩЕГО экземпляра. Если данные уже загружены, выходим.
    if (this._exchangeRates && this._exchangeRates[currency]) {
      return this._exchangeRates[currency];
    }
    
    // 2. Если кэш пуст, делаем запрос к API
    try {
      const response = await Http.api.rpc("ref", {
        ref: "ref_exchange_rate",
        op: "read",
        limit: 1
      });

      const { error, data, result } = response;
      const actualData = result || data;

      if (!error && actualData && actualData.length > 0) {
        const rates = actualData[0]?.rates || {};

        // Сохраняем ВСЕ курсы в кэш экземпляра
        this._exchangeRates = rates;

        if (rates[currency]) {
          return rates[currency];
        }
      }
    } catch (err) {
      // Ошибка будет обработана ниже, и сработает fallback
    }

    // 3. Fallback: если API недоступен или не вернул нужную валюту
    const fallbackRates = {
      'USD': 12658.19,
      'EUR': 14672.11,
      'RUB': 161.44,
      'KGS': 666,
    };

    // Сохраняем fallback-курсы в кэш экземпляра, чтобы не повторять запрос к API
    this._exchangeRates = fallbackRates;

    return this._exchangeRates[currency] || 1;
  }

  // УДАЛЕНО: Статические методы для управления общим кэшем больше не нужны
  // static async refreshExchangeRates() { ... }
  // static getExchangeRatesCache() { ... }
}