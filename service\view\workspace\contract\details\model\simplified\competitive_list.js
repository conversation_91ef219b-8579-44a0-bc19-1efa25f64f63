import { Entity } from '@iac/data';
import { Http } from '@iac/core';
import { Settings } from '@iac/kernel';

export default class SimplifiedContractCompetitiveList extends Entity {
  // Кэш курсов валют
  static _exchangeRatesCache = null;
  static _cacheTimestamp = null;
  static _cacheTimeout = 5 * 60 * 1000; // 5 минут

  static _disabledCurrencies = [
    'KGS'   // Киргизский сом отключен
    // 'EUR',
    // 'RUB',
  ];

  constructor(context) {
    super(context);

    this.supplier_name = context.supplier_name;
    this.supplier_inn = context.supplier_inn;
    this.price = context.price;
    this.currency = context.currency || Settings._default_currency;
    this.exchange_rate = context.exchange_rate || 1;
    this.price_uzs = context.price_uzs || this.calculatePriceUzs();
  }

  // Метод для получения строкового кода валюты
  getCurrencyCode() {
    // Согласно API ref_currency_full, строковый код валюты хранится в поле id
    // Структура: {name: "Доллар США", id: "USD", code: "840"}
    const currencyCode = this.currency?.id || this.currency;
    return currencyCode;
  }

  calculatePriceUzs() {
    const price = parseFloat(this.price) || 0;
    const rate = parseFloat(this.exchange_rate) || 1;

    // Получаем код валюты из объекта Entity
    const currencyCode = this.getCurrencyCode();

    if (currencyCode === 'UZS') {
      return price;
    }

    const result = price * rate;
    return result;
  }

  props() {
    return {
      supplier_name: {
        type: "text",
        label: "Наименование поставщика",
        required: true,
        attr: { react: true }
      },
      supplier_inn: {
        type: "text",
        label: "ИНН организации поставщика",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.supplier_inn) {
            return "Укажите ИНН поставщика";
          }
          // Простая валидация ИНН (9 цифр)
          if (!/^\d{9}$/.test(this.supplier_inn)) {
            return "ИНН должен содержать 9 цифр";
          }
        }
      },
      price: {
        type: "float",
        label: "Цена",
        required: true,
        attr: { react: true },
        validate: () => {
          if (!this.price || this.price <= 0) {
            return "Укажите цену больше 0";
          }
        },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      currency: {
        type: "entity",
        label: "Валюта",
        dataSource: 'ref_currency',
        required: true,
        attr: { react: true },
        // Фильтрация валют - исключаем отключенные валюты
        // Для отключения валюты добавьте её код в _disabledCurrencies
        // Для включения валюты удалите её код из _disabledCurrencies
        filter: (item) => {
          const currencyId = item?.id;
          const isDisabled = SimplifiedContractCompetitiveList._disabledCurrencies.includes(currencyId);
          return !isDisabled;
        },
        onChange: async () => {
          // Получаем код валюты с преобразованием
          const currencyCode = this.getCurrencyCode();

          if (currencyCode === 'UZS') {
            this.exchange_rate = 1;
          } else {
            this.exchange_rate = await this.getExchangeRate(currencyCode);
          }

          this.price_uzs = this.calculatePriceUzs();

          // Принудительно обновляем поля формы
          if (this.properties) {
            if (this.properties.exchange_rate) {
              this.properties.exchange_rate._value = this.exchange_rate;
            }
            if (this.properties.price_uzs) {
              this.properties.price_uzs._value = this.price_uzs;
            }
          }
        }
      },
      exchange_rate: {
        type: "float",
        label: "Курс пересчета",
        readonly: this.getCurrencyCode() === 'UZS',
        attr: { react: true },
        onChange: () => {
          this.price_uzs = this.calculatePriceUzs();
        }
      },
      price_uzs: {
        type: "float",
        label: "Цена в UZS",
        readonly: true,
        attr: { react: true }
      }
    }
  }

  async getExchangeRate(currency) {
    // Проверяем кэш
    const now = Date.now();
    if (SimplifiedContractCompetitiveList._exchangeRatesCache &&
        SimplifiedContractCompetitiveList._cacheTimestamp &&
        (now - SimplifiedContractCompetitiveList._cacheTimestamp) < SimplifiedContractCompetitiveList._cacheTimeout) {

      const cachedRate = SimplifiedContractCompetitiveList._exchangeRatesCache[currency];
      if (cachedRate) {
        return cachedRate;
      }
    }

    try {
      // Запрос к API курсов валют
      const response = await Http.api.rpc("ref", {
        ref: "ref_exchange_rate",
        op: "read",
        limit: 1
      });

      const { error, data, result } = response;

      // Проверяем разные варианты структуры ответа
      // Согласно примеру API, данные в поле result
      const actualData = result || data;

      if (!error && actualData && actualData.length > 0) {
        const exchangeData = actualData[0];
        const rates = exchangeData.rates || {};

        // Сохраняем в кэш
        SimplifiedContractCompetitiveList._exchangeRatesCache = rates;
        SimplifiedContractCompetitiveList._cacheTimestamp = now;

        // Возвращаем курс для указанной валюты
        if (rates[currency]) {
          return rates[currency];
        }
      }
    } catch (err) {
      // Ошибка получения курса, будет использован fallback
    }

    // Fallback: фиксированные курсы, если API недоступен
    // Курсы соответствуют актуальным данным из ref_exchange_rate
    const fallbackRates = {
      'USD': 12658.19,  // Доллар США
      'EUR': 14672.11,  // Евро
      'RUB': 161.44     // Российский рубль
      // KGS намеренно исключен, так как отключен
    };

    const fallbackRate = fallbackRates[currency] || 1;
    return fallbackRate;
  }

  // Статический метод для принудительного обновления курсов валют
  static async refreshExchangeRates() {
    try {
      const response = await Http.api.rpc("ref", {
        ref: "ref_exchange_rate",
        op: "read",
        limit: 1
      });

      const { error, data, result } = response;
      const actualData = result || data;

      if (!error && actualData && actualData.length > 0) {
        const exchangeData = actualData[0];
        const rates = exchangeData.rates || {};

        // Обновляем кэш
        SimplifiedContractCompetitiveList._exchangeRatesCache = rates;
        SimplifiedContractCompetitiveList._cacheTimestamp = Date.now();

        return rates;
      }
    } catch (err) {
      // Ошибка обновления курсов валют, возвращаем null
    }

    return null;
  }

  // Статический метод для получения всех курсов валют
  static getExchangeRatesCache() {
    return SimplifiedContractCompetitiveList._exchangeRatesCache;
  }
}