# Управление валютами в конкурентном листе

## Структура данных валют

Согласно API `ref_currency_full`, валюты имеют структуру:

```json
{
  "result": [
    {
      "name": "Узбекский сум",
      "id": "UZS",
      "code": "860"
    },
    {
      "name": "Доллар США", 
      "id": "USD",
      "code": "840"
    },
    {
      "name": "Евро",
      "id": "EUR", 
      "code": "978"
    },
    {
      "name": "Рубль",
      "id": "RUB",
      "code": "643"
    },
    {
      "name": "Кыргызский сом",
      "id": "KGS",
      "code": "417"
    }
  ]
}
```

**Важно**: 
- `id` - строковый код валюты (используется для API курсов)
- `code` - числовой код ISO 4217
- `name` - отображаемое название

## Получение кода валюты

```javascript
getCurrencyCode() {
  // Согласно API ref_currency_full, строковый код валюты хранится в поле id
  // Структура: {name: "Доллар США", id: "USD", code: "840"}
  const currencyCode = this.currency?.id || this.currency;
  
  return currencyCode;
}
```

## Управление доступными валютами

### Отключение валют

Для отключения валюты добавьте её код в массив `_disabledCurrencies`:

```javascript
static _disabledCurrencies = [
  'KGS'  // Киргизский сом отключен
];
```

### Включение валют

Для включения валюты удалите её код из массива `_disabledCurrencies`:

```javascript
static _disabledCurrencies = [
  // 'KGS'  // Закомментируйте для включения
];
```

### Примеры конфигурации

#### Только USD и UZS:
```javascript
static _disabledCurrencies = [
  'EUR',  // Евро отключен
  'RUB',  // Рубль отключен
  'KGS'   // Киргизский сом отключен
];
```

#### Все валюты кроме KGS:
```javascript
static _disabledCurrencies = [
  'KGS'   // Только киргизский сом отключен
];
```

#### Все валюты доступны:
```javascript
static _disabledCurrencies = [
  // Пустой массив - все валюты доступны
];
```

## Фильтрация в интерфейсе

Фильтрация происходит на уровне поля формы:

```javascript
currency: {
  type: "entity",
  label: "Валюта",
  dataSource: 'ref_currency',
  required: true,
  attr: { react: true },
  // Фильтрация валют - исключаем отключенные валюты
  filter: (item) => {
    const currencyId = item?.id;
    const isDisabled = SimplifiedContractCompetitiveList._disabledCurrencies.includes(currencyId);
    return !isDisabled;
  }
}
```

## Поддерживаемые курсы валют

API `ref_exchange_rate` поддерживает следующие валюты:

| Код | Название | Курс (пример) |
|-----|----------|---------------|
| USD | Доллар США | 12,658.19 UZS |
| EUR | Евро | 14,672.11 UZS |
| RUB | Российский рубль | 161.44 UZS |
| UZS | Узбекский сум | 1 UZS (базовая) |

**Примечание**: KGS не поддерживается API курсов, поэтому отключен по умолчанию.

## Fallback курсы

Если API недоступен, используются фиксированные курсы:

```javascript
const fallbackRates = {
  'USD': 12658.19,  // Доллар США
  'EUR': 14672.11,  // Евро
  'RUB': 161.44     // Российский рубль
  // KGS намеренно исключен, так как отключен
};
```

## Добавление новой валюты

### 1. Убедитесь, что валюта есть в ref_currency_full
### 2. Проверьте, поддерживает ли API ref_exchange_rate эту валюту
### 3. Если поддерживает, удалите из _disabledCurrencies
### 4. Если не поддерживает, добавьте fallback курс:

```javascript
const fallbackRates = {
  'USD': 12658.19,
  'EUR': 14672.11,
  'RUB': 161.44,
  'KZT': 27.5      // Добавили казахстанский тенге
};
```

## Отладка фильтрации

При загрузке формы в консоли появятся логи фильтрации:

```
🔍 Фильтр валют: {
  currency: "USD",
  name: "Доллар США", 
  disabled: false,
  show: true
}
🔍 Фильтр валют: {
  currency: "KGS",
  name: "Кыргызский сом",
  disabled: true,
  show: false
}
```

## Текущая конфигурация

### Доступные валюты:
- ✅ **UZS** - Узбекский сум (базовая валюта)
- ✅ **USD** - Доллар США
- ✅ **EUR** - Евро
- ✅ **RUB** - Российский рубль

### Отключенные валюты:
- ❌ **KGS** - Киргизский сом (не поддерживается API курсов)

## Быстрые изменения

### Включить KGS:
```javascript
static _disabledCurrencies = [
  // 'KGS'  // Закомментировать эту строку
];
```

### Отключить EUR:
```javascript
static _disabledCurrencies = [
  'KGS',
  'EUR'   // Добавить эту строку
];
```

### Оставить только USD и UZS:
```javascript
static _disabledCurrencies = [
  'EUR',
  'RUB', 
  'KGS'
];
```

## Результат

После настройки:
- ✅ В выпадающем списке валют отображаются только разрешенные валюты
- ✅ Отключенные валюты не видны пользователю
- ✅ Курсы получаются только для поддерживаемых валют
- ✅ Fallback курсы работают для всех разрешенных валют

Теперь управление валютами стало простым и гибким!
