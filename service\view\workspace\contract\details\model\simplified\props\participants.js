import { DataSource } from '@iac/data';

export function getParticipantsProps(contractBody) {
  return {
    participants_settings: {
      group: "participants",
      type: "setting",
      attr: {
        style: "max-width: 800px;"
      },
    },
    participants: {
      type: "model",
      group: "!participants",
      label: "!",
      fields: {
        initiator: {
          type: "widget",
          readonly: true,
          label: "-contract.organizer",
          widget: {
            name: 'router-link',
            content: contractBody.participants.initiator.company_details.title,
            props: {
              to: `/company/${contractBody.participants.initiator.company_details.id}`,
              style: '-webkit-line-clamp: 2; padding-top: 12px; overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; line-height: 20px;'
            },
          }
        },
        contragent: {
          label: "-company.seller",
          type: "model",
          readonly: !contractBody.contract.rights?.set_contragent_data,
          hidden: () => {
            return !contractBody.base_data.contract_type
          },
          fields: {
            small_business: {
              type: "bool",
              label: "small_business",
            },
            custom_company: {
              type: "bool",
              label: "add_company_manually",
              hidden: () => {
                return (
                  contractBody.readonly
                  || !contractBody.base_data.contract_type
                  || contractBody.base_data.contract_type == 1
                )
              },
            },
            company: {  // субъект государственных закупок
              label: "!company",
              type: "entity",
              required: true,
              hidden: () => {
                return (
                  !contractBody.base_data.contract_type
                  || contractBody.base_data.contract_type == 1
                  || contractBody.participants.contragent?.custom_company
                )
              },
              has_del: true,
              dataSource: new DataSource({
                valueExp: ["id", "title", "inn", "email"],
                search: true,
                displayExp: "title",
                store: {
                  method: "company_ref",
                  ref: "companies",
                  context: (context) => {
                    context.desc = `ИНН: ${context.inn}`
                    return context;
                  },
                }
              })
            },
            custom: {  // Кастомная компания
              type: "model",
              label: "!",
              hidden: () => {
                return (
                  !contractBody.base_data.contract_type
                  || contractBody.base_data.contract_type == 1
                  // || !contractBody.participants.contragent?.custom_company
                )
              },
              fields: {
                company_name: {
                  required: true,
                  label: "-direct_contract_comp_name"
                },
                activity_area_id: {
                  label: "-activity_area_id",
                  type: "entity",
                  dataSource: "ref_area",
                  required: true,
                },
                legal_address: {
                  label: "-legal_address",
                  required: true,
                },
                phone: {
                  label: "-phone",
                  required: true,
                },
                fax: {
                  label: "-fax"
                },
                inn: {
                  label: "-inn",
                  required: true,
                },
                oked_code: {
                  label: "-oked_code",
                  type: 'entity',
                  required: true,
                  dataSource: "ref_economic_acivity_type_"
                },
              }
            },
            email: {
              type: "email",
              label: "-email",
              hidden: () => {
                return (
                  contractBody.base_data.contract_type != 1
                  && !contractBody.participants.contragent?.custom_company
                  && contractBody.participants.contragent?.company?.email
                )
              },
              required: true,
            },
          }
        },
      }
    }
  };
}
