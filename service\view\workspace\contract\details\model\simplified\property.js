import { Property } from '@iac/data';
import { Config } from '@iac/kernel';

export default class SimplifiedContractBodyProperty extends Property {
  constructor(context) {
    super(context)
  }

  get meta() {
    if (this.type == 'file' && this.value) {
      return {
        ...this._meta,
        url: (value) => {
          if (!value.id)
            return;
          return `${Config.api_server}/file/${value.id}`
        },
      }
    }
    return this._meta
  }
}
