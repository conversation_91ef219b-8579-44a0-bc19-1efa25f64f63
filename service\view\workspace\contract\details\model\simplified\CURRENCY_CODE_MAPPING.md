# Исправление маппинга кодов валют

## Найденная проблема

Из логов выяснилось, что валюта передается как **числовой код** `840`, а не как **строковый код** `'USD'`.

### Что показали логи:
```
💱 Код валюты: 840
💰 Курсы валют: {USD: 12658.19, RUB: 161.44, EUR: 14672.11}
❌ Курс для валюты 840 не найден в: {USD: 12658.19, RUB: 161.44, EUR: 14672.11}
```

**Проблема**: API возвращает курсы по строковым кодам (`'USD'`, `'EUR'`, `'RUB'`), а валюта передается как числовой код (`840`, `978`, `643`).

## Решение

### 1. Добавлен маппинг числовых кодов в строковые

```javascript
// Маппинг числовых кодов валют в строковые
static _currencyCodeMap = {
  '840': 'USD',  // Доллар США
  '978': 'EUR',  // Евро
  '643': 'RUB',  // Российский рубль
  '860': 'UZS',  // Узбекский сум
  '417': 'KGS',  // Киргизский сом
};
```

### 2. Добавлен метод преобразования кода валюты

```javascript
getCurrencyCode() {
  const rawCode = this.currency?.code || this.currency?.id || this.currency;
  
  // Если это числовой код, преобразуем в строковый
  const stringCode = SimplifiedContractCompetitiveList._currencyCodeMap[rawCode] || rawCode;
  
  console.log('🔄 Преобразование кода валюты:', {
    raw: rawCode,
    mapped: stringCode,
    currency: this.currency
  });
  
  return stringCode;
}
```

### 3. Обновлены все места использования

- ✅ Обработчик `onChange` валюты
- ✅ Метод `calculatePriceUzs`
- ✅ Свойство `readonly` поля курса

## Международные коды валют (ISO 4217)

| Числовой код | Строковый код | Валюта |
|-------------|---------------|---------|
| 840 | USD | Доллар США |
| 978 | EUR | Евро |
| 643 | RUB | Российский рубль |
| 860 | UZS | Узбекский сум |
| 417 | KGS | Киргизский сом |

## Ожидаемый результат

После исправления логи должны показать:
```
🔄 Валюта изменена на: {code: '840', ...}
🔄 Преобразование кода валюты: {raw: '840', mapped: 'USD', currency: {...}}
💱 Код валюты: USD
💱 Запрашиваем курс для валюты: USD
✅ Найден курс для USD : 12658.19
💱 Получен курс: 12658.19
🔄 Обновлено поле exchange_rate: 12658.19
```

## Тестирование

### 1. Очистите консоль браузера (Ctrl+L)
### 2. Выберите валюту "Доллар США" в форме
### 3. Проверьте логи:
   - Должно появиться "Преобразование кода валюты"
   - `raw: '840'` → `mapped: 'USD'`
   - "Найден курс для USD"
### 4. Поле "Курс пересчета" должно заполниться **12,658.19**

## Поддерживаемые валюты

После исправления будут работать:
- **840 (USD)** → 12,658.19 UZS
- **978 (EUR)** → 14,672.11 UZS
- **643 (RUB)** → 161.44 UZS
- **860 (UZS)** → 1 UZS (базовая валюта)

## Расширение поддержки валют

Для добавления новых валют нужно:

1. **Узнать числовой код** валюты (ISO 4217)
2. **Добавить в маппинг**:
   ```javascript
   static _currencyCodeMap = {
     // ...существующие
     '398': 'KZT',  // Казахстанский тенге
     '156': 'CNY',  // Китайский юань
   };
   ```
3. **Убедиться**, что API поддерживает эту валюту

## Fallback поведение

Если числовой код не найден в маппинге:
- Используется исходный код как есть
- Если API не поддерживает, используется fallback курс 1

## Результат

✅ **Числовые коды преобразуются** в строковые
✅ **API находит курсы** по правильным кодам
✅ **Поля заполняются** актуальными курсами
✅ **Расчет работает** корректно

Теперь при выборе валюты "Доллар США" (код 840) поле "Курс пересчета" должно автоматически заполниться значением **12,658.19**!
